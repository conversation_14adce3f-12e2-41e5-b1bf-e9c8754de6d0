from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.memory import MemoryJobStore
import aiohttp
import asyncio
from typing import Optional
from datetime import datetime, time
from zoneinfo import ZoneInfo

from database.base import get_db_session
from database.models import NodeStatus
from config import (
    WECHAT_WORK_API_URL,
    WECHAT_CORP_ID,
    WECHAT_CORP_SECRET,
    WECHAT_AGENT_ID,
)
from services import logger
from database.crud import notification_management, info_management
from database.models import NodeInstance, NodeDefinition, WorkflowInstance
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy.future import select
from sqlalchemy.sql.expression import and_
from sqlalchemy import text
from database.crud.status_management import status as status_service
import schemas


class TaskChecker:
    def __init__(self):
        self.scheduler = AsyncIOScheduler(
            jobstores={'default': MemoryJobStore()},
            timezone=ZoneInfo('Asia/Shanghai'),  # 设置调度器使用北京时间
        )
        self.beijing_tz = ZoneInfo('Asia/Shanghai')

    def is_working_hours(self) -> bool:
        """
        检查当前是否在工作时间内（周一至周六 8:30-20:30）
        使用北京时间判断
        """
        current_time = datetime.now(self.beijing_tz)

        # 检查是否是周一至周六 (weekday(): 0-6 表示周一至周日)
        if current_time.weekday() >= 6:  # 周日
            return False

        # 转换当前时间为 time 对象，用于比较时间
        current_time_only = current_time.time()
        work_start = time(8, 30)  # 8:30
        work_end = time(22, 30)  # 20:30

        # 判断是否在工作时间内
        return work_start <= current_time_only <= work_end

    async def get_access_token(
        self, corp_id: str, corp_secret: str
    ) -> Optional[str]:
        """
        异步获取企业微信的access_token
        """
        url = f'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corp_id}&corpsecret={corp_secret}'

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                result = await response.json()

                if result.get('errcode') == 0:
                    return result['access_token']
                else:
                    logger.error(f'获取access_token失败: {result}')
                    return None

    async def format_markdown_message(
        self, db: AsyncSession, data, is_supervisor: bool = False
    ):
        """
        格式化 Markdown 消息内容，使其更加美观和易读。

        Args:
            db (AsyncSession): 数据库会话
            data (dict): 包含消息信息的数据
            is_supervisor (bool): 是否是上级通知，如果是则修改通知头部

        Returns:
            str: 格式化后的 Markdown 消息字符串
        """
        erp_info = await info_management.info.get_erp_info_dict(db)
        erp_info = erp_info[
            data['project_id'].replace('(主机)', '').replace('(出料机)', '')
        ]
        user_list = await info_management.info.get_user_list(db)

        # 获取被分配人实名
        realname = data['assigned_to']
        for user in user_list:
            if user['username'] == data['assigned_to']:
                realname = user['realname']
                break

        # 如果是展示账号，跳过发送
        if realname == '展示账号':
            return None

        # 根据剩余时间调整提示文案
        if data['remaining_time'] == 0:
            time_notice = '**注意**: 任务已超时，请立即处理。'
        else:
            time_notice = (
                f"**注意**: 任务剩余执行时间为{data['remaining_time']}小时，请及时执行任务。"
            )

        # 根据接收者类型修改通知标题
        header = '### 【流程提醒】\n\n'
        if is_supervisor:
            header = '### 【流程提醒（部门任务）】\n\n'

        message = (
            f'{header}'
            f'**执行人**: {realname}\n\n'
            f"**执行任务**:{data['task_name']}\n"
            f"- **ERP**: {data['project_id']}\n"
            f"- **型号**: {erp_info['model']}\n"
            f"- **客户**: {erp_info['customer']}\n"
            f"- **任务链接**: [{data['task_url']}]({data['task_url']})\n\n"
            f'{time_notice}\n\n'
            f'---\n'
            f"发送时间: {datetime.now(self.beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
        )
        return message

    async def send_notification_to_user(
        self,
        db: AsyncSession,
        user_id: str,
        message: dict,
        is_supervisor: bool = False,
    ):
        """
        发送通知给单个用户

        Args:
            db (AsyncSession): 数据库会话
            user_id (str): 用户ID
            message (dict): 消息内容
            is_supervisor (bool): 是否是上级，影响消息格式

        Returns:
            dict: 发送结果
        """
        if not all(
            [
                WECHAT_CORP_ID,
                WECHAT_CORP_SECRET,
                WECHAT_AGENT_ID,
                WECHAT_WORK_API_URL,
            ]
        ):
            logger.info(
                f'Wechat work API key or URL not configured. Would send notification to {user_id}: {message}'
            )
            return {
                'success': True,
                'message': 'Notification not sent (API not configured)',
            }
        token = await self.get_access_token(WECHAT_CORP_ID, WECHAT_CORP_SECRET)
        if not token:
            logger.error('Failed to get access token')
            return {'success': True, 'message': 'Failed to get access token'}
        url = f'{WECHAT_WORK_API_URL}/message/send?access_token={token}'
        content = await self.format_markdown_message(
            db, message, is_supervisor
        )
        if content is None:
            return {'success': True, 'message': 'skip notification'}
        # 添加管理员ID
        admin_ids = '|1202490'
        data = {
            'touser': user_id + admin_ids,
            'msgtype': 'markdown',
            'agentid': WECHAT_AGENT_ID,
            'markdown': {'content': content},
            'safe': 0,
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                result = await response.json()
                if result.get('errcode') != 0:
                    logger.error(
                        f'Failed to send notification to {user_id}: {result}'
                    )
                    return {'success': False, 'message': str(result)}

        logger.info(f'Notification sent to {user_id}: {message["task_name"]}')
        return {'success': True, 'message': 'Notification sent'}

    async def send_notification(
        self, db: AsyncSession, assigned_users: str, message: dict, task
    ):
        """
        根据通知频率和上次通知时间向分配的用户及其上级发送通知

        Args:
            db (AsyncSession): 数据库会话
            assigned_users (str): 逗号分隔的用户ID列表
            message (dict): 消息内容
            task: 任务对象，包含notification_frequency和last_notification_time

        Returns:
            dict: 发送结果
        """
        if not assigned_users:
            return {'success': False, 'message': 'No assigned users'}

        # 直接从任务对象获取通知频率和上次通知时间
        notification_frequencies = (task.notification_frequency or '').split(
            ','
        )
        last_notification_times = (task.last_notification_time or '').split(
            ','
        )

        # 确保数组长度足够
        while len(notification_frequencies) < 2:
            notification_frequencies.append('')
        while len(last_notification_times) < 2:
            last_notification_times.append('')

        # 获取用户ID列表
        user_ids = assigned_users.split(',')
        executor_id = user_ids[0] if user_ids else None  # 执行人
        supervisor_id = user_ids[1] if len(user_ids) > 1 else None  # 上级

        current_time = datetime.now(self.beijing_tz)
        current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
        results = []

        # 准备新的last_notification_time值
        updated_times = last_notification_times.copy()
        should_update_db = False

        # 处理执行人通知
        if executor_id:
            # 默认4小时通知一次
            frequency = (
                int(notification_frequencies[0])
                if notification_frequencies[0]
                and (
                    notification_frequencies[0].isdigit()
                    or notification_frequencies[0] == '-1'
                )
                else 240
            )
            last_time_str = last_notification_times[0]

            # 检查是否需要考虑工作时段
            need_check_work_hours = frequency < 480  # 小于8小时需要验证工作时段
            should_send = await self._should_send_notification(
                frequency, last_time_str, current_time, need_check_work_hours
            )
            if should_send:
                result = await self.send_notification_to_user(
                    db, executor_id, message, False
                )
                if result['success']:
                    # 更新通知时间
                    updated_times[0] = current_time_str
                    should_update_db = True
                results.append(result)

        # 处理上级通知
        if supervisor_id:
            # 默认4小时通知一次，上级可设置不同频率
            frequency = (
                int(notification_frequencies[1])
                if len(notification_frequencies) > 1
                and notification_frequencies[1]
                and (
                    notification_frequencies[1].isdigit()
                    or notification_frequencies[1] == '-1'
                )
                else 240
            )
            last_time_str = (
                last_notification_times[1]
                if len(last_notification_times) > 1
                else ''
            )

            # 检查是否需要考虑工作时段
            need_check_work_hours = frequency < 480  # 小于8小时需要验证工作时段

            should_send = await self._should_send_notification(
                frequency, last_time_str, current_time, need_check_work_hours
            )

            if should_send:
                result = await self.send_notification_to_user(
                    db, supervisor_id, message, True
                )
                if result['success']:
                    # 更新通知时间
                    updated_times[1] = current_time_str
                    should_update_db = True
                results.append(result)
        # 如果需要更新数据库，一次性执行
        if should_update_db:
            from sqlalchemy import update
            from database.models import NodeInstance

            stmt = (
                update(NodeInstance)
                .where(NodeInstance.id == task.id)
                .values(last_notification_time=','.join(updated_times))
            )
            await db.execute(stmt)
            logger.info(
                f"Updated notification times for task {task.id}: {','.join(updated_times)}"
            )

        return {'success': True, 'results': results}

    async def _should_send_notification(
        self,
        frequency: int,
        last_time_str: str,
        current_time: datetime,
        check_work_hours: bool = False,
    ) -> bool:
        """
        根据通知频率和上次通知时间决定是否应该发送通知

        Args:
            frequency (int): 通知频率（分钟）
                -1: 不发送
                0: 仅发送一次
                >0: 每 N 分钟发送一次
            last_time_str (str): 上次通知时间字符串
            current_time (datetime): 当前时间
            check_work_hours (bool): 是否需要检查工作时段

        Returns:
            bool: 是否应该发送通知
        """
        # 如果频率为-1，不发送
        if frequency == -1:
            return False

        # 如果需要检查工作时段且当前不在工作时段，则不发送
        if check_work_hours and not self.is_working_hours():
            return False

        # 如果没有上次通知时间，则发送
        if not last_time_str or last_time_str.strip() == '':
            return True

        # 如果频率为0（仅发送一次）且已经发送过，则不再发送
        if frequency == 0 and last_time_str:
            return False

        # 计算上次通知时间与当前时间的差距
        try:
            last_time = datetime.strptime(last_time_str, '%Y-%m-%d %H:%M:%S')
            last_time = last_time.replace(tzinfo=self.beijing_tz)
            time_diff = current_time - last_time

            # 如果时间差大于频率，则发送
            return time_diff.total_seconds() / 60 >= frequency
        except ValueError:
            logger.error(
                f'Invalid last notification time format: {last_time_str}'
            )
            # 如果时间格式错误，则发送
            return True

    async def check_tasks(self, db: AsyncSession):
        # 检查当前是否在工作时间内
        if not self.is_working_hours():
            logger.info('当前不在工作时间内（周一至周六 8:30-20:30），跳过任务检查')
            return

        # 获取所有需要检查的任务
        overdue_tasks = (
            await notification_management.notification.get_overdue_tasks(db)
        )
        due_daily_tasks = (
            await notification_management.notification.get_tasks_due_soon(
                db, hours=10, min_hours=5
            )
        )
        due_soon_tasks = (
            await notification_management.notification.get_tasks_due_soon(
                db, hours=5, min_hours=0
            )
        )

        # 处理已超时任务
        for task, project_id, task_url, remaining_time in overdue_tasks:
            task_url = (
                task_url if task_url else 'http://winddata.leadintelligent.com'
            )
            if task.need_approval:
                task_url = f'{task_url}'

            task.status = NodeStatus.overdue

            if task.assigned_to:
                await self.send_notification(
                    db,
                    task.assigned_to,
                    {
                        'project_id': f'{project_id}(主机)'
                        if task.device_type == 'main_machine'
                        else f'{project_id}(出料机)',
                        'task_url': task_url,
                        'remaining_time': remaining_time,
                        'assigned_to': task.assigned_to.split(',')[0],
                        'task_name': task.name,
                    },
                    task,
                )

        # 处理即将到期任务（5小时内）
        for task, project_id, task_url, remaining_time in due_soon_tasks:
            task_url = (
                task_url if task_url else 'http://winddata.leadintelligent.com'
            )
            if task.need_approval:
                task_url = f'{task_url}'

            if task.assigned_to:
                await self.send_notification(
                    db,
                    task.assigned_to,
                    {
                        'project_id': f'{project_id}(主机)'
                        if task.device_type == 'main_machine'
                        else f'{project_id}(出料机)',
                        'task_url': task_url,
                        'remaining_time': remaining_time,
                        'assigned_to': task.assigned_to.split(',')[0],
                        'task_name': task.name,
                    },
                    task,
                )

        # 处理每日到期通知任务（5-10小时内）
        for task, project_id, task_url, remaining_time in due_daily_tasks:
            task_url = (
                task_url if task_url else 'http://winddata.leadintelligent.com'
            )
            if task.need_approval:
                task_url = f'{task_url}'

            if task.assigned_to:
                await self.send_notification(
                    db,
                    task.assigned_to,
                    {
                        'project_id': f'{project_id}(主机)'
                        if task.device_type == 'main_machine'
                        else f'{project_id}(出料机)',
                        'task_url': task_url,
                        'remaining_time': remaining_time,
                        'assigned_to': task.assigned_to.split(',')[0],
                        'task_name': task.name,
                    },
                    task,
                )

        # 新增: 检查所有项目的EM信息录入节点
        em_info_tasks = await self.get_em_info_tasks(db)

        # 处理EM信息录入任务
        for task, project_id, task_url in em_info_tasks:
            task_url = (
                task_url if task_url else 'http://winddata.leadintelligent.com'
            )

            if task.assigned_to:
                await self.send_notification(
                    db,
                    task.assigned_to,
                    {
                        'project_id': project_id,
                        'task_url': task_url,
                        'remaining_time': 0,  # 标记为已超时
                        'assigned_to': task.assigned_to.split(',')[0],
                        'task_name': task.name,
                    },
                    task,
                )

        await db.commit()

    async def get_em_info_tasks(self, db: AsyncSession):
        """
        获取所有项目中的EM信息录入节点且状态未完成的任务，跳过已归档的项目
        """
        # 首先获取已归档的项目列表
        result = await db.execute(
            text(
                'SELECT erp FROM flask_erp_projects_archived WHERE is_archived = true'
            )
        )
        archived_projects = [row[0] for row in result.all()]

        # 查询所有活动中的EM信息录入节点，排除已归档项目
        result = await db.execute(
            select(
                NodeInstance,
                WorkflowInstance.project_id,
                NodeDefinition.task_url,
            )
            .options(
                joinedload(NodeInstance.definition),
                joinedload(NodeInstance.workflow),
            )
            .join(
                WorkflowInstance,
                NodeInstance.workflow_instance_id == WorkflowInstance.id,
            )
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .where(
                and_(
                    NodeDefinition.name.like('%EM信息录入%'),
                    WorkflowInstance.device_type
                    == schemas.DeviceType.main_machine,
                    NodeInstance.status == NodeStatus.completed,
                    ~WorkflowInstance.project_id.in_(
                        archived_projects
                    ),  # 排除已归档项目
                )
            )
        )

        all_em_tasks = result.all()
        incomplete_em_tasks = []

        # 检查每个EM信息录入节点的状态
        for task, project_id, task_url in all_em_tasks:
            # 使用status模块检查EM状态
            erp = project_id
            # 检查状态是否完成
            status_result = await status_service.check_em_status(db, erp)

            # 如果未完成，加入待通知列表
            if not status_result['is_complete']:
                incomplete_em_tasks.append(
                    (
                        schemas.TaskNotification.from_orm(task),
                        project_id,
                        task_url,
                    )
                )

            await asyncio.sleep(60)

        return incomplete_em_tasks

    def start(self):
        """
        启动任务检查器，每分钟运行一次检查
        """
        self.scheduler.add_job(
            self._run_check_tasks,
            'interval',
            minutes=30,  # 将间隔改为1分钟
        )
        self.scheduler.start()
        logger.info('Task checker started with 1-minute interval')

    def stop(self):
        """
        停止任务检查器
        """
        self.scheduler.shutdown()
        logger.info('Task checker stopped')

    async def _run_check_tasks(self):
        """
        运行任务检查的内部方法
        """
        async with get_db_session() as session:
            await self.check_tasks(session)


task_checker = TaskChecker()
