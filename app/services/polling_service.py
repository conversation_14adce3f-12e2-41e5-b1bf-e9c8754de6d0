# services/polling_service.py
import asyncio
from inspect import signature
from typing import Optional, List, Tuple, Dict, Any
from datetime import datetime, time, timedelta
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from database.models import (
    NodeInstance,
    NodeDefinition,
    NodeStatus,
    NodeType,
    StatusUpdateMode,
    EdgeCondition,
    ChangeLog,
    WorkflowInstance,
)
from database.base import get_db_session
from database.crud.node_status_service import EnhancedNodeStatusService

# from database.crud.approval_management import approval_record  # 不再需要
from services import logger
from database.crud.status_management import status as status_service
from database.crud import workflow_management
from database.crud.info_management import info as info_service

from schemas.base import DeviceType


class PollingService:
    def __init__(self):
        self.is_running: bool = False
        self.polling_interval: int = 600  # 默认轮询间隔(秒)
        self.node_status_service = None
        self.daily_check_time = time(hour=0, minute=0)  # 设置每日检查时间为0:00
        self.last_daily_check: Optional[datetime] = None
        self.last_assignment_sync: Optional[datetime] = None  # 添加最后同步时间

    async def start(self):
        """启动轮询服务"""
        self.is_running = True
        asyncio.create_task(self._polling_loop())
        asyncio.create_task(self._daily_erp_check_loop())
        asyncio.create_task(
            self._daily_node_assignment_sync_loop()
        )  # 启动节点分配同步循环
        logger.info('Polling service started')

    async def stop(self):
        """停止轮询服务"""
        self.is_running = False
        logger.info('Polling service stopped')

    async def _daily_node_assignment_sync_loop(self):
        """每日节点分配同步循环"""
        while self.is_running:
            try:
                current_time = datetime.now().time()
                current_date = datetime.now().date()

                # 只在每日检查时间执行
                if current_time >= self.daily_check_time and (
                    self.last_assignment_sync is None
                    or self.last_assignment_sync.date() < current_date
                ):
                    async with get_db_session() as session:
                        await self._sync_node_assignments(session)
                        self.last_assignment_sync = datetime.now()
                        await session.commit()
            except Exception as e:
                logger.error(
                    f'Error in daily node assignment sync loop: {str(e)}'
                )
                if session:
                    await session.rollback()

            # 等待1分钟后再次检查
            await asyncio.sleep(60)

    async def _sync_node_assignments(self, db: AsyncSession):
        """同步节点分配信息"""
        try:
            # 获取所有非主机类型、没有分配人员的节点实例，同时获取需要的字段
            query = (
                select(
                    NodeInstance,
                    WorkflowInstance.project_id,
                    WorkflowInstance.workflow_definition_id,
                    WorkflowInstance.id.label('workflow_instance_id'),
                    NodeDefinition.name.label('node_definition_name'),
                )
                .join(NodeInstance.workflow)
                .join(NodeInstance.definition)
                .filter(
                    NodeInstance.assigned_to.is_(None),
                    WorkflowInstance.device_type != DeviceType.main_machine,
                    WorkflowInstance.project_id.isnot(None),
                )
            )
            result = await db.execute(query)
            unassigned_data = result.all()

            # 记录更新数量
            update_count = 0

            for (
                node,
                project_id,
                workflow_definition_id,
                workflow_instance_id,
                node_definition_name,
            ) in unassigned_data:
                # 查找对应的主机工作流
                main_workflow_query = select(WorkflowInstance).filter(
                    WorkflowInstance.project_id == project_id,
                    WorkflowInstance.workflow_definition_id
                    == workflow_definition_id,
                    WorkflowInstance.device_type == DeviceType.main_machine,
                )
                main_workflow_result = await db.execute(main_workflow_query)
                main_workflow = main_workflow_result.scalar_one_or_none()

                if not main_workflow:
                    continue

                # 查找主机中同名节点定义的节点实例
                main_node_query = (
                    select(NodeInstance)
                    .join(NodeInstance.definition)
                    .filter(
                        NodeInstance.workflow_instance_id == main_workflow.id,
                        NodeDefinition.name == node_definition_name,
                    )
                )
                main_node_result = await db.execute(main_node_query)
                main_node = main_node_result.scalar_one_or_none()

                if main_node and main_node.assigned_to:
                    # 同步分配信息
                    node.assigned_to = main_node.assigned_to
                    update_count += 1

                    # 记录日志
                    await self._create_change_log(
                        db,
                        workflow_instance_id,
                        f'Node assignment synced from main machine: {main_node.assigned_to}',
                    )

            logger.info(
                f'Node assignment sync completed. Updated {update_count} nodes.'
            )
        except Exception as e:
            import traceback

            traceback.print_exc()
            logger.error(f'Error syncing node assignments: {str(e)}')
            raise

    async def _daily_erp_check_loop(self):
        """每日ERP检查循环"""
        while self.is_running:
            try:
                current_time = datetime.now().time()
                current_date = datetime.now().date()

                # 检查是否需要执行每日任务
                if current_time >= self.daily_check_time and (
                    self.last_daily_check is None
                    or self.last_daily_check.date() < current_date
                ):

                    async with get_db_session() as session:
                        await self._check_and_create_erp_workflows(session)
                        self.last_daily_check = datetime.now()
                        await session.commit()

            except Exception as e:
                logger.error(f'Error in daily ERP check loop: {str(e)}')
                if session:
                    await session.rollback()

            # 等待1分钟后再次检查
            await asyncio.sleep(60)

    async def _check_and_create_erp_workflows(self, db: AsyncSession):
        """检查ERP信息并创建新的工作流"""
        try:
            # 获取前一天的日期字符串
            yesterday = (datetime.now() - timedelta(days=7)).strftime(
                '%Y-%m-%d'
            )
            # 获取最新的ERP信息
            erp_info_dict = await info_service.get_erp_framework_list(
                db, yesterday
            )
            erps = list(set([i['erp'] for i in erp_info_dict]))

            query = select(WorkflowInstance)
            result = await db.execute(query)
            # 获取已存在的工作流实例
            existing_workflows = result.scalars().all()
            existing_project_ids = {wf.project_id for wf in existing_workflows}

            # 检查新的ERP项目
            for erp_id in erps:
                if erp_id not in existing_project_ids and erp_id:
                    try:
                        await workflow_management.workflow_instance.initialize_complete_workflow(
                            db,
                            '卷绕数据管理工作流主流程',
                            project_id=erp_id,
                            device_type=DeviceType.main_machine,
                            creator='system',
                        )
                        # await workflow_management.workflow_instance.initialize_complete_workflow(
                        #     db,
                        #     '卷绕数据管理工作流主流程',
                        #     project_id=erp_id,
                        #     device_type=DeviceType.output_machine,
                        #     creator='system',
                        # )
                        logger.info(
                            f'Created new main workflow for ERP project: {erp_id}'
                        )
                    except Exception as e:
                        logger.error(
                            f'Fail to create project {erp_id} for main machine: {e}'
                        )
            # output_machine = alias(WorkflowInstance)

            # result = await db.execute(
            #     select(WorkflowInstance.project_id.distinct())
            #     .filter(
            #         WorkflowInstance.device_type == DeviceType.main_machine,
            #         WorkflowInstance.project_id.isnot(None),
            #         # 使用别名来构建正确的相关子查询
            #         ~exists(
            #             select(1).where(
            #                 and_(
            #                     output_machine.c.project_id
            #                     == WorkflowInstance.project_id,
            #                     output_machine.c.device_type
            #                     == DeviceType.output_machine,
            #                 )
            #             )
            #         ),
            #     )
            #     .order_by(WorkflowInstance.project_id)
            # )

            # erp_numbers = result.scalars().all()
            # for erp_number in erp_numbers:
            #     part_numbers = await info_service.get_erp_part_numbers(
            #         db, erp_number
            #     )
            #     has_output_machine_flag = has_output_machine(part_numbers)
            #     if has_output_machine_flag:
            #         await workflow_management.workflow_instance.initialize_complete_workflow(
            #             db,
            #             '卷绕数据管理工作流主流程',
            #             project_id=erp_number,
            #             device_type=DeviceType.output_machine,
            #             creator='system',
            #         )
        except Exception as e:
            logger.error(f'Error checking ERP updates: {str(e)}')
            raise

    async def _polling_loop(self):
        """主轮询循环"""
        while self.is_running:
            try:
                async with get_db_session() as session:
                    # 获取所有需要轮询的节点
                    polling_nodes = await self._get_polling_nodes(session)
                    polling_node_ids = [
                        polling_node.id for polling_node in polling_nodes
                    ]

                    # 处理每个需要轮询的节点
                    for polling_node_id in polling_node_ids:
                        try:
                            await self._process_node(session, polling_node_id)
                            await asyncio.sleep(1)
                        except Exception as e:
                            import traceback

                            traceback.print_exc()
                            logger.error(
                                f'Error processing node {polling_node_id}: {str(e)}'
                            )
                            continue

                    await session.commit()

            except Exception as e:
                import traceback

                traceback.print_exc()
                logger.error(f'Error in polling loop: {str(e)}')
                if session:
                    await session.rollback()

            finally:
                status_service.reset()
            # 无论是否发生错误，都等待下一次轮询
            await asyncio.sleep(self.polling_interval)

    async def _get_polling_nodes(self, db: AsyncSession) -> List[NodeInstance]:
        """获取需要轮询的节点"""
        query = (
            select(NodeInstance)
            .join(NodeInstance.definition)
            .options(
                selectinload(NodeInstance.definition),
                selectinload(NodeInstance.workflow),
                selectinload(NodeInstance.outgoing_edges),
                selectinload(NodeInstance.incoming_edges),
            )
            .filter(
                NodeInstance.status == NodeStatus.active,
                NodeDefinition.status_update_mode == StatusUpdateMode.polling,
                NodeDefinition.status_query.isnot(None),
            )
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def _get_node(
        self, db: AsyncSession, node_id: int
    ) -> Optional[NodeInstance]:
        """获取需要轮询的节点"""
        query = (
            select(NodeInstance)
            .join(NodeInstance.definition)
            .options(
                selectinload(NodeInstance.definition),
                selectinload(NodeInstance.workflow),
                selectinload(NodeInstance.outgoing_edges),
                selectinload(NodeInstance.incoming_edges),
            )
            .filter(
                NodeInstance.id == node_id,
            )
        )
        result = await db.execute(query)
        return result.scalars().one_or_none()

    async def _process_node(self, db: AsyncSession, node_id: int):
        """处理单个节点的轮询"""
        try:
            # 获取状态查询结果
            status, result_data = await self._execute_status_query(db, node_id)
            if not status:
                return
            node = await self._get_node(db, node_id)

            if node and node.definition.type == NodeType.decision:
                # 处理决策节点
                await self._handle_decision_node(
                    db, node_id, status, result_data
                )
            else:
                # 处理普通节点
                await self._handle_regular_node(db, node_id, status)

        except Exception as e:
            import traceback

            traceback.print_exc()
            logger.error(f'Error processing node {node_id}: {str(e)}')
            await self._log_error(db, node_id, str(e))

    async def _execute_status_query(
        self, db: AsyncSession, node_id: int
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """执行状态查询SQL，返回状态和额外数据"""
        try:
            node = await self._get_node(db, node_id)
            if not node.definition.status_query:
                return None, None

            status_func = getattr(status_service, node.definition.status_query)
            # 获取函数参数信息
            sig = signature(status_func)
            params = sig.parameters

            # 准备调用参数
            kwargs = {}

            # 如果函数接受 device_type 参数，则添加
            if 'device_type' in params:
                kwargs['device_type'] = node.workflow.device_type
            # if node.workflow.project_id != '37102':
            #     return None, None

            result = await status_func(db, node.workflow.project_id, **kwargs)

            return result['is_complete'], result

        except Exception as e:
            logger.error(
                f'Error executing status query for node {node.id}: {str(e)}'
            )
            return None, None

    async def _handle_decision_node(
        self,
        db: AsyncSession,
        node_id: int,
        status: bool,
        result_data: Optional[Dict[str, Any]],
    ):
        """处理决策节点的轮询结果 - 优化版本，避免与审批服务冲突"""
        from services.node_coordination_service import node_coordinator

        # 检查节点是否被其他服务锁定
        locked_by = await node_coordinator.is_node_locked(node_id)
        if locked_by:
            logger.info(
                f'Node {node_id} is locked by {locked_by}, skipping polling update'
            )
            return

        try:
            # 尝试获取节点锁
            if not await node_coordinator.acquire_node_lock(
                node_id, 'polling', timeout_seconds=10
            ):
                logger.info(
                    f'Failed to acquire lock for node {node_id}, skipping polling update'
                )
                return

            try:
                node = await self._get_node(db, node_id)
                # 根据状态确定决策路径
                decision_result = self._determine_decision_path(
                    status, result_data
                )
                if not decision_result:
                    return

                # 再次检查节点状态，确保仍然是 active
                if node.status != NodeStatus.active:
                    logger.info(
                        f'Node {node_id} status changed to {node.status.value}, skipping polling update'
                    )
                    return

                # 使用轮询服务自己的节点状态服务
                self.node_status_service = EnhancedNodeStatusService(db)
                wf_ins = node.workflow_instance_id

                # 更新节点状态并设置决策结果
                await self.node_status_service.update_node_status(
                    node.id,
                    NodeStatus.completed,
                    decision_result=decision_result,
                )

                # 记录决策日志
                await self._create_change_log(
                    db,
                    wf_ins,
                    f'Decision node completed with result: {decision_result.value}'
                    f', based on status: {status} (by polling)',
                )

            finally:
                # 确保释放锁
                await node_coordinator.release_node_lock(node_id, 'polling')

        except Exception as e:
            # 如果是锁冲突，记录信息但不报错
            if 'Lock wait timeout' in str(
                e
            ) or 'is currently being processed' in str(e):
                logger.info(
                    f'Node {node_id} is being processed by another service, polling skipped'
                )
            else:
                logger.error(
                    f'Error handling decision node {node_id}: {str(e)}'
                )
                try:
                    node = await self._get_node(db, node_id)
                    await self._log_error(db, node_id, str(e))
                except:
                    pass  # 避免在错误处理中再次出错

    def _determine_decision_path(
        self, status: bool, result_data: Optional[Dict[str, Any]]
    ) -> Optional[EdgeCondition]:
        """根据查询结果确定决策路径"""
        if status:

            # 首先检查结果数据中是否直接指定了路径
            if result_data and 'decision' in result_data:
                decision = str(result_data['decision']).lower()
                if decision in ['yes', 'true', '1', True]:
                    return EdgeCondition.yes
                if decision in ['no', 'false', '0', False]:
                    return EdgeCondition.no
                return None

        return None

    async def _handle_regular_node(
        self, db: AsyncSession, node_id: int, status: str
    ):
        """处理普通节点的轮询结果 - 优化版本，避免与审批服务冲突"""
        from services.node_coordination_service import node_coordinator

        # 检查节点是否被其他服务锁定
        locked_by = await node_coordinator.is_node_locked(node_id)
        if locked_by:
            logger.info(
                f'Node {node_id} is locked by {locked_by}, skipping polling update'
            )
            return

        try:
            # 尝试获取节点锁
            if not await node_coordinator.acquire_node_lock(
                node_id, 'polling', timeout_seconds=10
            ):
                logger.info(
                    f'Failed to acquire lock for node {node_id}, skipping polling update'
                )
                return

            try:
                node = await self._get_node(db, node_id)
                new_status = NodeStatus.completed

                if new_status and new_status != node.status:
                    # 再次检查节点状态，确保仍然是 active
                    if node.status != NodeStatus.active:
                        logger.info(
                            f'Node {node_id} status changed to {node.status.value}, skipping polling update'
                        )
                        return

                    # 使用轮询服务自己的节点状态服务
                    self.node_status_service = EnhancedNodeStatusService(db)
                    wf_ins = node.workflow_instance_id

                    # 调用节点状态更新
                    await self.node_status_service.update_node_status(
                        node_id, new_status
                    )

                    await self._create_change_log(
                        db,
                        wf_ins,
                        f'Status updated {node_id} to {new_status.value} by polling',
                    )

            finally:
                # 确保释放锁
                await node_coordinator.release_node_lock(node_id, 'polling')

        except Exception as e:
            # 如果是锁冲突，记录信息但不报错
            if 'Lock wait timeout' in str(
                e
            ) or 'is currently being processed' in str(e):
                logger.info(
                    f'Node {node_id} is being processed by another service, polling skipped'
                )
            else:
                import traceback

                traceback.print_exc()
                logger.error(
                    f'Error handling regular node {node_id}: {str(e)}'
                )
                try:
                    await self._log_error(db, node_id, str(e))
                except:
                    pass  # 避免在错误处理中再次出错

    def _map_status(self, status: str) -> Optional[NodeStatus]:
        """映射外部状态到系统状态"""
        status = status.lower()

        status_mapping = {
            'completed': NodeStatus.completed,
            'complete': NodeStatus.completed,
            'done': NodeStatus.completed,
            'finished': NodeStatus.completed,
            'error': NodeStatus.error,
            'failed': NodeStatus.error,
            'timeout': NodeStatus.overdue,
            'overdue': NodeStatus.overdue,
            'in_progress': NodeStatus.active,
            'processing': NodeStatus.active,
            'pending': NodeStatus.pending,
        }

        return status_mapping.get(status)

    async def _create_change_log(
        self, db: AsyncSession, workflow_instance_id: int, details: str
    ):
        """创建变更日志"""
        log = ChangeLog(
            workflow_instance_id=workflow_instance_id,
            changed_by='polling_service',
            change_type='polling_status_update',
            change_details=details,
            created_at=datetime.now(),
        )
        db.add(log)
        await db.commit()

    async def _log_error(
        self, db: AsyncSession, node_id: int, error_message: str
    ):
        """记录错误日志"""
        node = await self._get_node(db, node_id)
        log = ChangeLog(
            workflow_instance_id=node.workflow_instance_id,
            changed_by='polling_service',
            change_type='polling_error',
            change_details=f'Polling error for node {node_id}: {error_message}',
            created_at=datetime.now(),
        )
        db.add(log)
        await db.commit()


# 创建服务实例
polling_service = PollingService()
