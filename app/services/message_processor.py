from sqlalchemy.future import select
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from redis import asyncio as aioredis
import json
import asyncio
from typing import Optional, Dict, Any

from database.models import (
    WorkflowInstance,
    NodeInstance,
    NodeDefinition,
    NodeStatus,
    ApprovalRecord,
    ChangeLog,
    EdgeCondition,
)
from database.base import get_db_dependency
import schemas
from database.crud.node_status_service import EnhancedNodeStatusService
from database.crud.approval_management import (
    approval_record as approval_record_service,
)
from config import REDIS_URL
from services import logger


class ExternalMessageProcessor:
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        self.is_running: bool = False
        self.db_dependency = get_db_dependency()
        self.node_status_service = None

    async def initialize(self):
        """初始化Redis连接和服务"""
        self.redis = await aioredis.from_url(REDIS_URL)

    async def shutdown(self):
        """关闭服务"""
        if self.redis:
            await self.redis.close()
        self.is_running = False

    async def enqueue_message(self, message: schemas.ExternalTaskMessage):
        """将消息放入队列"""
        await self.redis.lpush(
            'external_task_queue', json.dumps(message.dict())
        )

    async def process_message(
        self, db: AsyncSession, message: schemas.ExternalTaskMessage
    ) -> Dict[str, Any]:
        """处理外部系统消息"""
        try:
            # 1. 查找对应的工作流和节点
            node_instance = await self._find_node_instance(db, message)
            if not node_instance:
                raise ValueError(
                    f"Node '{message.node_name}' not found in workflow {message.workflow_id}"
                )

            # 2. 根据操作类型处理
            result = await self._handle_action(db, message, node_instance)

            # 3. 记录变更日志
            await self._create_change_log(db, message, node_instance)

            await db.commit()
            return result

        except Exception as e:
            await db.rollback()
            logger.error(f'Error processing external message: {str(e)}')
            raise

    async def _find_node_instance(
        self, db: AsyncSession, message: schemas.ExternalTaskMessage
    ) -> Optional[NodeInstance]:
        """查找对应的节点实例，包括子流程"""
        # 先查找所有相关工作流实例（主流程和子流程）
        workflow_query = select(WorkflowInstance).filter(
            WorkflowInstance.project_id == message.project_id,
        )
        workflow_result = await db.execute(workflow_query)
        workflows = workflow_result.scalars().all()

        if not workflows:
            return None

        # 构建所有工作流ID的列表
        workflow_ids = [wf.id for wf in workflows]

        # 查找节点实例
        node_query = (
            select(NodeInstance)
            .join(NodeDefinition)
            .filter(
                and_(
                    NodeInstance.workflow_instance_id.in_(workflow_ids),
                    NodeDefinition.name == message.node_name,
                )
            )
            .order_by(NodeInstance.created_at.desc())  # 获取最新的匹配节点
        )
        node_result = await db.execute(node_query)
        matched_node = node_result.scalar_one_or_none()

        if matched_node:
            return matched_node

        # 如果没有找到，尝试在子流程中查找
        subprocess_query = (
            select(WorkflowInstance)
            .join(
                NodeInstance,
                NodeInstance.subprocess_instance_id == WorkflowInstance.id,
            )
            .filter(NodeInstance.workflow_instance_id.in_(workflow_ids))
        )
        subprocess_result = await db.execute(subprocess_query)
        subworkflows = subprocess_result.scalars().all()

        if not subworkflows:
            return None

        # 在子流程中查找节点
        subprocess_node_query = (
            select(NodeInstance)
            .join(NodeDefinition)
            .filter(
                and_(
                    NodeInstance.workflow_instance_id.in_(
                        [sw.id for sw in subworkflows]
                    ),
                    NodeDefinition.name == message.node_name,
                )
            )
            .order_by(NodeInstance.created_at.desc())
        )
        subprocess_node_result = await db.execute(subprocess_node_query)
        return subprocess_node_result.scalar_one_or_none()

    async def _handle_action(
        self,
        db: AsyncSession,
        message: schemas.ExternalTaskMessage,
        node: NodeInstance,
    ) -> Dict[str, Any]:
        """根据操作类型处理节点状态"""
        self.node_status_service = EnhancedNodeStatusService(db)
        result = {
            'success': True,
            'message': '',
            'node_id': node.id,
            'new_status': None,
        }

        if message.action == schemas.ExternalTaskAction.APPROVE:
            # 创建审批记录
            approval_record = ApprovalRecord(
                node_instance_id=node.id,
                approved_by=message.operator,
                approval_status='approved',
                comments=message.comments,
                created_at=message.timestamp,
            )
            db.add(approval_record)

            # 更新节点状态
            approval_record_service.service.init_node_status_service(db)
            await approval_record_service.service.node_status_service.update_node_status(
                node.id,
                NodeStatus.completed,
                decision_result=EdgeCondition.yes,
            )
            result['message'] = 'Task approved successfully'

        elif message.action == schemas.ExternalTaskAction.REJECT:
            # 创建拒绝记录
            approval_record = ApprovalRecord(
                node_instance_id=node.id,
                approved_by=message.operator,
                approval_status='rejected',
                comments=message.comments,
                created_at=message.timestamp,
            )
            db.add(approval_record)

            # 更新节点状态
            approval_record_service.service.init_node_status_service(db)
            await approval_record_service.service.node_status_service.update_node_status(
                node.id, NodeStatus.error, decision_result=EdgeCondition.no
            )
            result['message'] = 'Task rejected'

        elif message.action == schemas.ExternalTaskAction.COMPLETE:
            approval_record_service.service.init_node_status_service(db)
            await approval_record_service.service.node_status_service.update_node_status(
                node.id, NodeStatus.completed
            )
            result['message'] = 'Task completed'

        elif message.action == schemas.ExternalTaskAction.ROLLBACK:
            # 处理回退逻辑
            await self.node_status_service.reset_branch_status(node.id)
            result['message'] = 'Task rolled back'

        elif message.action == schemas.ExternalTaskAction.REASSIGN:
            if not message.assigned_to:
                raise ValueError('assigned_to is required for reassign action')

            node.assigned_to = message.assigned_to
            result['message'] = f'Task reassigned to {message.assigned_to}'

        result['new_status'] = node.status.value
        return result

    async def _create_change_log(
        self,
        db: AsyncSession,
        message: schemas.ExternalTaskMessage,
        node: NodeInstance,
    ):
        """创建变更日志"""
        log = ChangeLog(
            workflow_instance_id=node.workflow_instance_id,
            changed_by=message.operator,
            change_type=f'external_{message.action.value}',
            change_details=json.dumps(
                {
                    'node_name': message.node_name,
                    'action': message.action.value,
                    'operator_name': message.operator_name,
                    'comments': message.comments,
                    'timestamp': message.timestamp.isoformat(),
                    'extra_data': message.extra_data,
                }
            ),
            created_at=message.timestamp,
        )
        db.add(log)

    async def start_processing(self):
        """启动消息处理服务"""
        self.is_running = True
        await self.initialize()
        asyncio.create_task(self._process_queue())

    async def _process_queue(self):
        """处理消息队列"""
        while self.is_running:
            try:
                # 从队列获取消息
                message_data = await self.redis.rpop('external_task_queue')
                if message_data:
                    message = schemas.ExternalTaskMessage(
                        **json.loads(message_data)
                    )
                    async for db in self.db_dependency:
                        await self.process_message(db, message)
                else:
                    await asyncio.sleep(1)
            except Exception as e:
                logger.error(f'Error in message processing loop: {str(e)}')
                # 可以将失败的消息移到死信队列
                if message_data:
                    await self.redis.lpush('failed_task_queue', message_data)


# 创建服务实例
message_processor = ExternalMessageProcessor()
