"""
节点状态协调服务
用于协调轮询服务和审批服务之间的节点状态更新，避免并发冲突
"""
import asyncio
import time
from typing import Dict, Optional
from datetime import datetime
from services import logger


class NodeCoordinationService:
    """节点状态协调服务，管理节点的并发访问"""

    def __init__(self):
        self._processing_nodes: Dict[int, Dict] = {}  # 正在处理的节点
        self._lock = asyncio.Lock()

    async def acquire_node_lock(
        self, node_id: int, service_name: str, timeout_seconds: int = 30
    ) -> bool:
        """
        尝试获取节点锁

        Args:
            node_id: 节点ID
            service_name: 服务名称 ('approval' 或 'polling')
            timeout_seconds: 超时时间（秒）

        Returns:
            bool: 是否成功获取锁
        """
        async with self._lock:
            current_time = time.time()

            # 检查是否已经被其他服务锁定
            if node_id in self._processing_nodes:
                existing_lock = self._processing_nodes[node_id]

                # 检查锁是否过期
                if current_time - existing_lock['timestamp'] > timeout_seconds:
                    logger.warning(
                        f"Node {node_id} lock expired for service {existing_lock['service']}, "
                        f'releasing and granting to {service_name}'
                    )
                    del self._processing_nodes[node_id]
                else:
                    # 锁仍然有效，拒绝新的请求
                    logger.info(
                        f"Node {node_id} is already locked by {existing_lock['service']}, "
                        f'denying access to {service_name}'
                    )
                    return False

            # 获取锁
            self._processing_nodes[node_id] = {
                'service': service_name,
                'timestamp': current_time,
                'timeout': timeout_seconds,
            }

            logger.info(f'Node {node_id} locked by {service_name}')
            return True

    async def release_node_lock(self, node_id: int, service_name: str):
        """
        释放节点锁

        Args:
            node_id: 节点ID
            service_name: 服务名称
        """
        async with self._lock:
            if node_id in self._processing_nodes:
                existing_lock = self._processing_nodes[node_id]
                if existing_lock['service'] == service_name:
                    del self._processing_nodes[node_id]
                    logger.info(
                        f'Node {node_id} lock released by {service_name}'
                    )
                else:
                    logger.warning(
                        f'Service {service_name} tried to release lock for node {node_id}, '
                        f"but it's owned by {existing_lock['service']}"
                    )

    async def is_node_locked(self, node_id: int) -> Optional[str]:
        """
        检查节点是否被锁定

        Args:
            node_id: 节点ID

        Returns:
            Optional[str]: 如果被锁定，返回锁定的服务名称；否则返回None
        """
        async with self._lock:
            if node_id in self._processing_nodes:
                lock_info = self._processing_nodes[node_id]
                current_time = time.time()

                # 检查锁是否过期
                if (
                    current_time - lock_info['timestamp']
                    > lock_info['timeout']
                ):
                    del self._processing_nodes[node_id]
                    return None

                return lock_info['service']
            return None

    async def cleanup_expired_locks(self):
        """清理过期的锁"""
        async with self._lock:
            current_time = time.time()
            expired_nodes = []

            for node_id, lock_info in self._processing_nodes.items():
                if (
                    current_time - lock_info['timestamp']
                    > lock_info['timeout']
                ):
                    expired_nodes.append(node_id)

            for node_id in expired_nodes:
                lock_info = self._processing_nodes[node_id]
                logger.warning(
                    f'Cleaning up expired lock for node {node_id} '
                    f"from service {lock_info['service']}"
                )
                del self._processing_nodes[node_id]

    async def get_lock_status(self) -> Dict:
        """获取当前锁状态（用于调试）"""
        async with self._lock:
            current_time = time.time()
            status = {}

            for node_id, lock_info in self._processing_nodes.items():
                remaining_time = lock_info['timeout'] - (
                    current_time - lock_info['timestamp']
                )
                status[node_id] = {
                    'service': lock_info['service'],
                    'remaining_seconds': max(0, remaining_time),
                    'locked_at': datetime.fromtimestamp(
                        lock_info['timestamp']
                    ).isoformat(),
                }

            return status


# 全局实例
node_coordinator = NodeCoordinationService()


class NodeLockContext:
    """节点锁上下文管理器"""

    def __init__(
        self, node_id: int, service_name: str, timeout_seconds: int = 30
    ):
        self.node_id = node_id
        self.service_name = service_name
        self.timeout_seconds = timeout_seconds
        self.acquired = False

    async def __aenter__(self):
        self.acquired = await node_coordinator.acquire_node_lock(
            self.node_id, self.service_name, self.timeout_seconds
        )
        if not self.acquired:
            raise RuntimeError(
                f'Failed to acquire lock for node {self.node_id} '
                f'by service {self.service_name}'
            )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.acquired:
            await node_coordinator.release_node_lock(
                self.node_id, self.service_name
            )


# 定期清理过期锁的任务
async def start_lock_cleanup_task():
    """启动锁清理任务"""
    while True:
        try:
            await node_coordinator.cleanup_expired_locks()
            await asyncio.sleep(60)  # 每分钟清理一次
        except Exception as e:
            logger.error(f'Error in lock cleanup task: {e}')
            await asyncio.sleep(60)


# 启动清理任务的函数
def start_cleanup_task():
    """启动锁清理任务，只有在事件循环运行时才启动"""
    try:
        loop = asyncio.get_running_loop()
        loop.create_task(start_lock_cleanup_task())
        logger.info('Node coordination cleanup task started')
    except RuntimeError:
        # 没有运行的事件循环，稍后再启动
        logger.info(
            'No running event loop, cleanup task will be started later'
        )


# 可以在应用启动时调用 start_cleanup_task()
