import asyncio
from datetime import datetime
from typing import List, Set
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import text

from database.models import NodeInstance, WorkflowInstance
from database.base import get_db_session
from services import logger


class ArchivedERPService:
    def __init__(self):
        self.is_running: bool = False
        self.check_interval: int = 300  # 5 minutes in seconds

    async def start(self):
        """启动归档ERP检查服务"""
        self.is_running = True
        asyncio.create_task(self._check_loop())
        logger.info('Archived ERP service started')

    async def stop(self):
        """停止归档ERP检查服务"""
        self.is_running = False
        logger.info('Archived ERP service stopped')

    async def _check_loop(self):
        """主检查循环"""
        while self.is_running:
            try:
                async with get_db_session() as session:
                    # 获取所有已归档的ERP项目
                    archived_erps = await self._get_archived_erps(session)

                    if archived_erps:
                        # 获取所有有项目ID的工作流
                        workflows = await self._get_workflows(session)

                        # 处理每个项目ID在已归档列表中的工作流
                        for workflow in workflows:
                            if (
                                workflow.project_id in archived_erps
                                and workflow.project_id != '37102'
                            ):
                                await self._process_archived_workflow(
                                    session, workflow
                                )

                    await session.commit()

            except Exception as e:
                logger.error(f'Error in archived ERP check loop: {str(e)}')
                if 'session' in locals() and session:
                    await session.rollback()

            # 等待指定时间后再次检查
            await asyncio.sleep(self.check_interval)

    async def _get_archived_erps(self, db: AsyncSession) -> Set[str]:
        """获取所有已归档的ERP项目ID"""
        try:
            # 查询flask_erp_projects_archived表获取已归档的ERP项目
            from sqlalchemy import text

            query = text(
                """
                SELECT erp FROM flask_erp_projects_archived
                WHERE is_archived = true
            """
            )
            result = await db.execute(query)
            return {row[0] for row in result}
        except Exception as e:
            logger.error(f'Error getting archived ERPs: {str(e)}')
            return set()

    async def _get_workflows(self, db: AsyncSession) -> List[WorkflowInstance]:
        """获取所有具有项目ID的工作流实例"""
        query = (
            select(WorkflowInstance)
            .filter(WorkflowInstance.project_id.isnot(None))
            .options(
                selectinload(WorkflowInstance.nodes).selectinload(
                    NodeInstance.definition
                )
            )
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def _process_archived_workflow(
        self, db: AsyncSession, workflow: WorkflowInstance
    ):
        """处理已归档项目的工作流"""
        try:
            # 检查是否需要更新任何节点
            needs_update = False
            for node in workflow.nodes:
                is_debugging_node = await self._is_debugging_node(node)

                if not is_debugging_node and node.status.value != 'completed':
                    needs_update = True
                    break

            # 如果所有节点已经处于正确状态，跳过此工作流
            if not needs_update:
                logger.info(
                    f'Skipping workflow for project: {workflow.project_id} - all nodes already in correct state'
                )
                return

            # 遍历工作流中的所有节点，进行必要的更新
            for node in workflow.nodes:
                # 检查是否是厂内调试节点
                is_debugging_node = await self._is_debugging_node(node)

                if is_debugging_node:
                    # 厂内调试节点，如果不是active且不是completed则设置为active
                    if (
                        node.status.value != 'active'
                        and node.status.value != 'completed'
                    ):
                        await self._update_node_status(db, node, 'active')
                        logger.info(
                            f'Archived ERP debugging node set to active: {node.id} (Project: {workflow.project_id})'
                        )
                else:
                    # 其他节点设置为已完成状态，如果状态不是completed才更新
                    if node.status.value != 'completed':
                        await self._update_node_status(db, node, 'completed')
                        logger.info(
                            f'Archived ERP node completed: {node.id} (Project: {workflow.project_id})'
                        )

            logger.info(
                f'Processed archived workflow for project: {workflow.project_id}'
            )
        except Exception as e:
            logger.error(
                f'Error processing archived workflow {workflow.id}: {str(e)}'
            )
            raise

    async def _is_debugging_node(self, node: NodeInstance) -> bool:
        """判断节点是否是厂内调试节点"""
        # 检查节点名称是否包含"厂内调试"关键词
        if node.definition and node.definition.name:
            return (
                '厂内调试' in node.definition.name
                or '等待装配' in node.definition.name
                or '仿真调试' in node.definition.name
            )
        return False

    async def _update_node_status(
        self, db: AsyncSession, node: NodeInstance, new_status: str
    ) -> bool:
        """使用直接SQL更新节点状态"""
        try:
            update_params = {
                'status': new_status,
                'updated_at': datetime.now(),
            }

            if new_status == 'active':
                # active状态设置开始时间
                if not node.start_time:
                    update_params['start_time'] = datetime.now()

            elif new_status == 'completed':
                # completed状态设置结束时间，如果没有开始时间也设置开始时间
                if not node.start_time:
                    update_params['start_time'] = datetime.now()
                update_params['end_time'] = datetime.now()

            # 构建SQL语句
            set_clauses = [f'{k} = :{k}' for k in update_params.keys()]
            query = text(
                f"""
                UPDATE workflow_node_instances
                SET {', '.join(set_clauses)}
                WHERE id = :node_id
            """
            )

            # 添加节点ID到参数
            params = {**update_params, 'node_id': node.id}

            # 执行更新
            await db.execute(query, params)

            return True

        except Exception as e:
            logger.error(f'节点更新失败: {node.id} - {str(e)}')
            return False


# 创建服务实例
archived_erp_service = ArchivedERPService()
