from openpyxl import Workbook
from openpyxl.styles import Pat<PERSON><PERSON>ill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, timedelta
import random
from typing import Dict, List, Set, Tuple, Optional
import colorsys
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
)
from database.base import engine


def remove_duplicates_from_paths(
    paths: List[List[int]], nodes_dict: Dict[int, NodeInstance]
) -> List[List[int]]:
    """
    从路径中删除重复节点，优先在较长路径中保留节点

    Args:
        paths: 按长度降序排序的路径列表
        nodes_dict: 节点字典

    Returns:
        List[List[int]]: 处理后的路径列表
    """

    # 记录每个节点出现的所有路径
    node_paths = {}
    for i, path in enumerate(paths):
        for node in path:
            if node not in node_paths:
                node_paths[node] = []
            node_paths[node].append(i)

    # 找出重复的节点
    duplicate_nodes = {
        node: indices
        for node, indices in node_paths.items()
        if len(indices) > 1
    }

    if not duplicate_nodes:
        return paths

    # 处理后的路径列表（深拷贝）
    processed_paths = [path[:] for path in paths]

    # 为每个重复节点，从较短的路径中删除
    for node, path_indices in duplicate_nodes.items():
        # 保留在最长路径（索引最小）中的节点
        keep_in_path = path_indices[0]
        paths_to_remove_from = path_indices[1:]

        for path_idx in paths_to_remove_from:
            if node in processed_paths[path_idx]:
                processed_paths[path_idx].remove(node)

    # 删除空路径
    processed_paths = [path for path in processed_paths if path]

    return processed_paths


def find_minimal_paths(
    graph: Dict, nodes_dict: Dict[int, NodeInstance]
) -> List[List[int]]:
    """
    找出覆盖所有节点的路径集合，并处理重复节点
    """

    def find_path_from_node(
        start_node: int, visited: Set[int] = None
    ) -> List[int]:
        """从给定节点寻找一条路径"""
        if visited is None:
            visited = set()

        path = []
        current = start_node

        while current is not None and current not in visited:
            visited.add(current)
            path.append(current)

            # 获取所有未访问的后继节点
            next_nodes = [
                n for n in graph['successors'][current] if n not in visited
            ]

            if not next_nodes:
                break

            # 选择入度最小的后继节点
            current = min(
                next_nodes, key=lambda n: len(graph['predecessors'][n])
            )

        return path

    def find_all_start_nodes() -> List[int]:
        """找出所有可能的起始节点"""
        # 首先使用图的起始节点
        start_nodes = list(graph['start_nodes'])

        # 添加所有入度为1的节点作为潜在的起始点
        for node_id in nodes_dict:
            if (
                len(graph['predecessors'][node_id]) == 1
                and node_id not in start_nodes
            ):
                start_nodes.append(node_id)

        return start_nodes

    # 从每个起始节点找出可能的路径
    all_paths = []
    start_nodes = find_all_start_nodes()

    for start in start_nodes:
        path = find_path_from_node(start)
        if path:
            all_paths.append(path)

    # 按路径长度降序排序
    all_paths.sort(key=len, reverse=True)

    # 检查是否覆盖了所有节点
    covered_nodes = {node for path in all_paths for node in path}
    uncovered = set(nodes_dict.keys()) - covered_nodes

    # 如果有未覆盖的节点，为它们创建单独的路径
    for node in uncovered:
        path = find_path_from_node(node)
        if path:
            all_paths.append(path)

    # 处理重复节点
    final_paths = remove_duplicates_from_paths(all_paths, nodes_dict)

    return final_paths


def find_all_paths(
    node_id: int,
    graph: Dict,
    nodes_dict: Dict[int, NodeInstance],
    current_path: Optional[List[int]] = None,
    all_paths: Optional[List[List[int]]] = None,
) -> List[List[int]]:
    """
    找出从给定节点开始的所有可能路径
    """
    if current_path is None:
        current_path = []
    if all_paths is None:
        all_paths = []

    current_path.append(node_id)

    # 如果是终点（没有后继节点），保存当前路径
    if not graph['successors'][node_id]:
        all_paths.append(current_path[:])
    else:
        # 继续遍历后继节点
        for next_node in graph['successors'][node_id]:
            find_all_paths(
                next_node, graph, nodes_dict, current_path[:], all_paths
            )

    return all_paths


def analyze_workflow_structure(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Tuple[Dict, List[List[int]], Dict[int, int]]:
    """
    分析工作流结构，识别并行路径和分配行号
    """

    # 构建节点关系图
    graph = build_node_graph(nodes, edges)
    nodes_dict = {node.id: node for node in nodes}

    # 找出路径集合并处理重复节点
    all_paths = find_minimal_paths(graph, nodes_dict)

    # 为每条路径分配唯一的行号
    node_rows = {}
    for row, path in enumerate(all_paths, 1):
        for node_id in path:
            node_rows[node_id] = row

    return graph, all_paths, node_rows


def adjust_chain_timing_v2(
    nodes: Dict[int, NodeInstance],
    graph: Dict,
    all_paths: List[List[int]],
    project_start: datetime,
) -> Dict[int, Tuple[datetime, datetime]]:
    """
    调整链路上节点的时间，只调整开始时间，保持原有截止时间不变
    """
    adjusted_times = {}
    skipped_nodes = set()  # 记录因时间冲突而跳过的节点

    def adjust_path_timing(path: List[int]):
        for i, node_id in enumerate(path):
            node = nodes[node_id]
            if not node.start_time:
                continue

            if i == 0:
                # 路径的第一个节点使用原始开始时间
                start_time = node.start_time
            else:
                # 后续节点在前一个节点结束后的第二天开始
                prev_node_id = path[i - 1]
                _, prev_end_time = adjusted_times[prev_node_id]
                start_time = prev_end_time + timedelta(days=1)

            # 保持原有截止时间
            end_time = node.end_time if node.end_time else datetime.now()

            # 检查调整后的开始时间是否超过截止时间
            if start_time.date() > end_time.date():
                skipped_nodes.add(node_id)
                continue

            adjusted_times[node_id] = (start_time, end_time)

    # 按路径调整时间
    for i, path in enumerate(all_paths):
        adjust_path_timing(path)

    return adjusted_times


def is_time_overlap(
    start1: datetime, end1: datetime, start2: datetime, end2: datetime
) -> bool:
    """判断两个时间段是否重叠"""
    current_time = datetime.now()
    end1 = end1 or current_time
    end2 = end2 or current_time
    return start1 < end2 and start2 < end1


def find_parallel_paths(
    node_id: int, edges: List[EdgeInstance], nodes: Dict[int, NodeInstance]
) -> List[List[int]]:
    """查找从给定节点开始的所有并行路径"""

    def dfs(
        current_id: int, current_path: List[int], all_paths: List[List[int]]
    ):
        current_path.append(current_id)
        next_nodes = [
            e.to_node_instance_id
            for e in edges
            if e.from_node_instance_id == current_id
        ]

        if not next_nodes:
            all_paths.append(current_path[:])
        else:
            for next_node in next_nodes:
                dfs(next_node, current_path[:], all_paths)

    all_paths = []
    dfs(node_id, [], all_paths)
    return all_paths


def assign_rows(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Dict[int, int]:
    """为节点分配行号，确保重叠的任务不会在同一行"""
    node_map = {node.id: node for node in nodes}
    node_rows = {}

    # 找到所有起始节点
    start_nodes = [
        n
        for n in nodes
        if not any(e.to_node_instance_id == n.id for e in edges)
    ]

    # 获取所有执行路径
    all_paths = []
    for start_node in start_nodes:
        paths = find_parallel_paths(start_node.id, edges, node_map)
        all_paths.extend(paths)

    # 创建节点到路径的映射
    node_to_paths = {node.id: [] for node in nodes}
    for path in all_paths:
        for node_id in path:
            node_to_paths[node_id].append(path)

    # 按开始时间排序节点
    sorted_nodes = sorted(
        [n for n in nodes if n.start_time], key=lambda x: x.start_time.date()
    )

    def get_path_nodes(node_id: int) -> Set[int]:
        """获取与当前节点在同一路径上的所有节点ID"""
        path_nodes = set()
        for path in node_to_paths[node_id]:
            path_nodes.update(path)
        return path_nodes

    # 为每个节点分配行号
    for node in sorted_nodes:
        # 获取与当前节点在同一路径上的所有节点
        path_nodes = get_path_nodes(node.id)

        # 查找可用的行号
        row = 0
        while True:
            row += 1
            can_use_row = True

            # 检查已分配行号的节点
            for other_node_id, assigned_row in node_rows.items():
                other_node = node_map[other_node_id]

                # 如果是同一路径上的节点，或者时间有重叠，就不能用相同的行
                if assigned_row == row and (
                    other_node_id in path_nodes
                    or (
                        other_node.start_time
                        and is_time_overlap(  # 确保节点有开始时间
                            node.start_time,
                            node.end_time,
                            other_node.start_time,
                            other_node.end_time,
                        )
                    )
                ):
                    can_use_row = False
                    break

            if can_use_row:
                break

        node_rows[node.id] = row

    return node_rows


def assign_rows_by_chain(
    nodes: List[NodeInstance],
    edges: List[EdgeInstance],
    adjusted_times: Dict[int, Tuple[datetime, datetime]],
) -> Dict[int, int]:
    """
    为节点分配行号，优先将同链路上的节点放在同一行

    Args:
        nodes: 节点列表
        edges: 边列表
        adjusted_times: 调整后的节点时间 {node_id: (start_time, end_time)}

    Returns:
        Dict[int, int]: 节点ID到行号的映射
    """
    # 构建节点关系图
    graph = build_node_graph(nodes, edges)
    node_rows = {}  # 存储节点的行号
    used_rows = {}  # 存储每行已使用的时间段 {row_num: [(start_time, end_time)]}

    def is_time_overlap(
        time1: Tuple[datetime, datetime], time2: Tuple[datetime, datetime]
    ) -> bool:
        """检查两个时间段是否重叠"""
        start1, end1 = time1
        start2, end2 = time2
        return start1 < end2 and start2 < end1

    def can_use_row(row: int, node_time: Tuple[datetime, datetime]) -> bool:
        """检查某行是否可以放置新的节点"""
        if row not in used_rows:
            return True
        return not any(
            is_time_overlap(node_time, time_slot)
            for time_slot in used_rows[row]
        )

    def get_available_row(node_time: Tuple[datetime, datetime]) -> int:
        """获取可用的行号"""
        row = 1
        while not can_use_row(row, node_time):
            row += 1
        return row

    def process_chain(node_id: int, preferred_row: int = None):
        """处理单个链路上的所有节点"""
        if node_id in node_rows:
            return

        # 获取节点的时间
        node_time = adjusted_times.get(node_id)
        if not node_time:
            return

        # 如果有建议的行号且该行可用，就使用建议的行号
        if preferred_row is not None and can_use_row(preferred_row, node_time):
            row = preferred_row
        else:
            # 否则找一个可用的行
            row = get_available_row(node_time)

        # 分配行号并记录使用情况
        node_rows[node_id] = row
        used_rows.setdefault(row, []).append(node_time)

        # 处理后继节点，优先使用同一行
        for successor_id in graph['successors'][node_id]:
            process_chain(successor_id, row)

    # 从每个起始节点开始处理链路
    for start_node_id in graph['start_nodes']:
        if start_node_id not in node_rows:
            process_chain(start_node_id)

    # 处理可能未分配行号的节点（如孤立节点）
    for node in nodes:
        if node.id not in node_rows and node.id in adjusted_times:
            node_time = adjusted_times[node.id]
            row = get_available_row(node_time)
            node_rows[node.id] = row
            used_rows.setdefault(row, []).append(node_time)

    return node_rows


def generate_distinct_colors(n: int) -> List[str]:
    """生成n个视觉上区分度高的颜色"""
    colors = []
    for i in range(n):
        hue = (i * 0.618033988749895) % 1
        saturation = 0.4 + random.random() * 0.2
        value = 0.8 + random.random() * 0.1

        rgb = colorsys.hsv_to_rgb(hue, saturation, value)
        hex_color = '%02x%02x%02x' % tuple(int(x * 255) for x in rgb)
        colors.append(hex_color.upper())

    return colors


def build_node_graph(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Dict:
    """
    构建节点关系图，包括前驱和后继节点
    返回: {
        'predecessors': {node_id: [predecessor_ids]},
        'successors': {node_id: [successor_ids]},
        'start_nodes': [node_ids],
    }
    """
    predecessors = {node.id: [] for node in nodes}
    successors = {node.id: [] for node in nodes}

    # 构建前驱和后继关系
    for edge in edges:
        predecessors[edge.to_node_instance_id].append(
            edge.from_node_instance_id
        )
        successors[edge.from_node_instance_id].append(edge.to_node_instance_id)

    # 找出起始节点（没有前驱的节点）
    start_nodes = [node.id for node in nodes if not predecessors[node.id]]

    return {
        'predecessors': predecessors,
        'successors': successors,
        'start_nodes': start_nodes,
    }


def adjust_chain_timing(
    nodes: Dict[int, NodeInstance], graph: Dict, project_start: datetime
) -> Dict[int, Tuple[datetime, datetime]]:
    """
    调整链路上节点的时间，确保后续节点在前一个节点结束后的第二天开始
    返回: {node_id: (adjusted_start_time, adjusted_end_time)}
    """
    adjusted_times = {}
    processed = set()

    def process_chain(node_id: int, chain_start: datetime = None):
        if node_id in processed:
            return

        node = nodes[node_id]
        if not node.start_time:
            return

        # 确定节点开始时间
        if chain_start is None:
            # 链路的第一个节点使用其原始开始时间
            start_time = node.start_time
        else:
            # 后续节点在前一天的基础上加1天
            start_time = chain_start + timedelta(days=1)

        # 计算结束时间
        if node.end_time:
            duration = (node.end_time - node.start_time).days
            end_time = start_time + timedelta(days=duration)
        else:
            end_time = datetime.now()

        adjusted_times[node_id] = (start_time, end_time)
        processed.add(node_id)

        # 处理后续节点
        for successor_id in graph['successors'][node_id]:
            process_chain(successor_id, end_time)

    # 从每个起始节点开始处理
    for start_node_id in graph['start_nodes']:
        if start_node_id not in processed:
            process_chain(start_node_id)

    return adjusted_times


def calculate_project_timeline(
    nodes: List[NodeInstance],
) -> Tuple[datetime, int]:
    """
    计算项目的开始时间和总持续天数
    返回: (项目开始时间, 总持续天数)
    """
    start_times = [n.start_time for n in nodes if n.start_time]
    if not start_times:
        return datetime.now(), 0

    project_start = min(start_times)
    current_date = datetime.now()
    duration = (current_date - project_start).days + 1
    return project_start, duration


def calculate_node_relative_days(
    start_time: datetime, end_time: datetime, project_start: datetime
) -> Tuple[int, int]:
    """
    计算节点相对于项目开始的天数
    返回: (开始天数, 结束天数)
    """
    if not start_time:
        return 0, 0

    start_day = (start_time - project_start).days + 1
    end_day = (end_time - project_start).days + 1

    return start_day, end_day


async def create_gantt_chart(session: AsyncSession, output_file: str):
    """创建甘特图Excel文件"""
    wb = Workbook()
    ws = wb.active
    ws.title = 'Project Gantt Chart'

    print('\n=== 开始生成甘特图 ===')

    # 获取主流程工作流
    workflows = (
        (
            await session.execute(
                select(WorkflowInstance)
                .join(WorkflowDefinition)
                .filter(WorkflowDefinition.is_subprocess == False)
            )
        )
        .scalars()
        .all()
    )

    # 获取所有唯一的节点名称和定义
    all_node_names = set()
    node_defs_map = {}

    # 计算所有项目中最长的持续天数
    max_duration = 0
    workflow_timelines = {}  # 存储每个工作流的时间信息

    for workflow in workflows:
        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        for node in nodes:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            all_node_names.add(node_def.name)
            node_defs_map[node.node_definition_id] = node_def

        # 计算每个工作流的时间线
        project_start, duration = calculate_project_timeline(nodes)
        workflow_timelines[workflow.id] = {
            'start': project_start,
            'duration': duration,
            'nodes': nodes,
        }
        max_duration = max(max_duration, duration)

    # 为每个节点名称生成唯一的颜色
    node_colors = dict(
        zip(
            sorted(all_node_names),
            generate_distinct_colors(len(all_node_names)),
        )
    )

    # 设置表头样式
    cell_width = 3
    ws.column_dimensions['A'].width = 15  # 项目名称列
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)

    # 写入表头
    ws['A1'] = '项目名称'
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font

    # 写入天数表头
    for day in range(1, max_duration + 1):
        col_letter = get_column_letter(day + 1)
        ws.column_dimensions[col_letter].width = cell_width
        cell = ws[f'{col_letter}1']
        cell.value = str(day)
        cell.alignment = Alignment(textRotation=90)
        cell.fill = header_fill
        cell.font = header_font

    current_row = 2

    # 为每个工作流实例创建甘特图
    for workflow in workflows:
        timeline = workflow_timelines[workflow.id]
        nodes = timeline['nodes']
        project_start = timeline['start']

        edges = (
            (
                await session.execute(
                    select(EdgeInstance).filter(
                        EdgeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        # 分析工作流结构
        graph, all_paths, node_rows = analyze_workflow_structure(nodes, edges)

        # 调整链路时间
        nodes_dict = {node.id: node for node in nodes}
        adjusted_times = adjust_chain_timing_v2(
            nodes_dict, graph, all_paths, project_start
        )

        max_row_num = max(node_rows.values()) if node_rows else 1

        # 写入工作流标题
        project_title = (
            f'项目 {workflow.project_id}'
            if workflow.project_id
            else f'工作流 {workflow.id}'
        )
        ws[f'A{current_row}'] = project_title
        ws[f'A{current_row}'].font = Font(bold=True)
        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 绘制每个节点的甘特图条
        for node in nodes:
            if node.start_time and node.id not in adjusted_times:  # 跳过时间冲突的节点
                continue

            if node.start_time:
                node_def = node_defs_map[node.node_definition_id]
                row = current_row + node_rows[node.id] - 1

                # 使用调整后的开始时间和原始结束时间
                start_time, end_time = adjusted_times[node.id]

                # 计算相对天数
                start_day = (start_time - project_start).days + 1
                end_day = (end_time - project_start).days + 1

                # 设置单元格颜色和合并
                start_col = start_day + 1  # 加1是因为A列是项目名称
                end_col = end_day + 1

                # 确保至少有一个格子被涂色
                end_col = max(start_col, end_col)

                start_letter = get_column_letter(start_col)
                end_letter = get_column_letter(end_col)

                # 为整个范围的单元格设置填充色
                for col in range(start_col, end_col + 1):
                    col_letter = get_column_letter(col)
                    cell = ws[f'{col_letter}{row}']
                    color = node_colors[node_def.name]
                    cell.fill = PatternFill(
                        start_color=color, end_color=color, fill_type='solid'
                    )

                # 合并单元格
                cell_range = f'{start_letter}{row}:{end_letter}{row}'
                ws.merge_cells(cell_range)
                print(cell_range, node_def.name, start_time, end_time)
                # 设置任务名称和对齐方式
                ws[f'{start_letter}{row}'] = node_def.name
                ws[f'{start_letter}{row}'].alignment = Alignment(
                    horizontal='center', vertical='center'
                )

        current_row += max_row_num + 1

    # 添加图例
    legend_row = current_row + 2
    ws[f'A{legend_row}'] = '图例：'
    ws[f'A{legend_row}'].font = Font(bold=True)
    legend_row += 1

    # 为每个任务类型创建图例
    for node_name, color in sorted(node_colors.items()):
        ws[f'A{legend_row}'] = node_name
        cell = ws[f'B{legend_row}']
        cell.fill = PatternFill(
            start_color=color, end_color=color, fill_type='solid'
        )
        legend_row += 1

    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    for row in ws.iter_rows(min_row=1, max_row=legend_row - 1):
        for cell in row:
            cell.border = thin_border

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_gantt_chart(session, 'tests/project_gantt_chart.xlsx')
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    import asyncio

    asyncio.run(generate_project_gantt_chart())
