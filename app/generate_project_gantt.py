import asyncio
from openpyxl import Workbook, load_workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, timedelta
from typing import Dict, List, Set
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
)
from database.base import engine
from schemas import WorkflowStatus, NodeStatus
from utils.constants import NODE_COLORS
from database.crud.info_management import info
from utils.tools import calculate_column_width
from utils.gantt_chart_generator import find_workflow_paths


def is_time_overlap(
    start1: datetime, end1: datetime, start2: datetime, end2: datetime
) -> bool:
    """判断两个时间段是否重叠"""
    current_time = datetime.now()
    end1 = end1 or current_time
    end2 = end2 or current_time
    return start1 < end2 and start2 < end1


def adjust_node_times_by_template(
    nodes: List[NodeInstance],
    template_paths: List[List[str]],
    template_node_rows: Dict[str, int],
) -> Dict[int, datetime]:
    """
    根据模板路径和行号调整节点时间
    处理原则：
    1. 如果是进行中的节点（有开始时间，无结束时间），保留该节点时间不变
    2. 如果两个节点时间相邻或重叠，前面节点的结束时间需要比后面节点的开始时间早一天
    """
    adjusted_times = {}  # 存储调整后的时间 {node_id: (start_time, end_time)}
    current_time = datetime.now()

    # 获取所有有时间信息的节点
    timed_nodes = []
    for node in nodes:
        if node.start_time:
            row = template_node_rows.get(node.node_definition_id, 0)
            # 记录节点是否正在进行中
            is_active = bool(node.start_time and not node.end_time)
            end_time = node.end_time if node.end_time else current_time
            timed_nodes.append((node, row, is_active, end_time))
    # 按行号分组
    row_nodes = {}
    for node, row, is_active, end_time in timed_nodes:
        if row not in row_nodes:
            row_nodes[row] = []
        row_nodes[row].append((node, False, end_time))
    # 处理每一行的节点
    for row, nodes_in_row in row_nodes.items():
        # 按开始时间排序，活动节点放在最后
        nodes_in_row.sort(
            key=lambda x: (
                not x[1],  # 非活动节点优先
                x[0].start_time.date(),  # 按开始时间排序
            )
        )

        # 从后往前调整时间
        for i in range(len(nodes_in_row) - 1, -1, -1):
            current_node, is_active, current_end = nodes_in_row[i]
            current_start = current_node.start_time

            # 活动节点保持原样
            if is_active:
                adjusted_times[current_node.id] = (current_start, current_end)
                continue

            # 检查与后续节点的时间重叠或相邻
            next_conflicting_start = None
            for j in range(i + 1, len(nodes_in_row)):
                next_node, next_is_active, next_end = nodes_in_row[j]
                next_start = next_node.start_time

                # 检查时间重叠或相邻（结束时间等于开始时间）
                if (
                    current_start.date() <= next_end.date()
                    and current_end.date() >= next_start.date()
                ):
                    # 如果后续节点是活动的或者还没有找到冲突时间
                    if next_is_active or next_conflicting_start is None:
                        next_conflicting_start = next_start

            # 如果存在重叠或相邻，调整当前节点的结束时间
            if next_conflicting_start:
                # 将结束时间调整为后续节点开始时间的前一天
                adjusted_end = next_conflicting_start - timedelta(days=1)
                # 只有当调整后的结束时间仍然在开始时间之后才进行调整
                if adjusted_end.date() >= current_start.date():
                    current_end = adjusted_end

            adjusted_times[current_node.id] = (current_start, current_end)

    # 返回调整后的时间（包含开始和结束时间）
    return adjusted_times


def find_parallel_paths(
    node_id: int, edges: List[EdgeInstance], nodes: Dict[int, NodeInstance]
) -> List[List[int]]:
    """查找从给定节点开始的所有并行路径"""

    def dfs(
        current_id: int, current_path: List[int], all_paths: List[List[int]]
    ):
        current_path.append(current_id)
        next_nodes = [
            e.to_node_instance_id
            for e in edges
            if e.from_node_instance_id == current_id
        ]

        if not next_nodes:
            all_paths.append(current_path[:])
        else:
            for next_node in next_nodes:
                dfs(next_node, current_path[:], all_paths)

    all_paths = []
    dfs(node_id, [], all_paths)
    return all_paths


def assign_rows(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Dict[int, int]:
    """为节点分配行号，确保重叠的任务不会在同一行"""
    node_map = {node.id: node for node in nodes}
    node_rows = {}

    # 找到所有起始节点
    start_nodes = [
        n
        for n in nodes
        if not any(e.to_node_instance_id == n.id for e in edges)
    ]

    # 获取所有执行路径
    all_paths = []
    for start_node in start_nodes:
        paths = find_parallel_paths(start_node.id, edges, node_map)
        all_paths.extend(paths)

    # 创建节点到路径的映射
    node_to_paths = {node.id: [] for node in nodes}
    for path in all_paths:
        for node_id in path:
            node_to_paths[node_id].append(path)

    # 按开始时间排序节点
    sorted_nodes = sorted(
        [n for n in nodes if n.start_time], key=lambda x: x.start_time.date()
    )

    def get_path_nodes(node_id: int) -> Set[int]:
        """获取与当前节点在同一路径上的所有节点ID"""
        path_nodes = set()
        for path in node_to_paths[node_id]:
            path_nodes.update(path)
        return path_nodes

    # 为每个节点分配行号
    for node in sorted_nodes:
        # 获取与当前节点在同一路径上的所有节点
        path_nodes = get_path_nodes(node.id)

        # 查找可用的行号
        row = 0
        while True:
            row += 1
            can_use_row = True

            # 检查已分配行号的节点
            for other_node_id, assigned_row in node_rows.items():
                other_node = node_map[other_node_id]

                # 如果是同一路径上的节点，或者时间有重叠，就不能用相同的行
                if assigned_row == row and (
                    other_node_id in path_nodes
                    or (
                        other_node.start_time
                        and is_time_overlap(
                            node.start_time,
                            node.end_time,
                            other_node.start_time,
                            other_node.end_time,
                        )
                    )
                ):
                    can_use_row = False
                    break

            if can_use_row:
                break

        node_rows[node.id] = row

    return node_rows


def adjust_times_by_rows(
    nodes: List[NodeInstance],
    edges: List[EdgeInstance],
    node_rows: Dict[int, int],
) -> Dict[int, datetime]:
    """根据行号调整节点时间"""
    adjusted_start_times = {}

    # 按行号和开始时间排序节点
    sorted_nodes = sorted(
        [(n, node_rows.get(n.id, 0)) for n in nodes if n.start_time],
        key=lambda x: (x[1], x[0].start_time.date()),
    )

    # 记录每行最后的结束时间
    row_end_times = {}

    # 调整每个节点的时间
    for node, row in sorted_nodes:
        # 获取当前行的最后结束时间
        last_end_date = row_end_times.get(row)

        # 确定开始日期
        start_date = node.start_time.date()
        if last_end_date and start_date <= last_end_date:
            # 如果开始时间早于或等于该行最后结束时间，调整到下一天
            start_date = last_end_date + timedelta(days=1)

        # 检查调整后的开始时间是否可行
        if not node.end_time or start_date <= node.end_time.date():
            adjusted_start = datetime.combine(start_date, datetime.min.time())
            adjusted_start_times[node.id] = adjusted_start

            # 更新该行的最后结束时间
            if node.end_time:
                row_end_times[row] = node.end_time.date()
            else:
                row_end_times[row] = datetime.now().date()

    return adjusted_start_times


async def create_gantt_chart(session: AsyncSession, output_file: str):
    """创建甘特图Excel文件"""
    try:
        wb = load_workbook(output_file)
    except FileNotFoundError:
        wb = Workbook()

    sheet_name = '甘特图（日期）'
    base_name = sheet_name
    counter = 1
    while sheet_name in wb.sheetnames:
        sheet_name = f'{base_name}_{counter}'
        counter += 1

    ws = wb.create_sheet(sheet_name)

    # 获取主流程工作流
    workflows = (
        (
            await session.execute(
                select(WorkflowInstance)
                .join(WorkflowDefinition)
                .filter(WorkflowDefinition.is_subprocess == False)
            )
        )
        .scalars()
        .all()
    )

    if not workflows:
        print('未找到工作流')
        return

    # 获取第一个工作流的结构作为模板
    template_workflow = workflows[0]
    template_nodes = (
        (
            await session.execute(
                select(NodeInstance).filter(
                    NodeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    template_edges = (
        (
            await session.execute(
                select(EdgeInstance).filter(
                    EdgeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    # 分析模板工作流结构（只执行一次）
    template_graph, template_paths, template_node_rows = find_workflow_paths(
        template_nodes, template_edges
    )

    # 获取所有唯一的节点名称和定义
    node_defs_map = {}
    all_start_times = []
    current_date = datetime.now().replace(
        hour=0, minute=0, second=0, microsecond=0
    )

    for workflow in workflows:
        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        for node in nodes:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            node_defs_map[node.node_definition_id] = node_def
            if node.start_time:
                all_start_times.append(node.start_time)

    # 设置时间范围
    chart_start = (
        min(all_start_times).replace(hour=0, minute=0, second=0, microsecond=0)
        if all_start_times
        else current_date
    )
    chart_end = current_date

    # 生成日期列表
    dates = []
    temp_date = chart_start
    while temp_date <= chart_end:
        dates.append(temp_date)
        temp_date += timedelta(days=1)

    # 设置表头
    ws['A1'] = '项目名称'
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font

    # 写入日期标题
    cell_width = 3
    for col, date in enumerate(dates, start=2):
        col_letter = get_column_letter(col)
        ws.column_dimensions[col_letter].width = cell_width
        cell = ws[f'{col_letter}1']
        cell.value = date.strftime('%m-%d')
        cell.alignment = Alignment(textRotation=90)
        cell.fill = header_fill
        cell.font = header_font

    current_row = 2
    erp_info = await info.get_erp_info_dict(session)
    column_widths = []

    # 处理每个工作流
    for workflow in workflows:
        if workflow.status in [
            WorkflowStatus.completed,
            WorkflowStatus.terminated,
        ]:
            continue

        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        # 跳过检查逻辑...
        if all(node.status == NodeStatus.completed for node in nodes):
            continue
        if not any(node.start_time for node in nodes):
            continue

        # 使用模板路径和行号调整节点时间
        adjusted_times = adjust_node_times_by_template(
            nodes, template_paths, template_node_rows
        )

        max_row_num = (
            max(template_node_rows.values()) if template_node_rows else 1
        )

        # 写入工作流标题
        erp_row = erp_info[workflow.project_id]
        project_title = (
            f'{workflow.project_id} {erp_row["customer"]} {erp_row["model"]}'
        )
        ws[f'A{current_row}'] = project_title
        ws[f'A{current_row}'].font = Font(bold=True)

        # 设置列宽和合并单元格
        column_width = calculate_column_width(project_title)
        column_widths.append(column_width)
        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 绘制甘特图条
        for node in nodes:
            if node.id not in adjusted_times:
                continue

            # 获取调整后的起止时间
            start_time = adjusted_times[node.id][0]  # 获取元组的第一个元素作为开始时间
            end_time = adjusted_times[node.id][1]  # 获取元组的第二个元素作为结束时间

            # 使用模板中的行号
            row = current_row + template_node_rows[node.node_definition_id] - 1

            # 计算列位置
            start_col = (start_time.date() - chart_start.date()).days + 2
            end_col = (end_time.date() - chart_start.date()).days + 2

            # 设置单元格样式和内容
            start_letter = get_column_letter(start_col)
            end_letter = get_column_letter(end_col)

            node_def = node_defs_map[node.node_definition_id]
            color = NODE_COLORS[node_def.name]

            # 首先设置单元格的值
            first_cell = ws[f'{start_letter}{row}']
            first_cell.value = node_def.name
            first_cell.alignment = Alignment(
                horizontal='center', vertical='center'
            )

            # 为整个范围设置填充色
            for col in range(start_col, end_col + 1):
                col_letter = get_column_letter(col)
                cell = ws[f'{col_letter}{row}']
                cell.fill = PatternFill(
                    start_color=color, end_color=color, fill_type='solid'
                )

            # 最后合并单元格
            cell_range = f'{start_letter}{row}:{end_letter}{row}'
            ws.merge_cells(cell_range)

        current_row += max_row_num + 1

    # 最终调整
    ws.column_dimensions['A'].width = max(column_widths)

    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    for row in ws.iter_rows(min_row=1, max_row=current_row - 1):
        for cell in row:
            cell.border = thin_border

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_gantt_chart(session, 'tests/project_gantt_chart.xlsx')
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    asyncio.run(generate_project_gantt_chart())
