from sqlalchemy.ext.asyncio import AsyncSession
from database.base import engine, create_tables
from database.models import (
    WorkflowDefinition,
    NodeDefinition,
    EdgeDefinition,
    NodeType,
    EdgeCondition,
)
import asyncio
from typing import Dict, Optional


class WorkflowBuilder:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.nodes: Dict[str, Dict[str, NodeDefinition]] = {}

    async def create_workflow(
        self,
        name: str,
        description: str,
        is_subprocess: bool = False,
        version: int = 1,
    ) -> str:
        workflow = WorkflowDefinition(
            name=name,
            description=description,
            version=version,
            is_latest=True,
            is_subprocess=is_subprocess,
        )
        self.db.add(workflow)
        await self.db.flush()

        workflow_key = name
        self.workflows[workflow_key] = workflow
        self.nodes[workflow_key] = {}

        return workflow_key

    async def add_node(
        self,
        workflow_key: str,
        node_key: str,
        name: str,
        node_type: NodeType,
        subprocess_id: Optional[int] = None,
    ) -> NodeDefinition:
        workflow = self.workflows[workflow_key]
        node = NodeDefinition(
            workflow_definition_id=workflow.id,
            name=name,
            type=node_type,
            subprocess_id=subprocess_id,
        )
        self.db.add(node)
        await self.db.flush()

        self.nodes[workflow_key][node_key] = node
        return node

    async def add_edge(
        self,
        workflow_key: str,
        from_node_key: str,
        to_node_key: str,
        condition: Optional[EdgeCondition] = None,
    ):
        workflow = self.workflows[workflow_key]
        from_node = self.nodes[workflow_key][from_node_key]
        to_node = self.nodes[workflow_key][to_node_key]

        edge = EdgeDefinition(
            workflow_definition_id=workflow.id,
            from_node_id=from_node.id,
            to_node_id=to_node.id,
            condition=condition,
        )
        self.db.add(edge)
        await self.db.flush()
        return edge


async def initialize_workflows():
    """初始化所有工作流定义"""
    async with AsyncSession(engine) as db:
        try:
            builder = WorkflowBuilder(db)
            workflow_ids = {}

            # 1. 主流程
            main_flow = await builder.create_workflow(
                name='卷绕数据管理工作流主流程', description='卷绕数据管理工作流主流程'
            )
            workflow_ids['main'] = builder.workflows[main_flow].id

            # # 2. 部件信息录入流程
            # component_flow = await builder.create_workflow(
            #     name='部件伺服信息录入', description='部件伺服信息录入子流程', is_subprocess=True
            # )
            # workflow_ids['component'] = builder.workflows[component_flow].id

            # # 3. 其他硬件信息录入流程
            # hardware_flow = await builder.create_workflow(
            #     name='部件完整信息录入', description='其他硬件信息录入子流程', is_subprocess=True
            # )
            # workflow_ids['hardware'] = builder.workflows[hardware_flow].id

            # 4. 伺服图纸生成流程
            servo_flow = await builder.create_workflow(
                name='伺服图纸自动生成流程',
                description='伺服图纸和钻孔图生成子流程',
                is_subprocess=True,
            )
            workflow_ids['servo'] = builder.workflows[servo_flow].id

            # 5. 原理图生成流程
            schematic_flow = await builder.create_workflow(
                name='原理图纸自动生成流程',
                description='原理图生成及投料子流程',
                is_subprocess=True,
            )
            workflow_ids['schematic'] = builder.workflows[schematic_flow].id

            # # 6. 气路设计流程
            # pneumatic_flow = await builder.create_workflow(
            #     name='气路设计流程', description='气路设计子流程', is_subprocess=True
            # )
            # workflow_ids['pneumatic'] = builder.workflows[pneumatic_flow].id

            # 创建主流程节点
            main_nodes = [
                ('start', '设备预研', NodeType.start),
                ('node1', '总装部件梳理', NodeType.process),
                ('node2', '部件伺服信息录入', NodeType.process),
                ('node3', '部件完整信息录入', NodeType.process),
                ('node4', '气路设计', NodeType.process),  # 位置调整
                ('node5', '机电沟通表入库', NodeType.process),
                ('node6', 'IO表入库', NodeType.process),
                ('node7', 'EM信息录入', NodeType.process),
                ('node8', 'PLC和HMI程序自动生成', NodeType.process),
                ('node9', '机型负责人检查确认', NodeType.process),
                ('node10', '伺服图纸自动生成钻孔图', NodeType.subprocess),  # 新位置
                ('node11', '原理图纸自动生成投料和图纸下发', NodeType.subprocess),  # 新位置
                ('node12', '机型负责人录入版本信息', NodeType.process),
                ('node13', '硬件开发负责人审核', NodeType.process),
                ('end', '厂内装配调试', NodeType.end),
            ]

            # 更新主流程边
            main_edges = [
                ('start', 'node1'),  # 设备预研 -> 总装部件梳理
                ('node1', 'node2'),  # 总装部件梳理 -> 部件伺服信息录入
                ('node2', 'node3'),  # 部件伺服信息录入 -> 部件完整信息录入
                ('node12', 'node4'),  # 机型负责人录入版本信息 -> 气路设计
                ('node3', 'node12'),  # 部件完整信息录入 -> 机型负责人录入版本信息
                ('node12', 'node5'),  # 机型负责人录入版本信息 -> 机电沟通表入库
                ('node2', 'node10'),  # 部件完整信息录入 -> 伺服图纸自动生成
                ('node4', 'node6'),  # 气路设计 -> IO表入库
                ('node5', 'node6'),  # 机电沟通表入库 -> IO表入库
                ('node5', 'node7'),  # 机电沟通表入库 -> EM信息录入
                ('node6', 'node8'),  # IO表入库 -> PLC和HMI程序自动生成
                ('node6', 'node11'),  # IO表入库 -> 原理图纸自动生成投料和图纸下发
                ('node7', 'node8'),  # EM信息录入 -> PLC和HMI程序自动生成
                ('node8', 'node9'),  # PLC和HMI程序自动生成 -> 机型负责人检查确认
                ('node9', 'end'),  # 机型负责人检查确认 -> 厂内装配调试
                ('node10', 'node11'),  # 伺服图纸自动生成钻孔图 -> 原理图纸自动生成投料和图纸下发
                ('node11', 'node13'),  # 原理图纸自动生成投料和图纸下发 -> 硬件开发负责人审核
                ('node13', 'end'),  # 硬件开发负责人审核 -> 厂内装配调试
            ]

            # 设置子流程关联
            for key, name, node_type in main_nodes:
                subprocess_id = None
                if key == 'node10':
                    subprocess_id = workflow_ids['servo']  # 伺服图纸子流程
                elif key == 'node11':
                    subprocess_id = workflow_ids['schematic']  # 原理图子流程
                # elif key == 'node4':
                #     subprocess_id = workflow_ids['pneumatic']  # 气路设计子流程
                # elif key == 'node2':
                #     subprocess_id = workflow_ids['component']  # 部件伺服信息录入子流程
                # elif key == 'node3':
                #     subprocess_id = workflow_ids['hardware']  # 部件完整信息录入子流程
                await builder.add_node(
                    main_flow, key, name, node_type, subprocess_id
                )

            for from_key, to_key in main_edges:
                await builder.add_edge(main_flow, from_key, to_key)

            # # 创建部件伺服信息录入节点
            # component_nodes = [
            #     ('start', '总装部件分配完成', NodeType.start),
            #     ('node1', '录入部件信息', NodeType.process),
            #     ('node2', '部件伺服和预留确认', NodeType.decision),
            #     ('node3', '录入伺服信息', NodeType.process),
            #     ('node4', '伺服信息确认', NodeType.decision),
            #     ('end', '部件信息录入完成', NodeType.end),
            # ]

            # for key, name, node_type in component_nodes:
            #     await builder.add_node(component_flow, key, name, node_type)

            # # 创建部件伺服信息录入边
            # component_edges = [
            #     ('start', 'node1'),  # 总装部件分配完成 -> 录入部件信息
            #     ('node1', 'node2'),  # 录入部件信息 -> 部件伺服和预留确认
            #     ('node2', 'node3'),  # 预留确认 -> 录入伺服信息 (通过)
            #     ('node3', 'node4'),  # 录入伺服信息 -> 伺服信息确认
            #     ('node4', 'node3', EdgeCondition.no),  # 伺服信息确认 -> 录入伺服信息 (不通过)
            #     ('node4', 'end', EdgeCondition.yes),  # 伺服信息确认 -> 结束 (通过)
            # ]

            # for edge in component_edges:
            #     if len(edge) == 2:
            #         await builder.add_edge(component_flow, edge[0], edge[1])
            #     else:
            #         await builder.add_edge(
            #             component_flow, edge[0], edge[1], edge[2]
            #         )

            # # 创建其他硬件信息录入节点
            # hardware_nodes = [
            #     ('start', '开始录入其他硬件信息', NodeType.start),
            #     ('node1', '录入其他硬件信息', NodeType.process),
            #     ('node2', '信息录入完成', NodeType.decision),
            #     ('end', '部件信息录入完成', NodeType.end),
            # ]

            # for key, name, node_type in hardware_nodes:
            #     await builder.add_node(hardware_flow, key, name, node_type)

            # # 创建其他硬件信息录入边
            # hardware_edges = [
            #     ('start', 'node1'),  # 开始录入 -> 录入其他硬件信息
            #     ('node1', 'node2'),  # 录入其他硬件信息 -> 信息录入完成确认
            #     (
            #         'node2',
            #         'node1',
            #         EdgeCondition.no,
            #     ),  # 信息录入完成确认 -> 录入其他硬件信息 (不通过)
            #     ('node2', 'end', EdgeCondition.yes),  # 信息录入完成确认 -> 结束 (通过)
            # ]

            # for edge in hardware_edges:
            #     if len(edge) == 2:
            #         await builder.add_edge(hardware_flow, edge[0], edge[1])
            #     else:
            #         await builder.add_edge(
            #             hardware_flow, edge[0], edge[1], edge[2]
            #         )

            # 创建伺服图纸生成流程节点
            servo_nodes = [
                ('start', '伺服沟通表完成', NodeType.start),
                ('node1', '生成驱动器选型表', NodeType.process),
                ('node2', '生成EtherCat伺服节点', NodeType.process),
                ('node3', '生成伺服图纸', NodeType.process),
                ('node4', '底板图制作', NodeType.process),
                ('node5', '底板图点检', NodeType.decision),
                ('node6', '底板排版、钻孔', NodeType.process),
                ('node7', '机械确认底板图', NodeType.decision),
                ('end', '钻孔图完成', NodeType.end),
            ]

            for key, name, node_type in servo_nodes:
                await builder.add_node(servo_flow, key, name, node_type)

            # 创建伺服图纸生成流程边
            servo_edges = [
                ('start', 'node1'),  # 伺服沟通表完成 -> 生成驱动器选型表
                ('start', 'node2'),  # 伺服沟通表完成 -> 生成EtherCat伺服节点
                ('node1', 'node3'),  # 生成驱动器选型表 -> 生成伺服图纸
                ('node2', 'node3'),  # 生成EtherCat伺服节点 -> 生成伺服图纸
                ('node3', 'node4'),  # 生成伺服图纸 -> 底板图制作
                ('node4', 'node5'),  # 底板图制作 -> 底板图点检
                ('node5', 'node4', EdgeCondition.no),  # 底板图点检 -> 底板图制作 (不通过)
                ('node5', 'node6', EdgeCondition.yes),  # 底板图点检 -> 底板排版、钻孔 (通过)
                ('node6', 'node7'),  # 底板排版、钻孔 -> 机械确认底板图
                (
                    'node7',
                    'node6',
                    EdgeCondition.no,
                ),  # 机械确认底板图 -> 底板排版、钻孔 (不通过)
                ('node7', 'end', EdgeCondition.yes),  # 机械确认底板图 -> 钻孔图完成 (通过)
            ]

            for edge in servo_edges:
                if len(edge) == 2:
                    await builder.add_edge(servo_flow, edge[0], edge[1])
                else:
                    await builder.add_edge(
                        servo_flow, edge[0], edge[1], edge[2]
                    )

            # # 创建气路设计流程节点
            # pneumatic_nodes = [
            #     ('start', '机电沟通表入库', NodeType.start),
            #     ('node1', '拉取已提交气缸、真空、吹气', NodeType.process),
            #     ('node2', '气路设计', NodeType.process),
            #     ('node3', '设计审核', NodeType.decision),
            #     ('end', '气路信息入库', NodeType.end),
            # ]

            # for key, name, node_type in pneumatic_nodes:
            #     await builder.add_node(pneumatic_flow, key, name, node_type)

            # # 创建气路设计流程边
            # pneumatic_edges = [
            #     ('start', 'node1'),  # 机电沟通表入库 -> 拉取已提交气缸信息
            #     ('node1', 'node2'),  # 拉取已提交气缸信息 -> 气路设计
            #     ('node2', 'node3'),  # 气路设计 -> 设计审核
            #     ('node3', 'node2', EdgeCondition.no),  # 设计审核 -> 气路设计 (不通过)
            #     ('node3', 'end', EdgeCondition.yes),  # 设计审核 -> 气路信息入库 (通过)
            # ]

            # for edge in pneumatic_edges:
            #     if len(edge) == 2:
            #         await builder.add_edge(pneumatic_flow, edge[0], edge[1])
            #     else:
            #         await builder.add_edge(
            #             pneumatic_flow, edge[0], edge[1], edge[2]
            #         )

            # 创建原理图生成流程节点
            schematic_nodes = [
                ('start', 'IO表入库', NodeType.start),
                ('node1', '生成完整选型表', NodeType.process),
                ('node2', '填写线缆长度', NodeType.process),
                ('node3', '生成原理图', NodeType.process),
                ('node4', '比对沟通表和EPLAN', NodeType.process),
                ('node5', '投料', NodeType.process),
                ('node6', '比对BOM数据', NodeType.process),
                ('node7', '图纸完成下发', NodeType.process),
                ('node8', '是否批量机>5台', NodeType.process),
                ('node9', '柜内布线', NodeType.process),
                ('node10', '柜外布线', NodeType.process),
                ('node11', '终版底板图', NodeType.process),  # 添加终版底板图节点
                ('end', '图纸完成', NodeType.end),
            ]

            for key, name, node_type in schematic_nodes:
                await builder.add_node(schematic_flow, key, name, node_type)

            # 创建原理图生成流程边
            schematic_edges = [
                ('start', 'node1'),  # IO表入库 -> 生成完整选型表
                ('node1', 'node2'),  # 生成完整选型表 -> 填写线缆长度
                ('node2', 'node3'),  # 填写线缆长度 -> 生成原理图
                ('node3', 'node4'),  # 生成原理图 -> 比对沟通表和EPLAN
                ('node4', 'node5'),  # 比对沟通表和EPLAN -> 投料
                ('node5', 'node6'),  # 投料 -> 比对BOM数据
                ('node6', 'node7'),  # 比对BOM数据 -> 图纸完成下发
                ('node7', 'node8'),  # 图纸完成下发 -> 批量机判断
                ('node8', 'node9'),  # 批量机判断 -> 柜内布线 (>5台)
                ('node8', 'node10'),  # 批量机判断 -> 柜外布线 (>5台)
                ('node9', 'node11'),  # 柜内布线 -> 终版底板图
                ('node10', 'end'),  # 柜外布线 -> 图纸完成
                ('node11', 'end'),  # 终版底板图 -> 图纸完成
            ]

            for edge in schematic_edges:
                if len(edge) == 2:
                    await builder.add_edge(schematic_flow, edge[0], edge[1])
                else:
                    await builder.add_edge(
                        schematic_flow, edge[0], edge[1], edge[2]
                    )

            await db.commit()
            print('所有工作流定义初始化完成')
            return workflow_ids

        except Exception as e:
            await db.rollback()
            print(f'初始化工作流定义时发生错误: {str(e)}')
            raise


async def run_initialization():
    try:
        await create_tables()
        await initialize_workflows()
    finally:
        await engine.dispose()


if __name__ == '__main__':
    asyncio.run(run_initialization())
