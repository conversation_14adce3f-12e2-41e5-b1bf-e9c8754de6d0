from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import text
from database.base import engine, create_tables
from database.models import (
    NodeInstance,
    NodeStatus,
    EdgeInstance,
    WorkflowInstance,
    WorkflowStatus,
    InstanceVariable,
    ChangeLog,
    ApprovalRecord,
)
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import asyncio


async def get_latest_workflows(db: AsyncSession) -> Dict[str, int]:
    """获取所有最新版本的工作流定义"""
    workflow_map = {}
    result = await db.execute(
        text(
            'SELECT name, id FROM workflow_definitions WHERE is_latest = TRUE'
        )
    )
    workflows = result.fetchall()
    for wf in workflows:
        workflow_map[wf.name] = wf.id
    return workflow_map


class WorkflowInstanceBuilder:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.instance_map: Dict[
            int, WorkflowInstance
        ] = {}  # workflow_def_id -> instance
        self.node_instance_map: Dict[
            int, Dict[int, NodeInstance]
        ] = {}  # workflow_def_id -> {node_def_id -> instance}

    async def create_workflow_instance(
        self,
        workflow_def_id: int,
        status: WorkflowStatus,
        start_time: datetime,
        creator: str,
        project_id: str,
    ) -> WorkflowInstance:
        """创建工作流实例"""
        instance = WorkflowInstance(
            workflow_definition_id=workflow_def_id,
            status=status,
            start_time=start_time,
            end_time=start_time + timedelta(days=1)
            if status == WorkflowStatus.completed
            else None,
            created_by=creator,
            project_id=project_id,
        )
        self.db.add(instance)
        await self.db.flush()

        self.instance_map[workflow_def_id] = instance
        self.node_instance_map[workflow_def_id] = {}

        # 记录工作流启动日志
        await self.create_change_log(
            instance.id, creator, 'flow_started', f'工作流 {workflow_def_id} 启动'
        )

        return instance

    async def create_node_instances(
        self,
        workflow_def_id: int,
        node_statuses: Dict[str, NodeStatus],
        creator: str,
    ):
        """为工作流创建所有节点实例"""
        workflow_instance = self.instance_map[workflow_def_id]

        # 获取工作流的节点定义
        result = await self.db.execute(
            text(
                """
                SELECT n.id, n.name, n.type, n.input_logic, n.subprocess_id
                FROM workflow_node_definitions n
                WHERE n.workflow_definition_id = :wf_id
            """
            ),
            {'wf_id': workflow_def_id},
        )
        nodes = result.fetchall()

        # 创建节点实例
        for node in nodes:
            node_status = node_statuses.get(node.name, NodeStatus.pending)
            start_time = (
                workflow_instance.start_time
                if node_status != NodeStatus.pending
                else None
            )
            end_time = (
                start_time + timedelta(hours=4)
                if node_status == NodeStatus.completed
                else None
            )

            node_instance = NodeInstance(
                workflow_instance_id=workflow_instance.id,
                node_definition_id=node.id,
                status=node_status,
                start_time=start_time,
                end_time=end_time,
                assigned_to=creator
                if node_status in [NodeStatus.active, NodeStatus.completed]
                else None,
            )
            self.db.add(node_instance)
            await self.db.flush()

            self.node_instance_map[workflow_def_id][node.id] = node_instance

            if node_status != NodeStatus.pending:
                await self.create_change_log(
                    workflow_instance.id,
                    creator,
                    'node_status_changed',
                    f'节点 {node.name} 状态更新为 {node_status.value}',
                )

    async def create_edge_instances(
        self, workflow_def_id: int, start_time: datetime
    ):
        """为工作流创建所有边实例"""
        workflow_instance = self.instance_map[workflow_def_id]

        # 获取工作流的边定义
        result = await self.db.execute(
            text(
                """
                SELECT e.id, e.from_node_id, e.to_node_id, e.condition,
                    fn.name as from_node_name, tn.name as to_node_name,
                    fn.type as from_node_type, tn.type as to_node_type
                FROM workflow_edge_definitions e
                JOIN workflow_node_definitions fn ON e.from_node_id = fn.id
                JOIN workflow_node_definitions tn ON e.to_node_id = tn.id
                WHERE e.workflow_definition_id = :wf_id
            """
            ),
            {'wf_id': workflow_def_id},
        )
        edges = result.fetchall()

        # 创建边实例
        for edge in edges:
            from_node_instance = self.node_instance_map[workflow_def_id][
                edge.from_node_id
            ]
            to_node_instance = self.node_instance_map[workflow_def_id][
                edge.to_node_id
            ]

            # 确定是否创建边实例的条件
            should_create_edge = True
            transition_time = None

            # 判断是否应该创建边实例的规则：
            # 1. 如果源节点已完成，创建边实例
            if from_node_instance.status == NodeStatus.completed:
                should_create_edge = True
                transition_time = from_node_instance.end_time or (
                    start_time + timedelta(hours=2)
                )

            # 2. 如果源节点处于活动状态且目标节点也是活动状态或待处理状态，创建边实例
            elif from_node_instance.status == NodeStatus.active:
                should_create_edge = True
                transition_time = None

            # 3. 对于决策节点的特殊处理
            elif (
                str(edge.from_node_type) == 'NodeType.decision'
                and edge.condition
            ):
                should_create_edge = True
                transition_time = None

            # 4. 开始节点总是创建出边
            elif str(edge.from_node_type) == 'NodeType.start':
                should_create_edge = True
                transition_time = start_time

            # 创建边实例
            if should_create_edge:
                edge_instance = EdgeInstance(
                    workflow_instance_id=workflow_instance.id,
                    edge_definition_id=edge.id,
                    from_node_instance_id=from_node_instance.id,
                    to_node_instance_id=to_node_instance.id,
                    transition_time=transition_time,
                )
                self.db.add(edge_instance)

                # 记录日志
                if transition_time:
                    await self.create_change_log(
                        workflow_instance.id,
                        'system',
                        'edge_created',
                        f'创建从 {edge.from_node_name} 到 {edge.to_node_name} 的边实例',
                    )

        await self.db.flush()

    async def create_variables(
        self,
        workflow_def_id: int,
        variables: List[Tuple[str, str]],
        node_variables: Dict[str, List[Tuple[str, str]]] = None,
    ):
        """创建工作流和节点变量"""
        workflow_instance = self.instance_map[workflow_def_id]

        # 创建工作流级别的变量
        for name, value in variables:
            var = InstanceVariable(
                workflow_instance_id=workflow_instance.id,
                name=name,
                value=value,
            )
            self.db.add(var)

        # 创建节点级别的变量
        if node_variables:
            for node_name, vars in node_variables.items():
                # 查找节点实例
                result = await self.db.execute(
                    text(
                        """
                        SELECT ni.id
                        FROM workflow_node_instances ni
                        JOIN workflow_node_definitions nd ON ni.node_definition_id = nd.id
                        WHERE nd.name = :node_name AND ni.workflow_instance_id = :wf_instance_id
                    """
                    ),
                    {
                        'node_name': node_name,
                        'wf_instance_id': workflow_instance.id,
                    },
                )
                node_instance = result.fetchone()

                if node_instance:
                    for var_name, var_value in vars:
                        var = InstanceVariable(
                            workflow_instance_id=workflow_instance.id,
                            node_instance_id=node_instance.id,
                            name=var_name,
                            value=var_value,
                        )
                        self.db.add(var)

    async def create_approvals(
        self, workflow_def_id: int, approvals: List[Tuple[str, str, str, str]]
    ):
        """创建审批记录"""
        workflow_instance = self.instance_map[workflow_def_id]

        for node_name, approver, status, comments in approvals:
            # 查找节点实例
            result = await self.db.execute(
                text(
                    """
                    SELECT ni.id
                    FROM workflow_node_instances ni
                    JOIN workflow_node_definitions nd ON ni.node_definition_id = nd.id
                    WHERE nd.name = :node_name AND ni.workflow_instance_id = :wf_instance_id
                """
                ),
                {
                    'node_name': node_name,
                    'wf_instance_id': workflow_instance.id,
                },
            )
            node_instance = result.fetchone()

            if node_instance:
                approval = ApprovalRecord(
                    node_instance_id=node_instance.id,
                    approved_by=approver,
                    approval_status=status,
                    comments=comments,
                )
                self.db.add(approval)

    async def create_change_log(
        self, instance_id: int, changed_by: str, change_type: str, details: str
    ):
        """创建变更日志"""
        log = ChangeLog(
            workflow_instance_id=instance_id,
            changed_by=changed_by,
            change_type=change_type,
            change_details=details,
        )
        self.db.add(log)

    async def link_subprocess(
        self,
        parent_workflow_def_id: int,
        parent_node_name: str,
        subprocess_instance_id: int,
    ):
        """关联子流程"""
        await self.db.execute(
            text(
                """
                UPDATE workflow_node_instances ni
                JOIN workflow_node_definitions nd ON ni.node_definition_id = nd.id
                SET ni.subprocess_instance_id = :subprocess_id
                WHERE nd.name = :node_name
                AND ni.workflow_instance_id = :parent_instance_id
            """
            ),
            {
                'subprocess_id': subprocess_instance_id,
                'node_name': parent_node_name,
                'parent_instance_id': self.instance_map[
                    parent_workflow_def_id
                ].id,
            },
        )


async def initialize_workflow_instance(db: AsyncSession):
    """初始化完整的工作流实例"""
    try:
        builder = WorkflowInstanceBuilder(db)
        current_time = datetime.now()
        instance_ids = {}  # 用于存储所有实例ID

        # 获取最新版本的工作流定义
        workflow_map = await get_latest_workflows(db)

        # 1. 创建主流程实例
        main_instance = await builder.create_workflow_instance(
            workflow_def_id=workflow_map['卷绕数据管理工作流主流程'],
            status=WorkflowStatus.active,
            start_time=current_time,
            creator='张工',
            project_id='PRJ-20240323-001',
        )
        instance_ids['main'] = main_instance.id  # 立即获取并存储ID

        # 创建主流程的节点实例
        await builder.create_node_instances(
            workflow_map['卷绕数据管理工作流主流程'],
            {
                '设备预研': NodeStatus.completed,
                '总装部件梳理': NodeStatus.completed,
                '部件伺服信息录入': NodeStatus.completed,
                '部件完整信息录入': NodeStatus.active,
                '气路设计': NodeStatus.completed,
            },
            '张工',
        )

        # 创建主流程的边实例
        await builder.create_edge_instances(
            workflow_map['卷绕数据管理工作流主流程'], current_time
        )

        # # 创建主流程的变量
        # await builder.create_variables(
        #     workflow_map['卷绕数据管理工作流主流程'],
        #     [
        #         ('设备名称', '卷绕机-XYZ-001'),
        #         ('设备类型', '标准型'),
        #         ('项目编号', 'PRJ-20240323-001'),
        #     ],
        #     {
        #         '设备预研': [('预研报告编号', 'YY-2024-001')],
        #         '总装部件梳理': [('部件清单版本', 'V1.0')],
        #     },
        # )

        # 创建主流程的审批记录
        await builder.create_approvals(
            workflow_map['卷绕数据管理工作流主流程'],
            [
                ('设备预研', '张工', 'approved', '设备预研方案已确认'),
                ('总装部件梳理', '李工', 'approved', '部件清单已确认'),
            ],
        )

        # # 2. 创建并关联子流程实例
        # # 部件伺服信息录入子流程
        # servo_instance = await builder.create_workflow_instance(
        #     workflow_map['部件伺服信息录入'],
        #     WorkflowStatus.completed,
        #     current_time - timedelta(days=1),
        #     '李工',
        #     'PRJ-20240323-001',
        # )
        # instance_ids['servo'] = servo_instance.id  # 立即获取并存储ID

        # await builder.create_node_instances(
        #     workflow_map['部件伺服信息录入'],
        #     {
        #         '总装部件分配完成': NodeStatus.completed,
        #         '录入部件信息': NodeStatus.completed,
        #         '部件伺服和预留确认': NodeStatus.completed,
        #         '录入伺服信息': NodeStatus.completed,
        #         '伺服信息确认': NodeStatus.completed,
        #         '部件信息录入完成': NodeStatus.completed,
        #     },
        #     '李工',
        # )

        # await builder.create_edge_instances(
        #     workflow_map['部件伺服信息录入'], current_time - timedelta(days=1)
        # )

        # # 关联子流程
        # await builder.link_subprocess(
        #     workflow_map['卷绕数据管理工作流主流程'], '部件伺服信息录入', servo_instance.id
        # )

        # # 气路设计子流程
        # pneumatic_instance = await builder.create_workflow_instance(
        #     workflow_map['气路设计流程'],
        #     WorkflowStatus.active,
        #     current_time - timedelta(hours=12),
        #     '赵工',
        #     'PRJ-20240323-001',
        # )
        # instance_ids['pneumatic'] = pneumatic_instance.id  # 立即获取并存储ID

        # await builder.create_node_instances(
        #     workflow_map['气路设计流程'],
        #     {
        #         '机电沟通表入库': NodeStatus.completed,
        #         '拉取已提交气缸、真空、吹气': NodeStatus.completed,
        #         '气路设计': NodeStatus.active,
        #         '设计审核': NodeStatus.pending,
        #         '气路信息入库': NodeStatus.pending,
        #     },
        #     '赵工',
        # )

        # await builder.create_edge_instances(
        #     workflow_map['气路设计流程'], current_time - timedelta(hours=12)
        # )

        # # 关联子流程
        # await builder.link_subprocess(
        #     workflow_map['卷绕数据管理工作流主流程'], '气路设计', pneumatic_instance.id
        # )

        # # 3. 创建部件完整信息录入子流程实例
        # hardware_instance = await builder.create_workflow_instance(
        #     workflow_map['部件完整信息录入'],
        #     WorkflowStatus.active,
        #     current_time - timedelta(hours=6),
        #     '王工',
        #     'PRJ-20240323-001',
        # )
        # instance_ids['hardware'] = hardware_instance.id  # 立即获取并存储ID

        # await builder.create_node_instances(
        #     workflow_map['部件完整信息录入'],
        #     {
        #         '开始录入其他硬件信息': NodeStatus.completed,
        #         '录入其他硬件信息': NodeStatus.active,
        #         '信息录入完成': NodeStatus.pending,
        #         '部件信息录入完成': NodeStatus.pending,
        #     },
        #     '王工',
        # )

        # await builder.create_edge_instances(
        #     workflow_map['部件完整信息录入'], current_time - timedelta(hours=6)
        # )

        # await builder.create_variables(
        #     workflow_map['部件完整信息录入'],
        #     [('部件总数', '45'), ('已录入数量', '28')],
        #     {'录入其他硬件信息': [('当前录入部件', '传感器组件'), ('剩余未录部件', '17')]},
        # )

        # # 关联子流程
        # await builder.link_subprocess(
        #     workflow_map['卷绕数据管理工作流主流程'], '部件完整信息录入', hardware_instance.id
        # )

        # 4. 创建伺服图纸子流程实例
        servo_drawing_instance = await builder.create_workflow_instance(
            workflow_map['伺服图纸自动生成流程'],
            WorkflowStatus.active,
            current_time - timedelta(hours=2),
            '张工',
            'PRJ-20240323-001',
        )
        instance_ids['servo_drawing'] = servo_drawing_instance.id  # 立即获取并存储ID

        await builder.create_node_instances(
            workflow_map['伺服图纸自动生成流程'],
            {
                '伺服沟通表完成': NodeStatus.completed,
                '生成驱动器选型表': NodeStatus.active,
                '生成EtherCat伺服节点': NodeStatus.active,
                '生成伺服图纸': NodeStatus.pending,
                '底板图制作': NodeStatus.pending,
                '底板图点检': NodeStatus.pending,
                '底板排版、钻孔': NodeStatus.pending,
                '机械确认底板图': NodeStatus.pending,
                '钻孔图完成': NodeStatus.pending,
            },
            '张工',
        )

        await builder.create_edge_instances(
            workflow_map['伺服图纸自动生成流程'], current_time - timedelta(hours=2)
        )

        # 关联子流程
        await builder.link_subprocess(
            workflow_map['卷绕数据管理工作流主流程'],
            '伺服图纸自动生成钻孔图',
            servo_drawing_instance.id,
        )

        await builder.create_variables(
            workflow_map['伺服图纸自动生成流程'],
            [('图纸编号', 'DWG-2024-001'), ('伺服总数', '12')],
            {
                '生成驱动器选型表': [
                    ('选型表版本', 'V1.0'),
                    (
                        '驱动器型号清单',
                        '[{"型号":"R88D-KN04H","数量":4},{"型号":"R88D-KN08H","数量":8}]',
                    ),
                ]
            },
        )

        # 5. 创建原理图子流程实例
        schematic_instance = await builder.create_workflow_instance(
            workflow_map['原理图纸自动生成流程'],
            WorkflowStatus.active,
            current_time - timedelta(hours=1),
            '李工',
            'PRJ-20240323-001',
        )
        instance_ids['schematic'] = schematic_instance.id  # 立即获取并存储ID

        await builder.create_node_instances(
            workflow_map['原理图纸自动生成流程'],
            {
                'IO表入库': NodeStatus.pending,
                '生成完整选型表': NodeStatus.pending,
                '填写线缆长度': NodeStatus.pending,
                '生成原理图': NodeStatus.pending,
                '比对沟通表和EPLAN': NodeStatus.pending,
                '投料': NodeStatus.pending,
                '比对BOM数据': NodeStatus.pending,
                '图纸完成下发': NodeStatus.pending,
                '是否批量机>5台': NodeStatus.pending,
                '柜内布线': NodeStatus.pending,
                '柜外布线': NodeStatus.pending,
                '终版底板图': NodeStatus.pending,
                '图纸完成': NodeStatus.pending,
            },
            '李工',
        )

        await builder.create_edge_instances(
            workflow_map['原理图纸自动生成流程'], current_time - timedelta(hours=1)
        )

        # 关联子流程
        await builder.link_subprocess(
            workflow_map['卷绕数据管理工作流主流程'],
            '原理图纸自动生成投料和图纸下发',
            schematic_instance.id,
        )

        await builder.create_variables(
            workflow_map['原理图纸自动生成流程'],
            [('原理图编号', 'SCH-2024-001'), ('批次编号', 'BATCH-2024-001')],
            {'生成完整选型表': [('选型表状态', '生成中'), ('完成百分比', '65')]},
        )

        # 创建变更日志
        change_logs = [
            (main_instance.id, '系统', 'workflow_started', '主流程启动'),
            # (servo_instance.id, '李工', 'subprocess_completed', '部件伺服信息录入完成'),
            # (hardware_instance.id, '王工', 'node_status_changed', '开始录入硬件信息'),
            # (pneumatic_instance.id, '赵工', 'node_status_changed', '气路设计进行中'),
            (
                servo_drawing_instance.id,
                '张工',
                'node_status_changed',
                '开始生成伺服图纸',
            ),
            (schematic_instance.id, '李工', 'node_status_changed', '开始生成原理图'),
        ]

        for instance_id, changed_by, change_type, details in change_logs:
            await builder.create_change_log(
                instance_id, changed_by, change_type, details
            )

        await db.commit()
        print('工作流实例初始化完成')
        return instance_ids

    except Exception as e:
        await db.rollback()
        print(f'初始化工作流实例时发生错误: {str(e)}')
        raise


async def run_initialization():
    """运行初始化程序"""
    async with AsyncSession(engine) as db:
        try:
            # 确保表已创建
            await create_tables()

            # 初始化工作流实例
            instance_ids = await initialize_workflow_instance(db)

            print('工作流实例初始化成功')
            print('实例ID映射:', instance_ids)

        except Exception as e:
            print(f'初始化过程中发生错误: {str(e)}')
            raise
        finally:
            await engine.dispose()


if __name__ == '__main__':
    asyncio.run(run_initialization())
