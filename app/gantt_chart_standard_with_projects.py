import os
import sys

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)

# 获取当前脚本的上层目录
parent_dir = os.path.dirname(os.path.dirname(current_script_path))

# 将上层目录添加到 Python 路径中
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload
from datetime import datetime, timedelta
import random
import math
from typing import Dict, List, Set, Tuple
import colorsys
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
    MachineTypeDuration,
)
from database.base import engine
from database.crud.info_management import info
from schemas import NodeStatus, DeviceType
from utils.tools import (
    get_full_node_name,
    get_node_color,
    calculate_column_width,
)
from utils.gantt_chart_standard_generator import (
    calculate_node_start_times,
    find_workflow_paths,
)


def get_hour_column(time: datetime, project_start: datetime) -> int:
    """
    将时间转换为对应的列号
    每天12列（每小时一列），从8:30-20:30
    返回: 整体列号（考虑天数）
    """
    # 计算天数差
    days_diff = (time.date() - project_start.date()).days

    # 获取当天内的时间位置
    base_time = time.replace(hour=8, minute=30, second=0, microsecond=0)
    if time.hour < 8 or (time.hour == 8 and time.minute < 30):
        # 如果时间早于8:30，使用当天的第一列
        hour_col = 0
    elif time.hour > 20 or (time.hour == 20 and time.minute > 30):
        # 如果时间晚于20:30，使用当天的最后一列
        hour_col = 11
    else:
        # 计算当天内的列位置
        diff_hours = (time - base_time).total_seconds() / 3600  # 转换为小时数
        hour_col = int(diff_hours)
        hour_col = min(hour_col, 11)  # 确保不超过当天的最后一列

    # 返回总列数（每天12列 * 天数 + 当天列位置）
    return days_diff * 12 + hour_col


def calculate_project_timeline(
    nodes: List[NodeInstance],
) -> Tuple[datetime, int]:
    """
    计算项目的开始时间和总持续天数
    返回: (项目开始时间, 总持续天数)
    """
    start_times = [n.start_time for n in nodes if n.start_time]
    if not start_times:
        return datetime.now(), 0

    project_start = min(start_times)
    current_date = datetime.now()

    # 调整到8:30
    project_start = project_start.replace(
        hour=8, minute=30, second=0, microsecond=0
    )

    # 计算持续天数
    duration = (current_date.date() - project_start.date()).days + 1
    return project_start, duration


def calculate_node_hours(
    node: NodeInstance, project_start: datetime
) -> Tuple[int, int]:
    """
    计算节点的开始和结束列
    返回: (开始列, 结束列)
    """
    if not node.start_time:
        return 0, 0

    # 确保结束时间大于开始时间
    end_time = node.end_time or datetime.now()
    if node.end_time and node.start_time > node.end_time:
        end_time = node.start_time

    start_col = get_hour_column(node.start_time, project_start)
    end_col = get_hour_column(end_time, project_start)

    return start_col, end_col


def calculate_node_relative_days(
    node: NodeInstance, project_start: datetime
) -> Tuple[int, int]:
    """
    计算节点相对于项目开始的天数
    返回: (开始天数, 结束天数)
    """
    if not node.start_time:
        return 0, 0

    start_day = (node.start_time - project_start).days + 1

    if node.end_time:
        end_day = (node.end_time - project_start).days + 1
    else:
        end_day = (datetime.now() - project_start).days + 1

    return start_day, end_day


def generate_distinct_colors(n: int) -> List[str]:
    """生成n个视觉上区分度高的颜色"""
    colors = []
    for i in range(n):
        hue = (i * 0.618033988749895) % 1
        saturation = 0.4 + random.random() * 0.2
        value = 0.8 + random.random() * 0.1

        rgb = colorsys.hsv_to_rgb(hue, saturation, value)
        hex_color = '%02x%02x%02x' % tuple(int(x * 255) for x in rgb)
        colors.append(hex_color.upper())

    return colors


def is_time_overlap(
    start1: datetime, end1: datetime, start2: datetime, end2: datetime
) -> bool:
    """判断两个时间段是否重叠"""
    current_time = datetime.now()
    end1 = end1 or current_time
    end2 = end2 or current_time
    return start1 < end2 and start2 < end1


def find_parallel_paths(
    node_id: int, edges: List[EdgeInstance], nodes: Dict[int, NodeInstance]
) -> List[List[int]]:
    """查找从给定节点开始的所有并行路径"""

    def dfs(
        current_id: int, current_path: List[int], all_paths: List[List[int]]
    ):
        current_path.append(current_id)
        next_nodes = [
            e.to_node_instance_id
            for e in edges
            if e.from_node_instance_id == current_id
        ]

        if not next_nodes:
            all_paths.append(current_path[:])
        else:
            for next_node in next_nodes:
                dfs(next_node, current_path[:], all_paths)

    all_paths = []
    dfs(node_id, [], all_paths)
    return all_paths


def assign_rows(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Dict[int, int]:
    """为节点分配行号，确保重叠的任务不会在同一行"""
    node_map = {node.id: node for node in nodes}
    node_rows = {}

    # 找到所有起始节点
    start_nodes = [
        n
        for n in nodes
        if not any(e.to_node_instance_id == n.id for e in edges)
    ]

    # 获取所有执行路径
    all_paths = []
    for start_node in start_nodes:
        paths = find_parallel_paths(start_node.id, edges, node_map)
        all_paths.extend(paths)

    # 创建节点到路径的映射
    node_to_paths = {node.id: [] for node in nodes}
    for path in all_paths:
        for node_id in path:
            node_to_paths[node_id].append(path)

    # 按开始时间排序节点
    sorted_nodes = sorted(
        [n for n in nodes if n.start_time], key=lambda x: x.start_time.date()
    )

    def get_path_nodes(node_id: int) -> Set[int]:
        """获取与当前节点在同一路径上的所有节点ID"""
        path_nodes = set()
        for path in node_to_paths[node_id]:
            path_nodes.update(path)
        return path_nodes

    # 为每个节点分配行号
    for node in sorted_nodes:
        # 获取与当前节点在同一路径上的所有节点
        path_nodes = get_path_nodes(node.id)

        # 查找可用的行号
        row = 0
        while True:
            row += 1
            can_use_row = True

            # 检查已分配行号的节点
            for other_node_id, assigned_row in node_rows.items():
                other_node = node_map[other_node_id]

                # 如果是同一路径上的节点，或者时间有重叠，就不能用相同的行
                if assigned_row == row and (
                    other_node_id in path_nodes
                    or (
                        other_node.start_time
                        and is_time_overlap(  # 确保节点有开始时间
                            node.start_time,
                            node.end_time,
                            other_node.start_time,
                            other_node.end_time,
                        )
                    )
                ):
                    can_use_row = False
                    break

            if can_use_row:
                break

        node_rows[node.id] = row

    return node_rows


def adjust_times_by_rows(
    nodes: List[NodeInstance],
    edges: List[EdgeInstance],
    node_rows: Dict[int, int],
) -> Dict[int, datetime]:
    """根据行号调整节点时间"""
    adjusted_start_times = {}

    # 按行号和开始时间排序节点
    sorted_nodes = sorted(
        [(n, node_rows.get(n.id, 0)) for n in nodes if n.start_time],
        key=lambda x: (x[1], x[0].start_time.date()),
    )

    # 记录每行最后的结束时间
    row_end_times = {}

    # 调整每个节点的时间
    for node, row in sorted_nodes:
        # 获取当前行的最后结束时间
        last_end_date = row_end_times.get(row)

        # 确定开始日期
        start_date = node.start_time.date()
        if last_end_date and start_date <= last_end_date:
            # 如果开始时间早于或等于该行最后结束时间，调整到下一天
            start_date = last_end_date + timedelta(days=1)

        # 检查调整后的开始时间是否可行
        if not node.end_time or start_date <= node.end_time.date():
            adjusted_start = datetime.combine(start_date, datetime.min.time())
            adjusted_start_times[node.id] = adjusted_start

            # 更新该行的最后结束时间
            if node.end_time:
                row_end_times[row] = node.end_time.date()
            else:
                row_end_times[row] = datetime.now().date()

    return adjusted_start_times


async def create_gantt_chart(
    session: AsyncSession, output_file: str, project_ids: List[str] = None
):
    """创建甘特图Excel文件"""
    try:
        wb = load_workbook(output_file)
    except FileNotFoundError:
        # 如果文件不存在，创建新的
        wb = Workbook()

    # 检查是否已存在同名sheet，如果存在则添加数字后缀
    sheet_name = '项目周期对比表'
    base_name = sheet_name
    counter = 1
    while sheet_name in wb.sheetnames:
        sheet_name = f'{base_name}_{counter}'
        counter += 1

    # 创建新的sheet
    ws = wb.create_sheet(sheet_name)

    # 获取模板工作流和相关数据
    template_workflow = (
        await session.execute(
            select(WorkflowInstance)
            .join(WorkflowDefinition)
            .filter(WorkflowDefinition.is_subprocess == False)
            .limit(1)
        )
    ).scalar_one_or_none()

    if not template_workflow:
        print('未找到模板工作流')
        return

    # 获取节点和边
    template_nodes = (
        (
            await session.execute(
                select(NodeInstance).filter(
                    NodeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    template_edges = (
        (
            await session.execute(
                select(EdgeInstance).filter(
                    EdgeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    # 获取路径信息
    template_graph, template_paths, template_node_rows = find_workflow_paths(
        template_nodes, template_edges
    )

    # 获取节点持续时间
    node_durations = (
        (
            await session.execute(
                select(MachineTypeDuration)
                .join(NodeDefinition)
                .filter(NodeDefinition.workflow_definition_id == 1)
                .options(joinedload(MachineTypeDuration.node_definition))
            )
        )
        .scalars()
        .all()
    )

    # 按机型和设备类型处理持续时间，将分钟转换为小时（向上取整）
    machine_types = set(d.machine_type for d in node_durations)
    device_types = set(d.device_type for d in node_durations)
    duration_maps = {}  # {machine_type: {node_def_id: duration_in_hours}}
    max_duration_hours = 0

    for machine_type in machine_types:
        for device_type in device_types:
            durations = {}
            for duration in node_durations:
                if (
                    duration.machine_type == machine_type
                    and duration.device_type == device_type
                ):
                    # 转换分钟为小时并向上取整
                    hours = math.ceil((duration.expected_duration or 0) / 60)
                    durations[duration.node_definition_id] = hours

            device_type_name = (
                '' if device_type == DeviceType.main_machine else '(出料机)'
            )
            duration_maps[f'{machine_type}{device_type_name}'] = durations

            # 计算该机型的最长结束时间（小时）
            start_times = calculate_node_start_times(
                template_graph,
                durations,
                {node.id: node for node in template_nodes},
            )

            for node_def_id, duration in durations.items():
                if duration > 0:
                    node_instance = next(
                        node
                        for node in template_nodes
                        if node.node_definition_id == node_def_id
                    )
                    node_end_time = start_times[node_instance.id] + duration
                    max_duration_hours = max(max_duration_hours, node_end_time)

    # 添加缓冲时间并转换为天数
    max_duration_hours += 10  # 添加10小时缓冲
    max_duration_days = math.ceil(max_duration_hours / 10)

    # 获取主流程工作流
    query = (
        select(WorkflowInstance)
        .join(WorkflowDefinition)
        .filter(WorkflowDefinition.is_subprocess == False)
    )

    # 如果指定了项目ID列表，添加筛选条件
    if project_ids:
        query = query.filter(WorkflowInstance.project_id.in_(project_ids))

    # 获取主流程工作流
    workflows = (await session.execute(query)).scalars().all()

    # 获取所有唯一的节点名称和定义
    all_node_names = set()
    node_defs_map = {}

    # 计算所有项目中最长的持续天数
    max_duration = 0
    workflow_timelines = {}  # 存储每个工作流的时间信息

    for workflow in workflows:

        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        for node in nodes:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            all_node_names.add(node_def.name)
            node_defs_map[node.node_definition_id] = node_def

        # 检查是否所有节点都是completed状态
        all_completed = all(
            node.status == NodeStatus.completed for node in nodes
        )
        if all_completed:
            continue

        # 检查工作流是否包含时间信息
        has_times = any(node.start_time or node.end_time for node in nodes)
        if not has_times:
            continue

        # 计算每个工作流的时间线
        project_start, duration = calculate_project_timeline(nodes)
        workflow_timelines[workflow.id] = {
            'start': project_start,
            'duration': duration,
            'nodes': nodes,
        }
        max_duration = max(max_duration, duration)

    # 设置表头样式
    cell_width = 0.5
    ws.column_dimensions['A'].width = 15  # 项目名称列
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)

    # 写入表头
    ws['A1'] = '项目名称'
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font

    # 写入天数表头，每天合并12列（每小时一列）
    max_days = max_duration if 'max_duration' in locals() else 30  # 设置默认最大天数

    max_days = max(max_days, max_duration_days)

    for day in range(max_days):
        # 计算每天的起始和结束列
        start_col = day * 12 + 2  # +2 是因为A列是项目名称
        end_col = start_col + 11  # 每天12列

        # 设置所有列的宽度
        for col in range(start_col, end_col + 1):
            col_letter = get_column_letter(col)
            ws.column_dimensions[col_letter].width = cell_width

        # 合并当天的所有列并设置天数
        start_letter = get_column_letter(start_col)
        end_letter = get_column_letter(end_col)
        ws.merge_cells(f'{start_letter}1:{end_letter}1')

        # 设置天数
        cell = ws[f'{start_letter}1']
        cell.value = f'D{day + 1}'
        cell.alignment = Alignment(horizontal='center')
        cell.fill = header_fill
        cell.font = header_font

    current_row = 2
    erp_info = await info.get_erp_info_dict(session)

    column_widths = []

    standard_plots = []

    # 为每个工作流实例创建甘特图
    for workflow in workflows:
        if workflow.id not in workflow_timelines:
            continue

        timeline = workflow_timelines[workflow.id]
        nodes = timeline['nodes']
        project_start = timeline['start']

        edges = (
            (
                await session.execute(
                    select(EdgeInstance).filter(
                        EdgeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        device_type = (
            '' if workflow.device_type == DeviceType.main_machine else '(出料机)'
        )
        if f'{workflow.machine_type}{device_type}' not in standard_plots:
            standard_plots.append(f'{workflow.machine_type}{device_type}')
            machine_type = f'{workflow.machine_type}{device_type}'
            if machine_type in duration_maps:
                durations = duration_maps[machine_type]
                if any(durations.values()):
                    # 计算该机型下所有节点的开始时间（小时）
                    start_times = calculate_node_start_times(
                        template_graph,
                        durations,
                        {node.id: node for node in template_nodes},
                    )

                    max_row_num = (
                        max(template_node_rows.values())
                        if template_node_rows
                        else 1
                    )

                    # 写入机型名称
                    ws[f'A{current_row}'] = machine_type
                    ws[f'A{current_row}'].font = Font(bold=True)
                    ws[f'A{current_row}'].alignment = Alignment(
                        vertical='center'
                    )

                    column_width = calculate_column_width(machine_type)
                    column_widths.append(column_width)

                    if max_row_num > 1:
                        ws.merge_cells(
                            f'A{current_row}:A{current_row + max_row_num - 1}'
                        )
                        ws[f'A{current_row}'].alignment = Alignment(
                            vertical='center'
                        )

                    # 绘制甘特图
                    for node_def_id, duration in durations.items():
                        if duration > 0:
                            row = (
                                current_row
                                + template_node_rows[node_def_id]
                                - 1
                            )

                            # 获取节点实例
                            node_instance = next(
                                node
                                for node in template_nodes
                                if node.node_definition_id == node_def_id
                            )

                            # 计算开始和结束列（基于小时）
                            start_col = (
                                2 + start_times[node_instance.id]
                            )  # B列开始
                            end_col = start_col + duration - 1

                            start_letter = get_column_letter(start_col)
                            end_letter = get_column_letter(end_col)

                            # 获取节点定义和设置样式
                            node_def = await session.get(
                                NodeDefinition, node_def_id
                            )
                            color = get_node_color(node_def.name)

                            # 合并单元格
                            cell_range = (
                                f'{start_letter}{row}:{end_letter}{row}'
                            )
                            ws.merge_cells(cell_range)

                            # 设置单元格样式和内容
                            cell = ws[f'{start_letter}{row}']
                            cell.value = f'{get_full_node_name(node_def.name)} ({duration / 10}d)'
                            cell.fill = PatternFill(
                                start_color=color,
                                end_color=color,
                                fill_type='solid',
                            )
                            cell.alignment = Alignment(
                                horizontal='left', vertical='center'
                            )

                    current_row += max_row_num + 1  # 添加空行分隔不同机型

        erp_row = erp_info[workflow.project_id]

        # 为任务分配行号
        node_rows = assign_rows(nodes, edges)
        max_row_num = max(node_rows.values()) if node_rows else 1

        # 写入工作流标题
        project_title = f'{workflow.project_id} {erp_row["customer"]} {erp_row["model"]}{device_type}'
        ws[f'A{current_row}'] = project_title
        ws[f'A{current_row}'].font = Font(bold=True)
        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')
        # 计算并设置列宽
        column_width = calculate_column_width(project_title)
        column_widths.append(column_width)

        # 绘制每个节点的甘特图条
        for node in nodes:
            if node.start_time:
                node_def = node_defs_map[node.node_definition_id]
                row = current_row + node_rows[node.id] - 1

                # 使用新的小时计算函数
                start_col, end_col = calculate_node_hours(node, project_start)

                # 确保至少有一个格子被涂色
                end_col = max(start_col, end_col)

                start_letter = get_column_letter(start_col + 2)  # +2 因为A列是项目名称
                end_letter = get_column_letter(end_col + 2)

                # 为整个范围的单元格设置填充色
                for col in range(start_col + 2, end_col + 3):
                    col_letter = get_column_letter(col)
                    cell = ws[f'{col_letter}{row}']
                    color = get_node_color(node_def.name)
                    cell.fill = PatternFill(
                        start_color=color, end_color=color, fill_type='solid'
                    )

                # 合并单元格并设置任务名称
                cell_range = f'{start_letter}{row}:{end_letter}{row}'
                ws.merge_cells(cell_range)

                # 设置任务名称和对齐方式
                ws[f'{start_letter}{row}'] = get_full_node_name(node_def.name)
                ws[f'{start_letter}{row}'].alignment = Alignment(
                    horizontal='left', vertical='center'
                )

        current_row += max_row_num + 1
    ws.column_dimensions['A'].width = max(column_widths)  # 项目名称列
    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    for row in ws.iter_rows(min_row=1, max_row=current_row - 1):
        for cell in row:
            cell.border = thin_border

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_gantt_chart(session, 'tests/project_gantt_chart.xlsx')
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    import asyncio

    asyncio.run(generate_project_gantt_chart())
