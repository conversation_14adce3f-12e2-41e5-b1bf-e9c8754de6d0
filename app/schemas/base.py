from enum import Enum


class WorkflowStatus(str, Enum):
    active = 'active'
    completed = 'completed'
    suspended = 'suspended'
    terminated = 'terminated'


class NodeStatus(str, Enum):
    pending = 'pending'
    active = 'active'
    completed = 'completed'
    error = 'error'
    overdue = 'overdue'


class NodeType(str, Enum):
    start = 'start'
    end = 'end'
    process = 'process'
    decision = 'decision'
    subprocess = 'subprocess'


class EdgeCondition(str, Enum):
    yes = 'yes'
    no = 'no'
    default = 'default'


class TaskStatus(str, Enum):
    pending = 'pending'
    in_progress = 'in_progress'
    completed = 'completed'
    cancelled = 'cancelled'


class StatusUpdateMode(str, Enum):
    manual = 'manual'
    polling = 'polling'
    callback = 'callback'


class InputLogicType(str, Enum):
    AND = 'and'
    OR = 'or'


class ApprovalStatus(str, Enum):
    approved = 'approved'
    rejected = 'rejected'
    # pending = 'pending'


class DeviceType(str, Enum):
    main_machine = 'main_machine'
    output_machine = 'output_machine'
