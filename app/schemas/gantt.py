from pydantic import BaseModel
from typing import Optional, List, Dict, Any


class GanttTask(BaseModel):
    id: str
    text: str
    start_date: str
    end_date: str
    type: str
    progress: float
    status: str
    parent: Optional[str] = None
    assigned_to: Optional[str] = None
    color: Optional[str] = None
    duration: Optional[int] = None
    duration_unit: Optional[str] = None
    need_approval: Optional[bool] = None
    expected_duration: Optional[int] = None


class GanttLink(BaseModel):
    id: str
    source: str
    target: str
    type: str


class GanttSettings(BaseModel):
    project_id: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None


class GanttDataResponse(BaseModel):
    data: List[Dict[str, Any]]
    links: List[Dict[str, Any]]
    settings: GanttSettings


class ProjectInfo(BaseModel):
    project_id: str
    customer: str
    model: str
    device_type: str
    status: str
    current_max_step: Any
    blocking_steps: List


class ProjectListResponse(BaseModel):
    total: int
    projects: List[ProjectInfo]


class ProjectStageStatus(BaseModel):
    """项目阶段状态"""

    project_id: str
    customer: str
    model: str
    stages: List[str]  # 所有机器的阶段列表
    total_machines: Optional[int] = None
    description: Optional[str] = None  # 项目描述信息
    error: Optional[str] = None


class ProjectStatusListResponse(BaseModel):
    """项目状态列表响应"""

    total: int
    success_count: int
    error_count: int
    projects: List[ProjectStageStatus]
