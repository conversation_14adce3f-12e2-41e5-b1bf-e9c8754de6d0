from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from schemas.base import WorkflowStatus, NodeStatus, DeviceType
from schemas.workflow import WorkflowDefinitionInfo
from schemas.node import NodeDefinition, EdgeDefinition


class WorkflowInstanceBase(BaseModel):
    workflow_definition_id: int = Field(..., gt=0)
    created_by: Optional[str] = Field(None, max_length=255)
    project_id: Optional[str] = Field(None, max_length=50)
    device_type: DeviceType = Field(default=DeviceType.main_machine)


class WorkflowInstanceCreate(WorkflowInstanceBase):
    status: WorkflowStatus = Field(default=WorkflowStatus.active)
    start_time: datetime = Field(default_factory=datetime.utcnow)
    device_type: DeviceType = Field(default=DeviceType.main_machine)

    class Config:
        schema_extra = {
            'example': {
                'workflow_definition_id': 1,
                'created_by': '<EMAIL>',
                'project_id': 'PRJ001',
                'status': 'active',
                'start_time': '2023-06-01T12:00:00',
                'device_type': 'main_machine',
            }
        }


class NodeInstanceBase(BaseModel):
    node_definition_id: int
    workflow_instance_id: int
    name: Optional[str] = None
    assigned_to: Optional[str] = None
    notification_frequency: Optional[str] = None
    status: NodeStatus = NodeStatus.pending
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    actual_duration: Optional[int] = None


class NodeInstanceCreate(NodeInstanceBase):
    pass


class NodeInstance(NodeInstanceBase):
    id: int
    subprocess_instance_id: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]
    # 添加节点定义的基本信息
    definition: NodeDefinition

    class Config:
        from_attributes = True


class EdgeInstanceBase(BaseModel):
    edge_definition_id: int
    from_node_instance_id: int
    to_node_instance_id: int
    transition_time: Optional[datetime]


class EdgeInstance(EdgeInstanceBase):
    id: int
    workflow_instance_id: int
    created_at: datetime
    definition: EdgeDefinition

    class Config:
        from_attributes = True


class EdgeInstanceCreate(EdgeInstanceBase):
    workflow_instance_id: int


class WorkflowInstance(WorkflowInstanceBase):
    id: int
    workflow_definition_id: int
    start_time: datetime
    end_time: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    status: WorkflowStatus
    definition: WorkflowDefinitionInfo
    nodes: List[NodeInstance] = []
    edges: List[EdgeInstance] = []

    class Config:
        from_attributes = True


class WorkflowInstanceInfo(WorkflowInstanceBase):
    id: int
    workflow_definition_id: int
    start_time: datetime
    end_time: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    definition: WorkflowDefinitionInfo
    status: WorkflowStatus

    class Config:
        from_attributes = True


class InstanceVariableBase(BaseModel):
    name: str
    value: str


class InstanceVariableCreate(InstanceVariableBase):
    workflow_instance_id: int
    node_instance_id: Optional[int] = None


class InstanceVariable(InstanceVariableBase):
    id: int
    workflow_instance_id: int
    node_instance_id: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class NodeInstanceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    assigned_to: Optional[str] = None
    status: Optional[NodeStatus] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    actual_duration: Optional[int] = None
    notification_frequency: Optional[str] = None
