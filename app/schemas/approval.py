from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from schemas.base import ApprovalStatus


class ApprovalRecordBase(BaseModel):
    node_instance_id: Optional[int]
    approved_by: str
    approval_status: ApprovalStatus
    comments: Optional[str] = None


class SimulationDataBase(BaseModel):
    """仿真数据基础模型"""

    simulation_efficiency: Optional[str] = None
    mechanical_issues: Optional[str] = None
    program_issues: Optional[str] = None
    remarks: Optional[str] = None
    # 新增仿真三维搭建相关字段
    simulation_3d_responsible: Optional[str] = None
    simulation_3d_start_time: Optional[datetime] = None
    simulation_3d_end_time: Optional[datetime] = None


class ApprovalRecordCreate(ApprovalRecordBase):
    simulation_data: Optional[SimulationDataBase] = None


class ApprovalRecord(ApprovalRecordBase):
    id: int
    created_at: datetime
    node_status: Optional[str] = None

    class Config:
        orm_mode = True
