from pydantic import BaseModel
from enum import Enum
from typing import Optional, Dict, Any
from datetime import datetime
from schemas.base import NodeStatus


class TaskNotification(BaseModel):
    id: int
    workflow_instance_id: int
    node_definition_id: int
    subprocess_instance_id: Optional[int]
    status: NodeStatus
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    assigned_to: Optional[str]
    notification_frequency: Optional[str]
    last_notification_time: Optional[str]
    actual_duration: Optional[int]
    need_approval: Optional[int]
    device_type: Optional[str]
    name: str

    @classmethod
    def from_orm(cls, node_instance):
        return cls(
            id=node_instance.id,
            workflow_instance_id=node_instance.workflow_instance_id,
            node_definition_id=node_instance.node_definition_id,
            subprocess_instance_id=node_instance.subprocess_instance_id,
            status=node_instance.status,
            start_time=node_instance.start_time,
            end_time=node_instance.end_time,
            created_at=node_instance.created_at,
            updated_at=node_instance.updated_at,
            assigned_to=node_instance.assigned_to,
            notification_frequency=node_instance.notification_frequency,
            last_notification_time=node_instance.last_notification_time,
            actual_duration=node_instance.actual_duration,
            name=node_instance.definition.name,  # 从预加载的 definition 中获取名称
            need_approval=node_instance.definition.need_approval,
            device_type=node_instance.workflow.device_type,
        )

    class Config:
        from_attributes = True
        use_enum_values = True


class ExternalTaskAction(str, Enum):
    APPROVE = 'approve'
    REJECT = 'reject'
    COMPLETE = 'complete'
    ROLLBACK = 'rollback'
    REASSIGN = 'reassign'


class ExternalTaskMessage(BaseModel):
    project_id: str
    node_name: str
    action: ExternalTaskAction
    operator: str
    operator_name: str
    comments: Optional[str]
    assigned_to: Optional[str]
    timestamp: datetime
    extra_data: Optional[Dict[str, Any]]
