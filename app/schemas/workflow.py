from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

from schemas.node import NodeDefinition, EdgeDefinition


class WorkflowDefinitionBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    is_subprocess: bool = False


class WorkflowDefinitionCreate(WorkflowDefinitionBase):
    version: int = Field(1, ge=1)
    is_latest: bool = True

    class Config:
        schema_extra = {
            'example': {
                'name': 'Sample Workflow',
                'description': 'This is a sample workflow definition',
                'is_subprocess': False,
                'version': 1,
                'is_latest': True,
            }
        }


class WorkflowDefinition(WorkflowDefinitionBase):
    id: int
    version: int
    is_latest: bool
    created_at: datetime
    updated_at: Optional[datetime]
    nodes: List['NodeDefinition']
    edges: List['EdgeDefinition']

    class Config:
        from_attributes = True


class WorkflowDefinitionInfo(WorkflowDefinitionBase):
    id: int
    version: int
    is_latest: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True
