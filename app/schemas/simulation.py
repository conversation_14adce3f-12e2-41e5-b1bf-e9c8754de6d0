from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from .approval import SimulationDataBase


class SimulationDataCreate(SimulationDataBase):
    """创建仿真数据模型"""

    node_instance_id: int = Field(..., description='节点实例ID')
    created_by: Optional[str] = Field(None, description='创建人')
    project_id: str = Field(..., description='项目ID')


class SimulationDataUpdate(BaseModel):
    """更新仿真数据模型"""

    simulation_efficiency: Optional[str] = Field(None, description='仿真效率(PPM)')
    mechanical_issues: Optional[str] = Field(None, description='机械问题个数')
    program_issues: Optional[str] = Field(None, description='程序问题个数')
    remarks: Optional[str] = Field(None, description='备注信息')


class SimulationDataResponse(SimulationDataBase):
    """仿真数据响应模型"""

    id: int
    node_instance_id: int
    created_by: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SimulationApprovalRequest(BaseModel):
    """仿真节点审批请求模型"""

    # 审批信息
    approval_status: str = Field(..., description='审批状态（APPROVED/REJECTED）')
    comments: Optional[str] = Field(None, description='审批意见')
    approved_by: str = Field(..., description='审批人ID')

    # 仿真数据（可选）
    simulation_data: Optional[SimulationDataBase] = Field(
        None, description='仿真数据'
    )
