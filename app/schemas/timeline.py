from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class TimelineEvent(BaseModel):
    """时间线事件模型"""

    date: str
    type: str
    text: str
    category: str
    # 所有可选字段
    assigned_to: Optional[str] = None
    status: Optional[str] = None
    duration: Optional[float] = None

    class Config:
        # 允许额外字段
        extra = 'allow'

        # 示例数据
        schema_extra = {
            'example': {
                'date': '2024-10-31 09:45',
                'type': 'node_start',
                'text': "节点 '部件完整信息录入' 开始",
                'category': 'node',
                'assigned_to': '张工',
                'status': 'active',
                'duration': 120.5,
            }
        }


class TimelineResponse(BaseModel):
    workflow_id: int
    workflow_name: str
    timeline: List[TimelineEvent]
    status: str
    duration: Optional[float]

    class Config:
        schema_extra = {
            'example': {
                'workflow_id': 1,
                'workflow_name': 'Sample Workflow',
                'timeline': [
                    {
                        'date': '2023-06-01 12:00',
                        'type': 'workflow_start',
                        'text': '工作流 "Sample Workflow" 启动',
                        'category': 'workflow',
                    }
                ],
                'status': 'active',
                'duration': 48.5,
            }
        }


class ApprovalHistoryItem(BaseModel):
    approved_by: str
    status: str
    comments: Optional[str]
    created_at: datetime


class WorkflowInfo(BaseModel):
    workflow_id: int
    workflow_name: str
    project_id: str
    workflow_status: str
    workflow_start_time: datetime
    workflow_end_time: Optional[datetime]


class NodeDetailResponse(BaseModel):
    node_id: int
    name: str
    type: str
    status: str
    color: Optional[str]
    assigned_to: Optional[str]
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    expected_duration: Optional[float]
    actual_duration: Optional[float]
    task_url: Optional[str]
    need_approval: Optional[bool]
    approval_history: List[ApprovalHistoryItem]
    workflow_info: WorkflowInfo
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class PendingApproval(BaseModel):
    node_id: int = Field(..., description='节点ID')
    node_name: str = Field(..., description='节点名称')
    workflow_id: int = Field(..., description='工作流ID')
    workflow_name: str = Field(..., description='工作流名称')
    project_id: str = Field(..., description='项目ID')
    start_time: datetime = Field(..., description='任务开始时间')
    # submitter: str = Field(..., description='提交人')
    expected_duration: Optional[float] = Field(None, description='预期完成时间（小时）')
    # priority: Optional[str] = Field(None, description='优先级')
    # description: Optional[str] = Field(None, description='任务描述')
    task_url: Optional[str] = Field(None, description='任务链接')
    node_status: str = Field(..., description='节点状态')

    class Config:
        json_schema_extra = {
            'example': {
                'node_id': 100,
                'node_name': '需求评审',
                'workflow_id': 1,
                'workflow_name': '产品发布流程',
                'project_id': 'proj_001',
                'submit_time': '2024-01-01T12:00:00Z',
                # 'submitter': 'user_001',
                'expected_duration': 24.0,
                # 'priority': 'HIGH',
                # 'description': '评审新功能需求文档',
                'task_url': 'http://example.com/tasks/100',
                'node_status': 'active',
            }
        }
