from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class InspectionTemplateBase(BaseModel):
    """点检表模板基础模型"""

    type: str = Field(..., description='点检表类型')
    sequence_number: int = Field(..., description='序号')
    inspection_item: str = Field(..., description='点检项')
    area_function: Optional[str] = Field(None, description='区域/功能')
    category: Optional[str] = Field(None, description='类别')
    specific_plan: Optional[str] = Field(None, description='具体方案或接线点检')
    inspection_method: Optional[str] = Field(None, description='检查方式')
    is_active: bool = Field(True, description='是否启用')


class InspectionTemplateCreate(InspectionTemplateBase):
    """创建点检表模板"""

    created_by: Optional[str] = Field(None, description='创建人')


class InspectionTemplateUpdate(BaseModel):
    """更新点检表模板"""

    type: Optional[str] = Field(None, description='点检表类型')
    sequence_number: Optional[int] = Field(None, description='序号')
    inspection_item: Optional[str] = Field(None, description='点检项')
    area_function: Optional[str] = Field(None, description='区域/功能')
    category: Optional[str] = Field(None, description='类别')
    specific_plan: Optional[str] = Field(None, description='具体方案或接线点检')
    inspection_method: Optional[str] = Field(None, description='检查方式')
    is_active: Optional[bool] = Field(None, description='是否启用')


class InspectionTemplate(InspectionTemplateBase):
    """点检表模板响应模型"""

    id: int
    created_by: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class ProjectInspectionBase(BaseModel):
    """项目点检表基础模型"""

    project_id: str = Field(..., description='项目ID/ERP')
    type: str = Field(..., description='点检表类型')
    sequence_number: int = Field(..., description='序号')
    inspection_item: str = Field(..., description='点检项')
    area_function: Optional[str] = Field(None, description='区域/功能')
    category: Optional[str] = Field(None, description='类别')
    specific_plan: Optional[str] = Field(None, description='具体方案或接线点检')
    inspection_method: Optional[str] = Field(None, description='检查方式')
    self_check_result: Optional[str] = Field(None, description='自查结论')
    self_checker: Optional[str] = Field(None, description='自检人')
    audit_result: Optional[str] = Field(None, description='审核结论')
    auditor: Optional[str] = Field(None, description='审核人')
    is_completed: bool = Field(False, description='是否完成')


class ProjectInspectionCreate(ProjectInspectionBase):
    """创建项目点检表"""

    template_id: int = Field(..., description='模板ID')
    created_by: Optional[str] = Field(None, description='创建人')


class ProjectInspectionUpdate(BaseModel):
    """更新项目点检表"""

    inspection_item: Optional[str] = Field(None, description='点检项')
    area_function: Optional[str] = Field(None, description='区域/功能')
    category: Optional[str] = Field(None, description='类别')
    specific_plan: Optional[str] = Field(None, description='具体方案或接线点检')
    inspection_method: Optional[str] = Field(None, description='检查方式')
    self_check_result: Optional[str] = Field(None, description='自查结论')
    self_checker: Optional[str] = Field(None, description='自检人')
    audit_result: Optional[str] = Field(None, description='审核结论')
    auditor: Optional[str] = Field(None, description='审核人')
    is_completed: Optional[bool] = Field(None, description='是否完成')


class ProjectInspection(ProjectInspectionBase):
    """项目点检表响应模型"""

    id: int
    template_id: int
    completed_at: Optional[datetime]
    created_by: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class InspectionTemplateListResponse(BaseModel):
    """点检表模板列表响应"""

    total: int
    templates: List[InspectionTemplate]


class ProjectInspectionListResponse(BaseModel):
    """项目点检表列表响应"""

    total: int
    inspections: List[ProjectInspection]


class InspectionTypeResponse(BaseModel):
    """点检表类型响应"""

    types: List[str]


class ProjectInspectionSummary(BaseModel):
    """项目点检表汇总"""

    project_id: str
    total_count: int
    completed_count: int
    completion_rate: float
    types: List[str]


class ProjectInspectionSummaryListResponse(BaseModel):
    """项目点检表汇总列表响应"""

    total: int
    summaries: List[ProjectInspectionSummary]


class BatchCreateFromTemplateRequest(BaseModel):
    """基于模板批量创建项目点检表请求"""

    project_ids: List[str] = Field(..., description='项目ID列表')
    template_type: Optional[str] = Field(None, description='模板类型，为空则创建所有类型')
    created_by: Optional[str] = Field(None, description='创建人')


class SyncFromTemplateRequest(BaseModel):
    """基于模板同步项目点检表请求"""

    project_ids: Optional[List[str]] = Field(
        None, description='项目ID列表，为空则同步所有项目'
    )
    template_type: Optional[str] = Field(None, description='模板类型，为空则同步所有类型')
    sync_mode: str = Field(
        'incremental', description='同步模式：incremental(增量) 或 full(全量)'
    )


class SyncResult(BaseModel):
    """同步结果"""

    project_id: str
    success: bool
    message: str
    created_count: int = 0
    updated_count: int = 0
    deleted_count: int = 0


class BatchSyncResponse(BaseModel):
    """批量同步响应"""

    total_projects: int
    success_count: int
    failed_count: int
    results: List[SyncResult]
