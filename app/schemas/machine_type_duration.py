from pydantic import BaseModel
from datetime import datetime
from typing import Optional
from schemas.base import DeviceType


class MachineTypeDurationBase(BaseModel):
    node_definition_id: int
    machine_type: str
    device_type: DeviceType = DeviceType.main_machine
    expected_duration: Optional[float] = None


class MachineTypeDurationCreate(MachineTypeDurationBase):
    created_by: str


class MachineTypeDurationUpdate(BaseModel):
    id: int
    expected_duration: Optional[float] = None


class MachineTypeDuration(MachineTypeDurationBase):
    id: int
    node_name: str
    created_by: str
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True
