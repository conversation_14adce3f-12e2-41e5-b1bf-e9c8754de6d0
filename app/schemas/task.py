from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from schemas.base import (
    TaskStatus,
)


class TaskBase(BaseModel):
    name: str
    description: Optional[str] = None
    assigned_to: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: TaskStatus
    url: Optional[str] = None


class TaskCreate(TaskBase):
    node_instance_id: int


class Task(TaskBase):
    id: int
    node_instance_id: int

    class Config:
        from_attributes = True


class NodeAssignUpdate(BaseModel):
    """节点分配更新模型"""

    assigned_to: str


class BatchUpdateResponse(BaseModel):
    """批量更新响应模型"""

    success: bool
    updated_count: int
    updated_nodes: List[int]


# 请求体模型
class ProjectNodeAssign(BaseModel):
    """项目节点分配请求模型"""

    project_id: str = Field(..., description='项目ID')
    node_name: str = Field(..., description='节点名称')
    assign_to: str = Field(..., description='分配的处理人ID')
    notification_frequency: Optional[str] = Field(None, description='通知频率')


class UnassignedNode(BaseModel):
    node_id: int
    node_name: str
    workflow_name: str


class ProjectAssignmentStatus(BaseModel):
    project_id: str
    fully_assigned: bool
    unassigned_nodes: List[UnassignedNode]
