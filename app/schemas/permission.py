from pydantic import BaseModel
from typing import Optional


class UserPermissionBase(BaseModel):
    user_id: int
    workflow_definition_id: int
    node_definition_id: int
    permission_type: str


class UserPermissionCreate(UserPermissionBase):
    pass


class UserPermission(UserPermissionBase):
    id: int

    class Config:
        from_attributes = True


class PermissionTemplateBase(BaseModel):
    name: str
    description: Optional[str] = None


class PermissionTemplateCreate(PermissionTemplateBase):
    pass


class PermissionTemplate(PermissionTemplateBase):
    id: int

    class Config:
        from_attributes = True


class TemplatePermissionBase(BaseModel):
    template_id: int
    node_type: str
    permission_type: str


class TemplatePermissionCreate(TemplatePermissionBase):
    pass


class TemplatePermission(TemplatePermissionBase):
    id: int

    class Config:
        from_attributes = True
