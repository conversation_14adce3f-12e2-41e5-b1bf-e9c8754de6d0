from database.crud.base import CRUDBase
import aiohttp
import json
from fastapi import HTTPException
from datetime import datetime, timedelta

# from database.models import Status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
from sqlalchemy import text
from schemas.base import DeviceType
from database import logger


class CRUDStatus(CRUDBase):
    def __init__(self, model):
        super().__init__(model)
        self.erp_framework_list = None
        self.erp_info = None
        self.components_info = None
        self.erp_mech_info = None
        self.erp_bom_info = None
        self.erp_latest_statistics = None
        self.all_erp_info = None
        self.yv_info = None
        self.erp_ethercat_info = None
        self.erp_ecatmodel_info = None

    def reset(self):
        self.erp_framework_list = None
        self.erp_info = None
        self.components_info = None
        self.erp_mech_info = None
        self.erp_bom_info = None
        self.erp_latest_statistics = None
        self.all_erp_info = None
        self.yv_info = None
        self.erp_ethercat_info = None
        self.erp_ecatmodel_info = None

    async def get_all_erp_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        查询ERP基础信息
        """
        sql = """
        SELECT
            *
        FROM
            erp_base_info
        """

        result = await db.execute(text(sql))
        raw_data = result.mappings().all()

        return [dict(row) for row in raw_data]

    async def get_erp_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        查询ERP基础信息
        """
        sql = """
        SELECT
            ERP,
            `客户`,
            `型号`,
            `类别`,
            `数量`
        FROM
            erp_base_info
        """

        result = await db.execute(text(sql))
        raw_data = result.mappings().all()
        erp_info = []
        for row in raw_data:
            try:
                machine_num = (
                    int(row['数量'].strip())
                    if '+' not in row['数量']
                    else [int(num.strip()) for num in row['数量'].split('+')]
                )
            except Exception as e:
                machine_num = 0
            erp_info.append(
                {
                    'erp': row['ERP'],
                    'customer': row['客户'],
                    'model': row['型号'],
                    'type': row['类别'],
                    'machine_num': machine_num,
                }
            )
        return erp_info

    async def get_erp_ethercat_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        查询ERP基础信息
        """
        sql = """
        select distinct(ERP) from erp_ethercat_info
        """

        result = await db.execute(text(sql))
        raw_data = result.mappings().all()
        return [dict(row) for row in raw_data]

    async def get_erp_ecatmodel_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        查询ERP基础信息
        """
        sql = """
        select distinct(ERP) from erp_ecatmodel_info
        """

        result = await db.execute(text(sql))
        raw_data = result.mappings().all()
        return [dict(row) for row in raw_data]

    # 设备预研状态
    async def check_device_research_status(self, db: AsyncSession, erp: str):
        if self.all_erp_info is None:
            self.all_erp_info = await self.get_all_erp_info(db)

        research_data = [row for row in self.all_erp_info if row['ERP'] == erp]

        if len(research_data) > 0:
            columns_excluding = {
                'id',
                'is_new',
                'primary_erp',
                'second_erp',
                'bom_time',
                'et_day',
                'soft_day',
                'on_time',
                'bom_time_end',
                'on_time_end',
                'mech_time_end',
                'et_time_end',
                'io_time_end',
                'doc_time_end',
                'soft_time_end',
                'created_by',
                'created_at',
                'modified_by',
                'modified_at',
            }

            research_data = research_data[0]
            # 获取所有需要检查的字段（除去排除的字段）
            fields_to_check = set(research_data.keys()) - columns_excluding

            # 检查所有需要检查的字段是否都有值（不为NULL）
            all_fields_complete = all(
                research_data[field] is not None for field in fields_to_check
            )

            return {'is_complete': all_fields_complete}

        return {'is_complete': False}

    async def get_erp_framework_list(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        总装部件梳理状态 0:未完成 1:待审核 2:已完成
        """
        sql = """
        SELECT
            erp,
            status,
            hard_verify_by
        FROM
            inner_erp_framework_list
        """

        result = await db.execute(text(sql))
        rows = result.mappings().all()
        return [dict(row) for row in rows]

    # 总装部件梳理
    async def check_assembly_parts_list(self, db: AsyncSession, erp: str):
        if self.erp_framework_list is None:
            self.erp_framework_list = await self.get_erp_framework_list(db)

        return {
            'is_complete': erp
            in [row['erp'] for row in self.erp_framework_list]
        }

    # 部件录入
    async def get_components_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        部件录入状态
        """
        sql = """
        SELECT
            aa.*,
            COALESCE(bb.cell_pitch, 0) as cell_pitch,
            COALESCE(bb.tape_distance, 0) as tape_distance,
            COALESCE(bb.safety_door, 0) as safety_door,
            COALESCE(bb.main_frame, 0) as main_frame,
            COALESCE(bb.feed_parameter, 0) as feed_parameter
        FROM (
            SELECT a.*, b.second_erp, b.customer, b.model, b.category
            FROM (
                SELECT
                    erp,
                    CASE
                        WHEN SUM(CASE WHEN `status` = 1 THEN 1 ELSE 0 END) > 0 THEN 1
                        WHEN SUM(CASE WHEN `status` = 2 THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN `status` = 1 THEN 1 ELSE 0 END) = 0 AND SUM(CASE WHEN `status` = 0 THEN 1 ELSE 0 END) = 0 THEN 2
                        WHEN SUM(CASE WHEN `status` = 0 THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN `status` = 1 THEN 1 ELSE 0 END) = 0 THEN 0
                        ELSE NULL
                    END as alter_state,
                    (SUM(CASE WHEN `status` = 2 THEN 1 ELSE 0 END) / COUNT(*)) * 100 AS percentage,
                    COALESCE((SUM(CASE WHEN (`status` = 2) AND is_sv = 1 THEN 1 ELSE 0 END) / NULLIF(SUM(CASE WHEN `is_sv` = 1 THEN 1 ELSE 0 END), 0)) * 100, 0) AS percentage_sv,
                    CASE
                        WHEN SUM( CASE WHEN partNumber REGEXP '^[0-9]{1,2}$' THEN 1 ELSE 0 END ) = 0 THEN 100
                        ELSE (SUM( CASE WHEN `status` = 2 AND partNumber REGEXP '^[0-9]{1,2}$' THEN 1 ELSE 0 END ) / NULLIF( SUM( CASE WHEN partNumber REGEXP '^[0-9]{1,2}$' THEN 1 ELSE 0 END ), 0 )) * 100
                    END AS percentage_sys,
                    CASE
                        WHEN SUM( CASE WHEN partNumber REGEXP '^[0-9]{1,2}$' AND `is_sv` = 1 THEN 1 ELSE 0 END ) = 0 THEN 100
                        ELSE (SUM( CASE WHEN `status` = 2 AND `is_sv` = 1 AND partNumber REGEXP '^[0-9]{1,2}$' THEN 1 ELSE 0 END ) / NULLIF( SUM( CASE WHEN partNumber REGEXP '^[0-9]{1,2}$' AND `is_sv` = 1 THEN 1 ELSE 0 END ), 0 )) * 100
                    END AS percentage_sys_sv,
                    CASE
                        WHEN SUM( CASE WHEN partNumber REGEXP '[A-Za-z]' THEN 1 ELSE 0 END ) = 0 THEN 100
                        ELSE (SUM( CASE WHEN `status` = 2 AND partNumber REGEXP '[A-Za-z]' THEN 1 ELSE 0 END ) / NULLIF( SUM( CASE WHEN partNumber REGEXP '[A-Za-z]' THEN 1 ELSE 0 END ), 0 )) * 100
                    END AS percentage_om,
                    CASE
                        WHEN SUM( CASE WHEN partNumber REGEXP '[A-Za-z]' AND `is_sv` = 1 THEN 1 ELSE 0 END ) = 0 THEN 100
                        ELSE (SUM( CASE WHEN `status` = 2 AND `is_sv` = 1 AND partNumber REGEXP '[A-Za-z]' THEN 1 ELSE 0 END ) / NULLIF( SUM( CASE WHEN partNumber REGEXP '[A-Za-z]' AND `is_sv` = 1 THEN 1 ELSE 0 END ), 0 )) * 100
                    END AS percentage_om_sv,
                    max(created_at) as created_at

                FROM
                    `inner_erp_framework_list`
                WHERE
                    is_delete = 0
                GROUP BY
                    erp
            ) a
            LEFT JOIN (
                SELECT erp, second_erp, 客户 AS customer, 型号 AS model, 类别 AS category
                FROM erp_base_info
            ) b ON a.erp = b.erp
            ORDER BY a.created_at DESC
        ) aa
        LEFT JOIN (
            SELECT
                erp,
                COALESCE(MAX(CASE WHEN type = 'CELL_PITCH' THEN status END), 0) as cell_pitch,
                COALESCE(MAX(CASE WHEN type = 'TAPE_DISTANCE' THEN status END), 0) as tape_distance,
                COALESCE(MAX(CASE WHEN type = 'SAFETY_DOOR' THEN status END), 0) as safety_door,
                COALESCE(MAX(CASE WHEN type = 'MAIN_FRAME' THEN status END), 0) as main_frame,
                COALESCE(MAX(CASE WHEN type = 'FEED_PARAMETER' THEN status END), 0) as feed_parameter
            FROM inner_erp_files_status
            GROUP BY erp
        ) bb on aa.erp = bb.erp
        """

        result = await db.execute(text(sql))
        rows = result.mappings().all()
        return [dict(row) for row in rows]

    # 部件伺服信息录入 percentage_sys_sv percentage_om_sv
    async def check_component_servo_info(
        self, db: AsyncSession, erp: str, device_type: DeviceType
    ):
        if device_type == DeviceType.output_machine:
            field_name = 'percentage_om_sv'
        else:
            field_name = 'percentage_sys_sv'

        if self.components_info is None:
            self.components_info = await self.get_components_info(db)

        if erp not in [row['erp'] for row in self.components_info]:
            return {'is_complete': False}
        else:
            erp_data = [
                row for row in self.components_info if row['erp'] == erp
            ][0]

            return {
                'is_complete': int(erp_data[field_name]) >= 50,
                'detail': erp_data,
            }

    # IO规划
    async def check_io_plan(self, db: AsyncSession, erp: str):

        if self.erp_ecatmodel_info is None:
            self.erp_ecatmodel_info = await self.get_erp_ecatmodel_info(db)

        if self.erp_ethercat_info is None:
            self.erp_ethercat_info = await self.get_erp_ethercat_info(db)

        if (erp not in [row['ERP'] for row in self.erp_ecatmodel_info]) or (
            erp not in [row['ERP'] for row in self.erp_ethercat_info]
        ):
            return {'is_complete': False}
        else:

            return {
                'is_complete': True,
            }

    # 部件完整信息录入 percentage_sys percentage_om
    async def check_component_full_info(
        self, db: AsyncSession, erp: str, device_type: DeviceType
    ):
        if device_type == DeviceType.output_machine:
            field_name = 'percentage_om'
        else:
            field_name = 'percentage_sys'
        if self.components_info is None:
            self.components_info = await self.get_components_info(db)
        if erp not in [row['erp'] for row in self.components_info]:
            return {'is_complete': False}
        else:
            erp_data = [
                row for row in self.components_info if row['erp'] == erp
            ][0]
            return {
                'is_complete': int(erp_data[field_name]) == 100,
                'detail': erp_data,
            }

    # 气路设计
    async def get_inner_yv_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        气路设计erp获取
        """
        sql = """
        SELECT DISTINCT erp FROM inner_yv_info
        UNION
        SELECT DISTINCT erp FROM erp_yv_info
        """

        result = await db.execute(text(sql))
        rows = result.mappings().all()
        return [dict(row)['erp'] for row in rows]

    # 气路设计
    async def check_yv_info(
        self, db: AsyncSession, erp: str
    ) -> List[Dict[str, Any]]:
        """
        气路设计状态检查
        """
        if self.yv_info is None:
            self.yv_info = await self.get_inner_yv_info(db)
        return {'is_complete': erp in self.yv_info}

    async def get_erp_mech_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        机电沟通表入库状态
        """
        sql = """
        SELECT
            erp
        FROM
            erp_mech_info
        """

        result = await db.execute(text(sql))
        rows = result.mappings().all()
        return [dict(row) for row in rows]

    # 机电沟通表入库 生成EtherCat伺服节点
    async def check_erp_mech_info(self, db: AsyncSession, erp: str):
        if self.erp_mech_info is None:
            self.erp_mech_info = await self.get_erp_mech_info(db)
        return {
            'is_complete': erp in [row['erp'] for row in self.erp_mech_info]
        }

    # 程序检查，程序上传GIT
    async def check_git_upload(self, db: AsyncSession, erp: str):
        # 原有的SQL查询，检查相似度
        sql = """
            SELECT
                cr.gitlab_id,
                cr.avg_similarity_percentage,
                e.`ERP`,
                a.`name`,
                e.`型号`
            FROM (
                SELECT
                    gitlab_id,
                    AVG(JSON_EXTRACT(comparison_result, '$.similarity_percentage')) AS avg_similarity_percentage
                FROM (
                    SELECT project_id as gitlab_id, comparison_result
                    FROM `function_comparison_results`
                    WHERE `name` NOT IN (SELECT `name` FROM soft_fb_exclusion WHERE is_delete = 0)

                    UNION

                    SELECT gitlab_id, comparison_result
                    FROM git_beckhoff_tcpou_comparison_results
                    WHERE `emv_name` NOT IN (SELECT `name` FROM soft_fb_exclusion WHERE is_delete = 0)
                ) AS combined_results
                GROUP BY gitlab_id
            ) AS cr
            INNER JOIN flask_project_managers a ON cr.gitlab_id = a.gitlab_id
            INNER JOIN erp_base_info e ON e.`型号` LIKE CONCAT('%', a.`name`, '%')
            WHERE a.root_group IN ('FactoryProject', 'DevelopingProject')
                AND a.type = 1
                AND a.`name` IS NOT NULL
                AND a.`name` <> '';
        """

        result = await db.execute(text(sql))
        rows = result.mappings().all()
        git_projects = [dict(row) for row in rows]

        all_ratio_sql = """
            SELECT erp, all_ratio FROM erp_fulfillment_of_schedule
        """

        result = await db.execute(text(all_ratio_sql))
        rows = result.mappings().all()
        all_ratios = [dict(row) for row in rows]

        # 获取处理后的pjList数据
        try:
            # 调用API获取项目数据
            url = 'http://10.30.200.215:8021/emBy2/pjList'
            if self.erp_info is None:
                self.erp_info = await self.get_erp_info(db)

            # 获取token
            login_url = 'http://10.30.200.215:8021/login'
            form_data = aiohttp.FormData()
            form_data.add_field('username', '1202490')
            form_data.add_field('password', '1234@qwer')
            form_data.add_field('idKey', '')
            form_data.add_field('remember', 'true')

            token = None
            async with aiohttp.ClientSession() as session:
                # 登录获取token
                async with session.post(
                    login_url,
                    data=form_data,
                    headers={
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                ) as resp:
                    if resp.status == 200:
                        resp_data = await resp.json()
                        token = resp_data['data']['access_token']
                        logger.info('登录成功，获取token')
                    else:
                        resp_text = await resp.text()
                        logger.error(f'登录失败: {resp_text}')
                        return {
                            'is_complete': False,
                            'message': '登录失败，无法获取项目数据',
                        }

                # 获取项目列表数据
                payload = {'root_group': 'DevelopingProject'}
                async with session.post(
                    url,
                    json=payload,
                    headers={
                        'accept': 'application/json',
                        'Authorization': f'Bearer {token}',
                        'Content-Type': 'application/json',
                    },
                ) as resp:
                    if resp.status == 200:
                        pjlist_data = await resp.json()
                        logger.info('pjList接口调用成功')
                    else:
                        resp_text = await resp.text()
                        logger.error(f'pjList接口调用失败: {resp_text}')
                        return {
                            'is_complete': False,
                            'message': 'pjList接口调用失败',
                        }

            # 获取模型信息和ERP号码对应关系
            if self.erp_info is None:
                self.erp_info = await self.get_erp_info(db)

            erp_model = [
                row['model'] for row in self.erp_info if row['erp'] == erp
            ][0].split('/')[0]

            # 处理获取到的项目数据
            if 'data' not in pjlist_data:
                return {'is_complete': False, 'message': 'pjList返回数据结构有误'}

            # 定义版本处理函数
            def get_lib_version_by_name(name, top_level_group):
                if 'version' not in pjlist_data:
                    return None

                version_list = pjlist_data['version']
                version_info = None

                if top_level_group == 'Omron_EmLibrary':
                    version_info = next(
                        (
                            v
                            for v in version_list
                            if v.get('top_level_group') == 'Omron_EmLibrary'
                            and v.get('name', '').lower() == name.lower()
                        ),
                        None,
                    )
                elif top_level_group == 'Beckhoff_EmLibrary':
                    version_info = next(
                        (
                            v
                            for v in version_list
                            if v.get('top_level_group') == 'Beckhoff_EmLibrary'
                            and v.get('name', '').lower() == name.lower()
                        ),
                        None,
                    )

                return version_info.get('lib_files') if version_info else None

            # 处理表格数据
            table_data = pjlist_data['data']
            processed_data = []

            for row in table_data:
                # 找出所有包含'Lds'的动态列
                dynamic_columns = [key for key in row.keys() if 'Lds' in key]

                # 计算main版本总数
                total_main = sum(
                    1
                    for key in dynamic_columns
                    if row.get(key, {}).get('main')
                )

                # 计算main版本匹配度
                same_count_main = 0
                for key in dynamic_columns:
                    if key not in row or not isinstance(row[key], dict):
                        continue

                    current_version = row[key].get('main')
                    lib_versions = get_lib_version_by_name(
                        key, row.get('top_level_group')
                    )

                    if not current_version or not lib_versions:
                        continue

                    # 转换为数组统一处理
                    versions = (
                        lib_versions
                        if isinstance(lib_versions, list)
                        else [lib_versions]
                    )

                    # 获取当前版本的主版本号
                    try:
                        version_parts = current_version.split('.')
                        if len(version_parts) < 3:
                            continue

                        current_major_version = version_parts[0]

                        # 查找主版本号相同的库版本
                        matching_version = next(
                            (
                                v
                                for v in versions
                                if v.split('.')[0] == current_major_version
                            ),
                            None,
                        )

                        if not matching_version:
                            continue

                        lib_version_parts = matching_version.split('.')
                        if len(lib_version_parts) < 3:
                            continue

                        major_version = int(version_parts[0])
                        if major_version != int(lib_version_parts[0]):
                            continue

                        # 主版本号小于100的情况
                        if major_version < 100:
                            # 将次版本和末位版本组合成10进制数值
                            version_num = int(version_parts[1]) * 10 + int(
                                version_parts[2]
                            )
                            lib_version_num = int(
                                lib_version_parts[1]
                            ) * 10 + int(lib_version_parts[2])

                            # 判断版本匹配度
                            if version_num == lib_version_num:
                                same_count_main += 1
                            elif (
                                abs(version_num - lib_version_num) <= 6
                                and version_num != lib_version_num
                            ):
                                same_count_main += 0.9
                                same_count_main = round(
                                    same_count_main, 1
                                )  # 每次累加后立即固定小数位
                        else:
                            # 主版本号大于等于100时使用原来的比较逻辑
                            if version_parts[2] == lib_version_parts[2]:
                                same_count_main += 1
                            elif (
                                version_parts[2] != lib_version_parts[2]
                                and abs(
                                    int(version_parts[2])
                                    - int(lib_version_parts[2])
                                )
                                <= 6
                            ):
                                same_count_main += 0.9
                                same_count_main = round(same_count_main, 1)
                    except Exception as e:
                        logger.warning(f'处理版本 {current_version} 时出错: {str(e)}')
                        continue

                # 计算main版本更新百分比
                update_percentage_main = (
                    0
                    if total_main == 0
                    else (same_count_main / total_main) * 100
                )

                # 更新行数据
                new_row = row.copy()
                new_row.update(
                    {
                        'total_main': total_main,
                        'sameCount_main': same_count_main,
                        'updatePercentage_main': update_percentage_main,
                    }
                )

                processed_data.append(new_row)

            # 查找匹配当前ERP型号的数据
            matching_rows = [
                row
                for row in processed_data
                if erp_model in row.get('name', '')
            ]

            if not matching_rows:
                return {
                    'is_complete': False,
                    'message': f'未找到与ERP型号({erp_model})匹配的项目数据',
                }

            update_percentage_check = all(
                row.get('updatePercentage_main') >= 95 for row in matching_rows
            )

            # 检查原来的SQL查询结果
            original_similarity_check = False
            if erp in [row['ERP'] for row in git_projects]:
                row = next(row for row in git_projects if row['ERP'] == erp)
                original_similarity_check = (
                    int(row['avg_similarity_percentage']) >= 95
                )

            # 检查原来的SQL查询结果
            all_ratio_check = False
            if erp in [row['erp'] for row in all_ratios]:
                row = next(row for row in all_ratios if row['erp'] == erp)
                all_ratio_check = int(float(row['all_ratio'])) == 100

            # 两个条件都满足时才返回完成
            return {
                'is_complete': original_similarity_check
                and update_percentage_check
                and all_ratio_check,
                'original_similarity': original_similarity_check,
                'update_percentage_check': update_percentage_check,
                'all_ratio_check': all_ratio_check,
                'message': f"代码相似度检查: {'通过' if original_similarity_check else '未通过'}, 版本更新检查: {'通过' if update_percentage_check else '未通过'}",
            }

        except Exception as e:
            logger.error(f'检查GIT上传状态时出错: {str(e)}')
            return {'is_complete': False, 'message': f'处理数据时出错: {str(e)}'}

    # EM准备
    async def check_em_prepare(
        self, db: AsyncSession, erp: str
    ) -> List[Dict[str, Any]]:
        """
        检查EM配置状态 - MySQL版本
        """

        sql = """
        SELECT
            ERP,
            `客户`,
            `型号`,
            `类别`
        FROM
            erp_base_info
        WHERE
            ERP = :erp
        """
        result = await db.execute(text(sql), {'erp': erp})
        raw_model = result.mappings().all()

        for record in raw_model:
            if '圆柱' in record['类别']:
                remark = '圆柱机'
                break
            elif 'EV' in record['类别']:
                remark = 'EV机'
                break
            elif '数码' in record['类别']:
                remark = '一体机'
                break

        # 使用参数替换原SQL中的ERP值
        sql = self.get_base_em_sql(erp, remark)

        result = await db.execute(text(sql))
        raw_data = result.mappings().all()
        status = []
        for record in raw_data:
            if record['status1'] is not None:
                status.append(record['status1'] == 0)
            if record['status2'] is not None:
                status.append(record['status2'] == 0)
            if record['status3'] is not None:
                status.append(record['status3'] == 0)
            if record['status4'] is not None:
                status.append(record['status4'] == 0)
        return {'is_complete': all(status)}

    async def get_all_em_status(self, erp):
        url = f'http://10.30.200.215:8021/status/emlist/{erp}'

        # 获取token
        login_url = 'http://10.30.200.215:8021/login'
        form_data = aiohttp.FormData()
        form_data.add_field('username', '1202490')
        form_data.add_field('password', '1234@qwer')
        form_data.add_field('idKey', '')
        form_data.add_field('remember', 'true')

        token = None
        async with aiohttp.ClientSession() as session:
            # 登录获取token
            async with session.post(
                login_url,
                data=form_data,
                headers={
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            ) as resp:
                if resp.status == 200:
                    resp_data = await resp.json()
                    token = resp_data['data']['access_token']
                    logger.info('登录成功，获取token')
                else:
                    resp_text = await resp.text()
                    logger.error(f'登录失败: {resp_text}')
                    return None

            async with session.post(
                url,
                headers={
                    'accept': 'application/json',
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json',
                },
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    return data
                else:
                    resp_text = await resp.text()
                    logger.error(f'pjList接口调用失败: {resp_text}')
                    return None

    # EM信息录入
    async def check_em_status(
        self, db: AsyncSession, erp: str
    ) -> List[Dict[str, Any]]:
        """
        检查EM配置状态 - MySQL版本
        """

        result = await self.get_all_em_status(erp)
        status = []
        for record in result['data']:
            if record['cm_status'] is not None:
                status.append(
                    record['cm_status'] != 2
                    and record['ver3']
                    and record['ver4']
                )
        return {'is_complete': all(status)}

    # 机型负责人录入版本信息
    async def check_ver_status(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, bool]:
        """
        检查指定ERP的ver1和ver3配置状态
        """
        sql = """
        SELECT
            CASE
                WHEN COUNT(*) = 0 THEN 0
                WHEN COUNT(CASE WHEN ver1 IS NOT NULL AND ver3 IS NULL THEN 1 END) > 0 THEN 0
                ELSE 1
            END as is_complete
        FROM erp_framework_list
        WHERE erp = :erp AND ver1 IS NOT NULL
        """

        result = await db.execute(text(sql), {'erp': erp})
        status = result.scalar()

        return {'is_complete': status}

    def get_base_em_sql(self, erp: str, remark: str) -> str:
        """获取基础EM查询SQL"""
        return f"""
        SELECT DISTINCT
            n.*
        FROM
            (
            SELECT
                z.*,
                x.EM中文 AS emCN
            FROM
                (
                SELECT COALESCE
                    ( a.`EM名称`, io.EM名称, ec.EM名称, ef.EM名称, hy.EM名称 ) AS emName,
                CASE

                        WHEN io.空软件变量数量 > 0 THEN
                        1
                        WHEN io.空软件变量数量 IS NULL THEN
                        NULL ELSE 0
                    END AS status1,
                CASE

                        WHEN ec.空软件版本号 > 0 THEN
                        1
                        WHEN ec.空软件版本号 IS NULL THEN
                        NULL ELSE 0
                    END AS status2,
                CASE

                        WHEN ef.SV基础参数 > 0 THEN
                        1
                        WHEN ef.SV基础参数 IS NULL THEN
                        NULL ELSE 0
                    END AS status3,
                CASE

                        WHEN hy.YV基础参数 > 0 THEN
                        1
                        WHEN hy.YV基础参数 IS NULL THEN
                        NULL ELSE 0
                    END AS status4,
                            CASE

                        WHEN ff.FB是否为空 > 0 THEN
                        1
                        WHEN ff.FB是否为空 IS NULL THEN
                        NULL ELSE 0
                    END AS status5
                FROM
                    (
                        ( SELECT DISTINCT `EM名称` FROM `HMI_SV配置` WHERE ERP = '{erp}' AND EM名称 IS NOT NULL ) UNION
                        ( SELECT DISTINCT `EM名称` FROM `HMI_YV配置` WHERE ERP = '{erp}' ) UNION
                        ( SELECT DISTINCT `EM名称` FROM `io_version_info` WHERE ERP = '{erp}' AND `中文名称` IS NOT NULL AND `中文名称` <> '' ) UNION
                        (
                        SELECT
                            q1.EM名称
                        FROM
                            ( SELECT DISTINCT `EM名称` FROM `ERP配置清单` WHERE ERP = '{erp}' AND `EM名称` IS NOT NULL ) AS q1
                            JOIN ( SELECT DISTINCT menu_name FROM `程序框架` WHERE is_max = 0 ) AS pf ON q1.`EM名称` = pf.menu_name
                        )) a
                    LEFT JOIN (
                    SELECT
                        `EM名称`,
                        SUM( CASE WHEN `软件变量` IS NULL OR `软件变量` = '' THEN 1 ELSE 0 END ) AS `空软件变量数量`
                    FROM
                        `io_version_info`
                    WHERE
                        ERP = '{erp}'
                        AND `中文名称` IS NOT NULL
                        AND `中文名称` <> ''
                    GROUP BY
                        `EM名称`
                    ) AS io ON a.`EM名称` = io.`EM名称`
                    LEFT JOIN (
                    SELECT
                        q11.EM名称,
                    CASE

                            WHEN gg.fb_name IS NULL
                            OR gg.fb_name = '' THEN
                                1 ELSE 0
                                END AS `空软件版本号`
                        FROM
                            (
                            SELECT
                                q1.`EM名称`,
                                q1.软件版本号,
                                fb_name
                            FROM
                                ( SELECT `EM名称`, `软件版本号` -- 		SUM( CASE WHEN `软件版本号` IS NULL OR `软件版本号` = '' THEN 1 ELSE 0 END ) AS `空软件版本号`
                                FROM `ERP配置清单` WHERE ERP = '{erp}' AND `EM名称` IS NOT NULL AND 硬件主版本号 <> 0) AS q1
                                JOIN ( SELECT menu_name, fb_name FROM `程序框架` WHERE is_max = 0 AND menu_type = 'S' AND fb_name IS NOT NULL AND fb_name <> '' and remark like '%%{remark}%%') AS pf ON q1.`EM名称` = pf.menu_name
                            ) q11
                            LEFT JOIN ( SELECT fb_name, fb_code, soft_version FROM soft_fb_version ) gg ON q11.fb_name = gg.fb_name
                            AND q11.`软件版本号` = gg.soft_version
                        ) AS ec ON a.`EM名称` = ec.`EM名称`
                        LEFT JOIN (
                        SELECT
                            b.EM名称,
                            a.SV基础参数
                        FROM
                            (
                            SELECT
                                (
                                CASE

                                        WHEN ( `节点编号` = 'Core1' ) THEN
                                        'EM511'
                                        WHEN ( `节点编号` = 'Core2' ) THEN
                                        'EM711'
                                        WHEN ( `节点编号` = 'Core3' ) THEN
                                        'EM611' ELSE substring_index( `节点编号`, '_', 1 )
                                    END
                                    ) AS `EM名称`,
                                SUM( CASE WHEN `PDO周期` IS NULL OR `PDO周期` = '' THEN 1 ELSE 0 END ) AS `SV基础参数`
                            FROM
                                `EtherCAT`
                            WHERE
                                `ERP` = '{erp}'
                                AND `节点名称` LIKE '%%伺服%%'
                            GROUP BY
                                                        (
                                CASE

                                        WHEN ( `节点编号` = 'Core1' ) THEN
                                        'EM511'
                                        WHEN ( `节点编号` = 'Core2' ) THEN
                                        'EM711'
                                        WHEN ( `节点编号` = 'Core3' ) THEN
                                        'EM611' ELSE substring_index( `节点编号`, '_', 1 )
                                    END
                                    )
                            ) a
                            LEFT JOIN ( SELECT DISTINCT `code` as EM名称, `name` as EM中文 FROM flask_em_dict WHERE is_delete = 0 ) b ON a.`EM名称` = b.`EM名称`
                        ) AS ef ON a.`EM名称` = ef.`EM名称`
                        LEFT JOIN (
                        SELECT
                            `EM名称`,
                            SUM( CASE WHEN `点动F名称` IS NULL OR `点动F名称` = '' THEN 1 ELSE 0 END ) AS `YV基础参数`
                        FROM
                            `HMI_YV配置`
                        WHERE
                            `ERP` = '{erp}'
                        GROUP BY
                            `EM名称`
                        ) AS hy ON a.`EM名称` = hy.`EM名称`
                        LEFT JOIN (
        SELECT q11.EM名称,CASE WHEN gg.em_name IS NULL OR gg.em_name = '' THEN 1 ELSE 0 END  AS `FB是否为空` from
        (SELECT
            q1.`EM名称`,
            q1.硬件主版本号,
            CASE WHEN q1.硬件次版本号 IS NULL  THEN 0 ELSE q1.硬件次版本号 END as 硬件次版本号
        FROM
            (
            SELECT
                `EM名称`,
                `硬件主版本号`,`硬件次版本号`
        -- 		SUM( CASE WHEN `软件版本号` IS NULL OR `软件版本号` = '' THEN 1 ELSE 0 END ) AS `空软件版本号`
            FROM
                `ERP配置清单`
            WHERE
                ERP = '{erp}'
                AND `EM名称` IS NOT NULL
            ) AS q1
            JOIN ( SELECT menu_name,fb_name FROM `程序框架` WHERE is_max = 0 and menu_type = 'S' and fb_name is not null and fb_name <> '' ) AS pf ON q1.`EM名称` = pf.menu_name) q11 LEFT JOIN
            (SELECT DISTINCT em_name,ver3,ver2 FROM flask_fb_log) gg on q11.EM名称 = gg.em_name and q11.`硬件主版本号` = gg.ver3 and q11.`硬件次版本号` = gg.ver2
                        ) AS ff ON a.`EM名称` = ff.`EM名称`
                    ) z
                    LEFT JOIN ( SELECT DISTINCT `code` as EM名称,`name` as EM中文 FROM flask_em_dict WHERE is_delete = 0) x ON z.emName = x.EM名称
                ) n
        """

    # IO表入库+IO表核对
    async def check_io_table_status(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, bool]:
        result1 = await self.check_activity_completeness(db, erp)
        result2 = await self.check_erp_io_accuracy(db, erp)
        return {
            'is_complete': result1['is_complete'] and result2['is_complete']
        }

    # IO表入库
    async def check_activity_completeness(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, Any]:
        """
        检查指定ERP的活动完整性
        检查activiti_value为1,2,3,4,6中存在的值是否submit_count>0
        """
        sql = """
        SELECT
            is_new
        FROM erp_base_info
        WHERE ERP = :erp
        """

        result = await db.execute(text(sql), {'erp': erp})
        records = result.mappings().all()
        if len(records) > 0:
            if int(records[0]['is_new']) == 0:
                return {
                    'erp': erp,
                    'message': '老框架需要手动审批',
                    'is_complete': False,
                }
        sql = """
        WITH required_steps AS (
            SELECT '1' as step
            UNION SELECT '2'
            UNION SELECT '3'
            UNION SELECT '4'
            UNION SELECT '6'
        ),
        actual_steps AS (
            SELECT
                erp,
                activiti_value as step,
                submit_count
            FROM flask_activiti_log
            WHERE erp = :erp
                AND activiti_value IN ('1', '2', '3', '4', '6')
                AND is_delete = 0
        )
        SELECT
            :erp as erp,
            COUNT(DISTINCT CASE
                WHEN a.submit_count > 0 THEN a.step
            END) as completed_steps,
            COUNT(DISTINCT a.step) as existing_steps,
            GROUP_CONCAT(
                CASE
                    WHEN a.step IS NULL THEN r.step
                    WHEN a.submit_count <= 0 THEN a.step
                END
                ORDER BY r.step
            ) as missing_or_incomplete_steps
        FROM required_steps r
        LEFT JOIN actual_steps a ON r.step = a.step
        """

        result = await db.execute(text(sql), {'erp': erp})
        data = result.mappings().first()

        missing_steps = data['missing_or_incomplete_steps']
        completed = data['completed_steps']
        existing = data['existing_steps']

        response = {
            'erp': erp,
            'existing_steps': existing,
            'completed_steps': completed,
            'missing_or_incomplete_steps': missing_steps.split(',')
            if missing_steps
            else [],
            'is_complete': completed == 5,
            'message': None,
        }

        if existing == 0:
            response['message'] = '未找到任何相关活动记录'
        elif completed == 5:
            try:
                url = 'http://10.30.200.215:8021/status/createFactoryProject'
                if self.erp_info is None:
                    self.erp_info = await self.get_erp_info(db)

                login_url = 'http://10.30.200.215:8021/login'
                # 构建表单数据
                form_data = aiohttp.FormData()
                form_data.add_field('username', '1202490')
                form_data.add_field('password', '1234@qwer')
                form_data.add_field('idKey', '')
                form_data.add_field('remember', 'true')
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        login_url,
                        data=form_data,
                        headers={
                            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                    ) as resp:
                        if resp.status == 200:
                            # 先等待 json() 完成
                            resp_data = await resp.json()
                            # 然后从返回的数据中获取 token
                            token = resp_data['data']['access_token']
                        else:
                            resp_text = await resp.text()
                            logger.error(f'登录失败: {resp_text}')

                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        url,
                        json={
                            'customer': [
                                i for i in self.erp_info if i['erp'] == erp
                            ][0]['customer'],
                            'model': [
                                i for i in self.erp_info if i['erp'] == erp
                            ][0]['model'].split('/')[0],
                        },
                        headers={
                            'accept': 'application/json',
                            'Authorization': f'Bearer {token}',
                        },
                    ) as resp:
                        if resp.status == 200:
                            logger.info('创建项目成功')
                        else:
                            logger.error('创建项目失败')

            except Exception as e:
                # 记录异常日志
                logger.error(f'调用创建项目接口失败: {str(e)}')
            response['message'] = '已有活动步骤全部完成'
        else:
            response['message'] = f'以下步骤缺失或未完成: {missing_steps}'

        return response

    # 自动跳过节点
    async def always_true(self, *args, **kwargs):
        return {'is_complete': True}

    # PLC和HMI程序自动生成
    async def check_plc_hmi_status(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, Any]:
        hmi_status = await self.check_hmi_status(db, erp)
        plc_status = await self.check_pap_status(db, erp)
        is_complete = hmi_status['is_success'] and plc_status['is_success']

        return {
            'erp': erp,
            'hmi_status': hmi_status,
            'plc_status': plc_status,
            'is_complete': is_complete,
        }

    # plc代码自动生成
    async def check_pap_status(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, Any]:
        """检查PAP生成状态"""
        sql = """
        SELECT
            erp,
            plc,
            stage,
            status
        FROM flask_pap_log
        WHERE erp = :erp
        """

        result = await db.execute(text(sql), {'erp': erp})
        records = result.mappings().all()

        if not records:
            return {
                'erp': erp,
                'status': 'unknown',
                'message': '未找到PAP生成记录',
                'is_success': False,
                'plc_type': None,
            }

        # 获取PLC类型
        plc_type = None
        if records:
            for record in records:
                if record['plc'] == '欧姆龙':
                    plc_type = 'omron'
                    break
                elif record['plc'] == '倍福':
                    plc_type = 'beckhoff'
                    break

        # 检查最新的相关记录
        target_stage = '生产XML' if plc_type == 'omron' else '生成PLC代码'
        status = []

        for record in records:
            if record['stage'] == target_stage:
                status.append(record['status'])

        is_success = any(status)

        return {
            'erp': erp,
            'status': 'success' if is_success else 'failed',
            'message': f"PAP{target_stage}{'成功' if is_success else '失败'}",
            'is_success': is_success,
            'plc_type': plc_type,
        }

    # hmi自动生成
    async def check_hmi_status(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, Any]:
        """检查HMI状态"""
        sql = """
        SELECT
            HMI
        FROM erp_base_info
        WHERE ERP = :erp
        """

        result = await db.execute(text(sql), {'erp': erp})
        records = result.mappings().all()
        if len(records) > 0:
            if records[0]['HMI'] != '诺达佳':
                return {
                    'erp': erp,
                    'status': 'success',
                    'message': '非诺达佳不需要自动生成',
                    'is_success': True,
                }

        sql = """
        SELECT
            erp,
            stage,
            status
        FROM flask_hmi_log
        WHERE erp = :erp
        """

        result = await db.execute(text(sql), {'erp': erp})
        records = result.mappings().all()

        if not records:
            return {
                'erp': erp,
                'status': 'unknown',
                'message': '未找到HMI读表记录',
                'is_success': False,
            }

        # 检查最新的读表记录
        for record in records:
            if record['stage'] == '读表成功':
                is_success = record['status'] == 1
                if is_success:
                    return {
                        'erp': erp,
                        'status': 'success' if is_success else 'failed',
                        'message': f"HMI读表{'成功' if is_success else '失败'}",
                        'is_success': is_success,
                    }

        return {
            'erp': erp,
            'status': 'failed',
            'message': '未找到HMI读表成功记录',
            'is_success': False,
        }

    async def create_factory_project(self, db: AsyncSession, erp: str) -> bool:
        """
        创建工厂项目的单独方法

        Args:
            db: 数据库会话
            erp: ERP号码

        Returns:
            bool: 创建是否成功
        """
        try:
            url = 'http://10.30.200.215:8021/status/createFactoryProject'
            if self.erp_info is None:
                self.erp_info = await self.get_erp_info(db)

            # 查找对应的ERP信息
            erp_data = next(
                (i for i in self.erp_info if i['erp'] == erp), None
            )
            if not erp_data:
                logger.error(f'未找到ERP信息: {erp}')
                return False

            login_url = 'http://10.30.200.215:8021/login'
            # 构建表单数据
            form_data = aiohttp.FormData()
            form_data.add_field('username', '1202490')
            form_data.add_field('password', '1234@qwer')
            form_data.add_field('idKey', '')
            form_data.add_field('remember', 'true')

            token = None
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    login_url,
                    data=form_data,
                    headers={
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                ) as resp:
                    if resp.status == 200:
                        # 先等待 json() 完成
                        resp_data = await resp.json()
                        # 然后从返回的数据中获取 token
                        token = resp_data['data']['access_token']
                    else:
                        resp_text = await resp.text()
                        logger.error(f'登录失败: {resp_text}')
                        return False

                async with session.post(
                    url,
                    json={
                        'customer': erp_data['customer'],
                        'model': erp_data['model'].split('/')[0],
                    },
                    headers={
                        'accept': 'application/json',
                        'Authorization': f'Bearer {token}',
                    },
                ) as resp:
                    if resp.status == 200:
                        logger.info(f'创建项目成功: {erp}')
                        return True
                    else:
                        logger.error(f'创建项目失败: {erp}')
                        return False

        except Exception as e:
            logger.error(f'调用创建项目接口失败: {str(e)}')
            return False

    async def get_erp_machine_num_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        查询ERP基础信息
        """
        sql = """
        SELECT
            ERP,
            `数量`
        FROM
            erp_base_info
        """

        result = await db.execute(text(sql))
        raw_data = result.mappings().all()
        erp_machine_num = {}
        for row in raw_data:
            erp_machine_num['erp'] = row['ERP']
            erp_machine_num['machine_num'] = (
                int(row['数量'])
                if '+' not in row['数量']
                else [int(num.strip()) for num in row['数量'].split('+')]
            )
        return erp_machine_num

    # 是否批量机
    async def check_erp_machine_num(self, db: AsyncSession, erp: str):
        if self.erp_info is None:
            self.erp_info = await self.get_erp_info(db)
        if erp in [row['erp'] for row in self.erp_info]:
            decision = [row for row in self.erp_info if row['erp'] == erp][0][
                'machine_num'
            ] > 5
            return {
                'is_complete': True,
                'decision': decision,
                'detail': [row for row in self.erp_info if row['erp'] == erp][
                    0
                ],
            }
        return {'is_complete': False}

    async def check_erp_machine_num_lower_than_five(
        self, db: AsyncSession, erp: str
    ):
        if self.erp_info is None:
            self.erp_info = await self.get_erp_info(db)
        if erp in [row['erp'] for row in self.erp_info]:
            decision = [row for row in self.erp_info if row['erp'] == erp][0][
                'machine_num'
            ] >= 5
            if decision:
                return {
                    'is_complete': False,
                }
            else:
                return {
                    'is_complete': True,
                }
        return {'is_complete': False}

    async def get_all_erp_latest_statistics(self) -> List[Dict[str, Any]]:
        url = 'http://10.30.200.215:30088/all_erp_statistics'
        async with aiohttp.ClientSession() as session:
            async with session.get(
                url, headers={'accept': 'application/json'}
            ) as response:
                if response.status == 200:
                    return await response.json()
                raise HTTPException(status_code=response.status, detail='请求失败')

    # bom比对分析
    async def check_erp_bom_accuracy(self, db: AsyncSession, erp: str):
        if self.erp_latest_statistics is None:
            self.erp_latest_statistics = (
                await self.get_all_erp_latest_statistics()
            )

        if erp in [
            row['erp_number'] for row in self.erp_latest_statistics['data']
        ]:
            detail = [
                row
                for row in self.erp_latest_statistics['data']
                if row['erp_number'] == erp
            ]
            if len(detail):
                error_count = [
                    row['error_count']
                    for row in detail[0]['latest_calculation']['error_details']
                    if row['type']
                    in [
                        'airline',
                        'drive',
                        'cable',
                        'bomhistorycheck',
                        'electric',
                        'cabinet',
                    ]
                ]
                # total_count = [
                #     row['total_count'] != 0
                #     for row in detail[0]['latest_calculation']['error_details']
                #     if row['type']
                #     in [
                #         'airline',
                #         'drive',
                #         'cable',
                #         'bomhistorycheck',
                #         'electric',
                #         'cabinet',
                #     ]
                # ]
                is_complete = (
                    len(error_count) == 6
                    and sum(error_count) == 0
                    # and all(total_count)
                )
                return {'is_complete': is_complete, 'detail': detail}
        return {'is_complete': False}

    # 8Elpan图纸IO点位表校验
    async def check_erp_io_accuracy(self, db: AsyncSession, erp: str):
        if self.erp_latest_statistics is None:
            self.erp_latest_statistics = (
                await self.get_all_erp_latest_statistics()
            )

        if erp in [
            row['erp_number'] for row in self.erp_latest_statistics['data']
        ]:
            detail = [
                row
                for row in self.erp_latest_statistics['data']
                if row['erp_number'] == erp
            ]
            if len(detail):
                error_counts = [
                    row['error_count']
                    for row in detail[0]['latest_calculation']['error_details']
                    if row['type'] == 'iotable'
                ]
                if len(error_counts):
                    error_count = error_counts[0]
                    return {'is_complete': error_count == 0, 'detail': detail}
        return {'is_complete': False}

    async def get_erp_bom_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        查询ERP基础信息
        """
        sql = """
        SELECT
            erp
        FROM
            inner_erp_bom_info
        """

        result = await db.execute(text(sql))
        rows = result.mappings().all()
        return [dict(row) for row in rows]

    # 生成驱动器选型表 生成完整选型表 填写线缆长度
    async def check_erp_bom_info(self, db: AsyncSession, erp: str):
        if self.erp_bom_info is None:
            self.erp_bom_info = await self.get_erp_bom_info(db)

        return {
            'is_complete': erp in [row['erp'] for row in self.erp_bom_info]
        }

    # 机电沟通表核查 机电沟通表核查完成
    async def check_mech_verify(self, db: AsyncSession, erp: str):
        if self.erp_framework_list is None:
            self.erp_framework_list = await self.get_erp_framework_list(db)

        return {
            'is_complete': all(
                [row['hard_verify_by'] for row in self.erp_framework_list]
            )
        }

    # 三维搭建
    async def check_3d(self, db: AsyncSession, erp: str):
        sql = """
        select ni.start_time, ni.status, wi.project_id, ni.id from workflow_node_instances ni
        left join workflow_instances wi on ni.workflow_instance_id = wi.id
        where node_definition_id = 48 and wi.device_type = 'main_machine' and wi.project_id = :erp and wi.workflow_definition_id =1
        """

        result = await db.execute(text(sql), {'erp': erp})
        rows = result.mappings().all()

        # 如果没有查询结果，返回 False
        if not rows:
            return {'is_complete': False}

        start_time = [dict(row) for row in rows][0]['start_time']

        # 如果 start_time 为空，返回 False
        if not start_time:
            return {'is_complete': False}

        # 获取当前时间
        current_time = datetime.now()

        # start_time 从数据库查出来已经是 datetime 对象，无需转换

        # 计算时间差
        time_difference = current_time - start_time

        # 判断是否大于5天
        is_complete = time_difference > timedelta(days=5)

        return {'is_complete': is_complete}

    async def get_project_stages(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, Any]:
        """
        获取项目的所有机器阶段信息 - 简化版本，只返回阶段列表
        """
        try:
            # 第一步：登录获取token
            login_url = 'https://api.leadchina.cn/Login/LoginOn'
            login_payload = {
                'json': json.dumps(
                    {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'User': {
                            'Domain': '',
                            'UserId': '1297844',
                            'Password': 'FCBfcb990607',
                            'ValiCode': '',
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }
                )
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    login_url, data=login_payload
                ) as login_response:
                    login_result = await login_response.json()

                    if login_result.get('Code') != 0:
                        raise Exception(f"登录失败: {login_result.get('Message')}")

                    token = login_result.get('Result', {}).get('Token')
                    logger.info(f'登录成功，获取到Token')

                    # 第二步：构建查询条件，查询该ERP的所有数据
                    filter_condition = (
                        f"cal_cERPNo = '{erp}' and cIsValidate = '是'"
                    )

                    # 设置请求头
                    headers = {
                        'leadtoken': token,
                        'Authorization': f'Bearer {token}',
                    }

                    # 第三步：获取数据
                    query_url = 'https://api.leadchina.cn/Common/InEvent'
                    query_data = {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'Guid': 'cd1312bf-6212-409a-a124-6f01917150a9',
                        'Method': 'QueryNewData',
                        'DataModel': {
                            'pageId': '1',
                            'dataModel': {
                                'pageIndex': 1,
                                'pageSize': 1000,
                                'paramList': [],
                                'fiter': filter_condition,
                                'sort': 'cPlanNo desc',
                            },
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }

                    query_payload = {'json': json.dumps(query_data)}
                    async with session.post(
                        query_url, headers=headers, data=query_payload
                    ) as query_response:
                        query_result = await query_response.json()

                        if query_result.get('Code') != 0:
                            raise Exception(
                                f"查询失败: {query_result.get('Message')}"
                            )

                        result_data = query_result.get('Result', {})
                        all_data = result_data.get('List', [])

                        logger.info(f'获取到 {len(all_data)} 条数据')

                        if not all_data:
                            return {
                                'stages': [],
                                'total_machines': 0,
                            }

                        # 获取所有机器的阶段
                        all_stages = [
                            item.get('cStage', '') for item in all_data
                        ]
                        # 过滤掉空阶段
                        valid_stages = [stage for stage in all_stages if stage]

                        return {
                            'stages': valid_stages,
                            'total_machines': len(all_data),
                        }

        except Exception as e:
            logger.error(f'获取项目 {erp} 阶段信息失败: {str(e)}')
            raise e

    async def get_multiple_projects_stages(
        self, db: AsyncSession, erp_list: List[str]
    ) -> Dict[str, Dict[str, Any]]:
        """
        批量获取多个项目的阶段信息 - 使用SQL IN语句优化
        包含反机ERP的阶段信息合并到正机结果中

        Args:
            db: 数据库会话
            erp_list: ERP号列表

        Returns:
            Dict[str, Dict[str, Any]]: 以ERP号为key的字典，包含每个项目的阶段信息
        """
        if not erp_list:
            return {}

        try:
            # 第一步：获取ERP base info，找出所有相关的反机ERP，同时获取description信息
            erp_base_info_sql = """
            SELECT
                e.ERP,
                e.second_erp,
                e.型号 as model,
                f1.description,
                f1.id,
                f1.start_time,
                f1.root_group,
                f1.gitlab_id
            FROM
                erp_base_info e
            LEFT JOIN (
                SELECT
                    f1.description,
                    f2.name as project_name,
                    f1.id,
                    f1.start_time,
                    f1.root_group,
                    f1.gitlab_id
                FROM flask_git_delivery_stage_description f1
                LEFT JOIN flask_project_managers f2 ON f1.gitlab_id = f2.gitlab_id
                WHERE f2.type = 1
                    AND f2.name IS NOT NULL
                    AND f2.name != ""
            ) f1 ON UPPER(SUBSTRING_INDEX(e.型号, "/", 1)) = UPPER(f1.project_name)
            WHERE
                e.ERP IN ({})
            """.format(
                ','.join([f"'{erp}'" for erp in erp_list])
            )

            result = await db.execute(text(erp_base_info_sql))
            base_info_rows = result.mappings().all()

            # 构建ERP映射关系：正机ERP -> [正机ERP, 反机ERP1, 反机ERP2, ...]
            erp_mapping = {}  # 正机ERP -> 所有相关ERP列表
            erp_descriptions = {}  # 正机ERP -> description
            erp_additional_info = {}  # 正机ERP -> 其他字段信息
            all_erps_to_query = set(erp_list)  # 需要查询的所有ERP（包括反机）

            for row in base_info_rows:
                main_erp = row['ERP']
                second_erp = row['second_erp']
                description = row['description']

                # 获取其他字段
                additional_info = {
                    'id': row['id'],
                    'start_time': row['start_time'],
                    'root_group': row['root_group'],
                    'gitlab_id': row['gitlab_id']
                }

                # 初始化映射，正机ERP总是包含自己
                if main_erp not in erp_mapping:
                    erp_mapping[main_erp] = [main_erp]

                # 保存description信息和其他字段信息
                erp_descriptions[main_erp] = description or ''
                erp_additional_info[main_erp] = additional_info

                # 如果有反机ERP，添加到映射和查询列表中
                if (
                    second_erp
                    and second_erp.strip() != '无'
                    and second_erp.strip() != '/'
                ):
                    # second_erp可能是逗号分隔的多个ERP
                    second_erps = [
                        erp.strip()
                        for erp in second_erp.split('/')
                        if erp.strip()
                    ]
                    erp_mapping[main_erp].extend(second_erps)
                    all_erps_to_query.update(second_erps)

            # 对于没有在base_info中找到的ERP，也要添加到映射中
            for erp in erp_list:
                if erp not in erp_mapping:
                    erp_mapping[erp] = [erp]
                if erp not in erp_descriptions:
                    erp_descriptions[erp] = ''
                if erp not in erp_additional_info:
                    erp_additional_info[erp] = {
                        'id': None,
                        'start_time': None,
                        'root_group': None,
                        'gitlab_id': None
                    }

            logger.info(f'原始ERP列表: {erp_list}')
            logger.info(f'包含反机后的完整ERP列表: {list(all_erps_to_query)}')
            logger.info(f'ERP映射关系: {erp_mapping}')

            # 第二步：使用完整的ERP列表调用API
            all_stages_data = await self._get_multiple_projects_stages_via_api(
                list(all_erps_to_query)
            )

            # 第三步：合并反机数据到正机结果中
            final_results = {}

            for main_erp in erp_list:
                related_erps = erp_mapping.get(main_erp, [main_erp])

                # 合并所有相关ERP的阶段数据
                combined_stages = []
                combined_total_machines = 0

                for related_erp in related_erps:
                    erp_data = all_stages_data.get(
                        related_erp, {'stages': [], 'total_machines': 0}
                    )
                    combined_stages.extend(erp_data.get('stages', []))
                    combined_total_machines += erp_data.get(
                        'total_machines', 0
                    )

                # 获取额外的字段信息
                additional_info = erp_additional_info.get(main_erp, {})

                final_results[main_erp] = {
                    'stages': combined_stages,
                    'total_machines': combined_total_machines,
                    'description': erp_descriptions.get(main_erp, ''),
                    'id': additional_info.get('id'),
                    'start_time': additional_info.get('start_time'),
                    'root_group': additional_info.get('root_group'),
                    'gitlab_id': additional_info.get('gitlab_id'),
                }

                logger.info(
                    f'ERP {main_erp} 合并了 {len(related_erps)} 个相关ERP的数据: {related_erps}'
                )

            logger.info(f'成功处理了 {len(final_results)} 个项目的阶段信息（包含反机数据）')
            return final_results

        except Exception as e:
            logger.error(f'批量获取项目阶段信息失败: {str(e)}')
            # 返回空结果而不是抛出异常
            return {
                erp: {
                    'stages': [],
                    'total_machines': 0,
                    'description': '',
                    'id': None,
                    'start_time': None,
                    'root_group': None,
                    'gitlab_id': None
                }
                for erp in erp_list
            }

    async def _get_multiple_projects_stages_via_api(
        self, erp_list: List[str]
    ) -> Dict[str, Dict[str, Any]]:
        """
        通过API批量获取多个项目的阶段信息 - 优化版本
        """
        results = {}

        try:
            # 第一步：登录获取token（只登录一次）
            login_url = 'https://api.leadchina.cn/Login/LoginOn'
            login_payload = {
                'json': json.dumps(
                    {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'User': {
                            'Domain': '',
                            'UserId': '1297844',
                            'Password': 'FCBfcb990607',
                            'ValiCode': '',
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }
                )
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    login_url, data=login_payload
                ) as login_response:
                    login_result = await login_response.json()

                    if login_result.get('Code') != 0:
                        raise Exception(f"登录失败: {login_result.get('Message')}")

                    token = login_result.get('Result', {}).get('Token')
                    logger.info(f'登录成功，开始批量查询 {len(erp_list)} 个项目')

                    # 设置请求头
                    headers = {
                        'leadtoken': token,
                        'Authorization': f'Bearer {token}',
                    }

                    # 第二步：批量查询（构建IN条件）
                    erp_conditions = "', '".join(erp_list)
                    filter_condition = f"cal_cERPNo IN ('{erp_conditions}') and cIsValidate = '是'"

                    query_url = 'https://api.leadchina.cn/Common/InEvent'
                    query_data = {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'Guid': 'cd1312bf-6212-409a-a124-6f01917150a9',
                        'Method': 'QueryNewData',
                        'DataModel': {
                            'pageId': '1',
                            'dataModel': {
                                'pageIndex': 1,
                                'pageSize': 10000,  # 增大页面大小以获取更多数据
                                'paramList': [],
                                'fiter': filter_condition,
                                'sort': 'cPlanNo desc',
                            },
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }

                    query_payload = {'json': json.dumps(query_data)}
                    async with session.post(
                        query_url, headers=headers, data=query_payload
                    ) as query_response:
                        query_result = await query_response.json()

                        if query_result.get('Code') != 0:
                            raise Exception(
                                f"查询失败: {query_result.get('Message')}"
                            )

                        result_data = query_result.get('Result', {})
                        all_data = result_data.get('List', [])

                        logger.info(f'批量获取到 {len(all_data)} 条数据')

                        # 第三步：按ERP分组整理数据
                        for item in all_data:
                            erp = item.get('cal_cERPNo', '')
                            stage = item.get('cStage', '')

                            if erp not in results:
                                results[erp] = {
                                    'stages': [],
                                    'total_machines': 0,
                                }

                            if stage:
                                results[erp]['stages'].append(stage)
                            results[erp]['total_machines'] += 1

                        # 为没有数据的ERP添加空结果
                        for erp in erp_list:
                            if erp not in results:
                                results[erp] = {
                                    'stages': [],
                                    'total_machines': 0,
                                }

                        logger.info(f'成功处理了 {len(results)} 个项目的阶段信息')
                        return results

        except Exception as e:
            logger.error(f'API批量获取失败: {str(e)}')
            # 返回空结果
            return {
                erp: {'stages': [], 'total_machines': 0} for erp in erp_list
            }

    # 厂内调试
    async def check_field_debugging(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, Any]:
        """
        场内调试检查 - 通过API接口方式
        根据ERP查询所有阶段，需要所有机器的阶段都是G开头
        """
        try:
            # 第一步：登录获取token
            login_url = 'https://api.leadchina.cn/Login/LoginOn'
            login_payload = {
                'json': json.dumps(
                    {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'User': {
                            'Domain': '',
                            'UserId': '1297844',
                            'Password': 'FCBfcb990607',
                            'ValiCode': '',
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }
                )
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    login_url, data=login_payload
                ) as login_response:
                    login_result = await login_response.json()

                    if login_result.get('Code') != 0:
                        raise Exception(f"登录失败: {login_result.get('Message')}")

                    token = login_result.get('Result', {}).get('Token')
                    logger.info(f'登录成功，获取到Token')

                    # 第二步：构建查询条件，查询该ERP的所有数据
                    filter_condition = (
                        f"cal_cERPNo = '{erp}' and cIsValidate = '是'"
                    )

                    # 设置请求头
                    headers = {
                        'leadtoken': token,
                        'Authorization': f'Bearer {token}',
                    }

                    # 第三步：获取数据
                    query_url = 'https://api.leadchina.cn/Common/InEvent'
                    query_data = {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'Guid': 'cd1312bf-6212-409a-a124-6f01917150a9',
                        'Method': 'QueryNewData',
                        'DataModel': {
                            'pageId': '1',
                            'dataModel': {
                                'pageIndex': 1,
                                'pageSize': 1000,
                                'paramList': [],
                                'fiter': filter_condition,
                                'sort': 'cPlanNo desc',
                            },
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }

                    query_payload = {'json': json.dumps(query_data)}
                    async with session.post(
                        query_url, headers=headers, data=query_payload
                    ) as query_response:
                        query_result = await query_response.json()

                        if query_result.get('Code') != 0:
                            raise Exception(
                                f"查询失败: {query_result.get('Message')}"
                            )

                        result_data = query_result.get('Result', {})
                        all_data = result_data.get('List', [])

                        logger.info(f'获取到 {len(all_data)} 条数据')

                        if not all_data:
                            return {
                                'is_complete': False,
                                'message': f'未找到ERP {erp} 的数据',
                                'detail': [],
                            }

                        # 第四步：检查所有机器的阶段
                        all_stages = [
                            item.get('cStage', '') for item in all_data
                        ]
                        non_empty_stages = [
                            stage for stage in all_stages if stage
                        ]

                        if not non_empty_stages:
                            return {
                                'is_complete': False,
                                'message': f'ERP {erp} 没有有效的阶段信息',
                                'detail': all_data,
                            }

                        # 检查是否所有阶段都以G开头
                        g_stages = [
                            stage
                            for stage in non_empty_stages
                            if stage.startswith('G')
                        ]
                        non_g_stages = [
                            stage
                            for stage in non_empty_stages
                            if not stage.startswith('G')
                        ]

                        is_complete = len(non_g_stages) == 0

                        # 第五步：准备返回结果
                        result_detail = {
                            'total_machines': len(all_data),
                            'total_stages': len(non_empty_stages),
                            'g_stages_count': len(g_stages),
                            'non_g_stages_count': len(non_g_stages),
                            'g_stages': g_stages,
                            'non_g_stages': non_g_stages,
                            'unique_stages': list(set(non_empty_stages)),
                            'stage_distribution': {
                                stage: non_empty_stages.count(stage)
                                for stage in set(non_empty_stages)
                            },
                            'raw_data': all_data,
                        }

                        if is_complete:
                            message = f'ERP {erp} 所有机器({len(all_data)}台)的阶段都已达到G阶段，可以进行场内调试'
                        else:
                            message = f'ERP {erp} 还有{len(non_g_stages)}个机器的阶段未达到G阶段: {list(set(non_g_stages))}'

                        return {
                            'is_complete': is_complete,
                            'message': message,
                            'detail': result_detail,
                        }

        except Exception as e:
            logger.error(f'检查场内调试状态时出错: {str(e)}')
            return {
                'is_complete': False,
                'message': f'检查场内调试状态时出错: {str(e)}',
                'detail': [],
            }

    # 等待装配
    async def check_field_waiting(
        self, db: AsyncSession, erp: str
    ) -> Dict[str, Any]:
        """
        场内调试检查 - 通过API接口方式
        根据ERP查询所有阶段，需要所有机器的阶段都不是M0-M4（小于M5的状态）
        """
        try:
            # 第一步：登录获取token
            login_url = 'https://api.leadchina.cn/Login/LoginOn'
            login_payload = {
                'json': json.dumps(
                    {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'User': {
                            'Domain': '',
                            'UserId': '1297844',
                            'Password': 'FCBfcb990607',
                            'ValiCode': '',
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }
                )
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    login_url, data=login_payload
                ) as login_response:
                    login_result = await login_response.json()

                    if login_result.get('Code') != 0:
                        raise Exception(f"登录失败: {login_result.get('Message')}")

                    token = login_result.get('Result', {}).get('Token')
                    logger.info(f'登录成功，获取到Token')

                    # 第二步：构建查询条件，查询该ERP的所有数据
                    filter_condition = (
                        f"cal_cERPNo = '{erp}' and cIsValidate = '是'"
                    )

                    # 设置请求头
                    headers = {
                        'leadtoken': token,
                        'Authorization': f'Bearer {token}',
                    }

                    # 第三步：获取数据
                    query_url = 'https://api.leadchina.cn/Common/InEvent'
                    query_data = {
                        'Device': 'Win32',
                        'Version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mac': 'Google Inc.',
                        'Guid': 'cd1312bf-6212-409a-a124-6f01917150a9',
                        'Method': 'QueryNewData',
                        'DataModel': {
                            'pageId': '1',
                            'dataModel': {
                                'pageIndex': 1,
                                'pageSize': 1000,
                                'paramList': [],
                                'fiter': filter_condition,
                                'sort': 'cPlanNo desc',
                            },
                        },
                        'AppKey': '2e22ec6c-ba31-421b-b7b1-f5e27cd7cde8',
                        'ApiVersion': '1.0.0.2',
                    }

                    query_payload = {'json': json.dumps(query_data)}
                    async with session.post(
                        query_url, headers=headers, data=query_payload
                    ) as query_response:
                        query_result = await query_response.json()

                        if query_result.get('Code') != 0:
                            raise Exception(
                                f"查询失败: {query_result.get('Message')}"
                            )

                        result_data = query_result.get('Result', {})
                        all_data = result_data.get('List', [])

                        logger.info(f'获取到 {len(all_data)} 条数据')

                        if not all_data:
                            return {
                                'is_complete': False,
                                'message': f'未找到ERP {erp} 的数据',
                                'detail': [],
                            }

                        # 第四步：检查所有机器的阶段
                        all_stages = [
                            item.get('cStage', '') for item in all_data
                        ]
                        non_empty_stages = [
                            stage for stage in all_stages if stage
                        ]

                        if not non_empty_stages:
                            return {
                                'is_complete': False,
                                'message': f'ERP {erp} 没有有效的阶段信息',
                                'detail': all_data,
                            }

                        # 检查是否有小于M5的阶段（M0-M4为未完成状态）
                        def is_stage_below_m5(stage):
                            """判断阶段是否小于M5（即M0、M1、M2、M3、M4）"""
                            if stage.startswith('M'):
                                try:
                                    # 提取M后面的数字
                                    stage_num = int(stage[1:2])  # 只取第一个数字
                                    return stage_num < 5
                                except (ValueError, IndexError):
                                    return False
                            return False

                        below_m5_stages = [
                            stage
                            for stage in non_empty_stages
                            if is_stage_below_m5(stage)
                        ]
                        completed_stages = [
                            stage
                            for stage in non_empty_stages
                            if not is_stage_below_m5(stage)
                        ]

                        is_complete = len(below_m5_stages) == 0

                        # 第五步：准备返回结果
                        result_detail = {
                            'total_machines': len(all_data),
                            'total_stages': len(non_empty_stages),
                            'completed_stages_count': len(completed_stages),
                            'below_m5_stages_count': len(below_m5_stages),
                            'completed_stages': completed_stages,
                            'below_m5_stages': below_m5_stages,
                            'unique_stages': list(set(non_empty_stages)),
                            'stage_distribution': {
                                stage: non_empty_stages.count(stage)
                                for stage in set(non_empty_stages)
                            },
                            'raw_data': all_data,
                        }

                        if is_complete:
                            message = f'ERP {erp} 所有机器({len(all_data)}台)的阶段都已达到M5或以上阶段，可以进行场内调试'
                        else:
                            unique_below_m5 = list(set(below_m5_stages))
                            message = f'ERP {erp} 还有{len(below_m5_stages)}个机器的阶段小于M5: {unique_below_m5}'

                        return {
                            'is_complete': is_complete,
                            'message': message,
                            'detail': result_detail,
                        }

        except Exception as e:
            logger.error(f'检查场内调试状态时出错: {str(e)}')
            return {
                'is_complete': False,
                'message': f'检查场内调试状态时出错: {str(e)}',
                'detail': [],
            }


status = CRUDStatus(None)
