from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from typing import Optional, List
from database.models import SimulationData
from schemas.simulation import SimulationDataCreate, SimulationDataUpdate


class SimulationDataCRUD:
    """仿真数据CRUD操作"""

    async def create_simulation_data(
        self, db: AsyncSession, simulation_data: SimulationDataCreate
    ) -> SimulationData:
        """创建仿真数据"""
        db_obj = SimulationData(**simulation_data.model_dump())
        db.add(db_obj)
        await db.flush()
        await db.refresh(db_obj)
        return db_obj

    async def get_simulation_data(
        self, db: AsyncSession, simulation_id: int
    ) -> Optional[SimulationData]:
        """根据ID获取仿真数据"""
        query = select(SimulationData).where(
            SimulationData.id == simulation_id
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_simulation_data_by_node(
        self, db: AsyncSession, node_instance_id: int
    ) -> Optional[SimulationData]:
        """根据节点实例ID获取仿真数据"""
        query = select(SimulationData).where(
            SimulationData.node_instance_id == node_instance_id
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_simulation_data_by_project(
        self, db: AsyncSession, project_id: str
    ) -> List[SimulationData]:
        """根据项目ID获取所有仿真数据"""
        query = (
            select(SimulationData)
            .where(SimulationData.project_id == project_id)
            .order_by(SimulationData.created_at.desc())
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_simulation_data_by_erp(
        self, db: AsyncSession, erp: str
    ) -> Optional[SimulationData]:
        """根据ERP获取仿真数据"""
        query = select(SimulationData).where(SimulationData.project_id == erp)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_all_simulation_data(
        self, db: AsyncSession, skip: int = 0, limit: int = 1000
    ) -> List[SimulationData]:
        """获取所有仿真数据"""
        query = (
            select(SimulationData)
            .order_by(SimulationData.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def create_simulation_data_by_erp(
        self, db: AsyncSession, erp: str, simulation_data: SimulationDataCreate
    ) -> SimulationData:
        """根据ERP创建仿真数据"""
        # 检查是否已存在该ERP的数据
        existing_data = await self.get_simulation_data_by_erp(db, erp)
        if existing_data:
            raise ValueError(f'ERP {erp} 的仿真数据已存在')

        # 如果没有提供node_instance_id，尝试查找对应的节点实例
        if not simulation_data.node_instance_id:
            from sqlalchemy import text

            # 查询仿真调试节点实例ID (node_definition_id = 47)
            node_query = text("""
                SELECT ni.id
                FROM workflow_node_instances ni
                LEFT JOIN workflow_instances wi ON ni.workflow_instance_id = wi.id
                WHERE ni.node_definition_id = 47
                AND wi.device_type = 'main_machine'
                AND wi.project_id = :erp
                AND wi.workflow_definition_id = 1
                LIMIT 1
            """)

            result = await db.execute(node_query, {'erp': erp})
            node_row = result.fetchone()

            if not node_row:
                raise ValueError(f'未找到ERP {erp} 对应的仿真调试节点')

            node_instance_id = node_row[0]
        else:
            node_instance_id = simulation_data.node_instance_id

        # 创建仿真数据
        db_obj = SimulationData(
            node_instance_id=node_instance_id,
            project_id=erp,
            **simulation_data.model_dump(exclude={'node_instance_id', 'project_id'})
        )
        db.add(db_obj)
        await db.flush()
        await db.refresh(db_obj)
        return db_obj

    async def update_simulation_data_by_erp(
        self, db: AsyncSession, erp: str, update_data: SimulationDataUpdate
    ) -> Optional[SimulationData]:
        """根据ERP更新仿真数据"""
        existing_data = await self.get_simulation_data_by_erp(db, erp)
        if not existing_data:
            raise ValueError(f'ERP {erp} 的仿真数据不存在')

        return await self.update_simulation_data(db, existing_data.id, update_data)

    async def upsert_simulation_3d_data(
        self, db: AsyncSession, erp: str, simulation_3d_data: dict
    ) -> SimulationData:
        """根据ERP更新或插入仿真三维搭建数据"""
        # 先查询是否已存在该ERP的数据
        existing_data = await self.get_simulation_data_by_erp(db, erp)

        if existing_data:
            # 如果存在，更新仿真三维搭建相关字段
            update_dict = {
                k: v for k, v in simulation_3d_data.items() if v is not None
            }
            if update_dict:
                query = (
                    update(SimulationData)
                    .where(SimulationData.project_id == erp)
                    .values(**update_dict)
                )
                await db.execute(query)
                await db.refresh(existing_data)
            return existing_data
        else:
            # 如果不存在，创建新记录
            # 注意：这里需要一个有效的node_instance_id，我们需要从仿真三维搭建节点获取
            from sqlalchemy import text

            # 查询仿真三维搭建节点实例ID (node_definition_id = 48)
            node_query = text(
                """
                SELECT ni.id
                FROM workflow_node_instances ni
                LEFT JOIN workflow_instances wi ON ni.workflow_instance_id = wi.id
                WHERE ni.node_definition_id = 48
                AND wi.device_type = 'main_machine'
                AND wi.project_id = :erp
                AND wi.workflow_definition_id = 1
                LIMIT 1
            """
            )

            result = await db.execute(node_query, {'erp': erp})
            node_row = result.fetchone()

            if not node_row:
                raise ValueError(f'未找到ERP {erp} 对应的仿真三维搭建节点')

            node_instance_id = node_row[0]

            # 创建新的仿真数据记录
            new_data = SimulationData(
                node_instance_id=node_instance_id,
                project_id=erp,
                **simulation_3d_data,
            )
            db.add(new_data)
            await db.flush()
            await db.refresh(new_data)
            return new_data

    async def update_simulation_data(
        self,
        db: AsyncSession,
        simulation_id: int,
        update_data: SimulationDataUpdate,
    ) -> Optional[SimulationData]:
        """更新仿真数据"""
        # 构建更新字典，排除None值
        update_dict = {
            k: v for k, v in update_data.model_dump().items() if v is not None
        }

        if not update_dict:
            return await self.get_simulation_data(db, simulation_id)

        query = (
            update(SimulationData)
            .where(SimulationData.id == simulation_id)
            .values(**update_dict)
        )
        await db.execute(query)
        return await self.get_simulation_data(db, simulation_id)

    async def delete_simulation_data(
        self, db: AsyncSession, simulation_id: int
    ) -> bool:
        """删除仿真数据"""
        query = delete(SimulationData).where(
            SimulationData.id == simulation_id
        )
        result = await db.execute(query)
        return result.rowcount > 0


# 实例化CRUD对象
simulation_data = SimulationDataCRUD()
