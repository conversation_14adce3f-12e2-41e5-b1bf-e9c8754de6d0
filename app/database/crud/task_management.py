# app/crud/task_management.py
from database.crud.base import CRUDBase

from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update
from datetime import datetime
from typing import List, Set
from database.models import (
    NodeInstance,
    EdgeInstance,
    NodeStatus,
    WorkflowInstance,
    WorkflowStatus,
    InputLogicType,
    NodeDefinition,
)
import schemas
from services import task_checker
from database.crud import notification_management


class NodeStatusService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.processed_nodes: Set[int] = set()  # 避免循环处理

    async def get_node_definition(self, node_instance_id: int):
        """获取节点定义信息"""
        result = await self.db.execute(
            select(NodeInstance)
            .join(NodeInstance.definition)
            .filter(NodeInstance.id == node_instance_id)
        )
        return result.scalar_one_or_none()

    async def get_incoming_edges(
        self, node_instance_id: int
    ) -> List[EdgeInstance]:
        """获取节点的所有入边"""
        result = await self.db.execute(
            select(EdgeInstance).filter(
                EdgeInstance.to_node_instance_id == node_instance_id
            )
        )
        return result.scalars().all()

    async def get_outgoing_edges(
        self, node_instance_id: int
    ) -> List[EdgeInstance]:
        """获取节点的所有出边"""
        result = await self.db.execute(
            select(EdgeInstance).filter(
                EdgeInstance.from_node_instance_id == node_instance_id
            )
        )
        return result.scalars().all()

    async def get_downstream_nodes(
        self, node_instance_id: int
    ) -> List[NodeInstance]:
        """获取下游节点及其相关信息

        Args:
            node_instance_id: 节点实例ID

        Returns:
            包含下游节点实例及其相关信息的列表
        """
        result = await self.db.execute(
            select(
                NodeInstance,
                WorkflowInstance.project_id,
                NodeDefinition.task_url,
                NodeDefinition.name.label('node_name'),
            )
            .join(
                EdgeInstance,
                EdgeInstance.to_node_instance_id == NodeInstance.id,
            )
            .join(
                WorkflowInstance,
                WorkflowInstance.id == NodeInstance.workflow_instance_id,
            )
            .join(
                NodeDefinition,
                NodeDefinition.id == NodeInstance.node_definition_id,
            )
            .filter(EdgeInstance.from_node_instance_id == node_instance_id)
        )
        return result.all()

    async def check_node_ready(self, node_instance: NodeInstance) -> bool:
        """检查节点是否满足启动条件"""
        # 获取所有入边对应的源节点
        incoming_edges = await self.get_incoming_edges(node_instance.id)
        if not incoming_edges:
            return True  # 没有入边的节点（如开始节点）可以直接启动

        # 获取节点的输入逻辑类型
        node_def = await self.get_node_definition(node_instance.id)
        input_logic = node_def.definition.input_logic

        # 检查所有源节点的状态
        completed_count = 0
        for edge in incoming_edges:
            from_node = await self.db.get(
                NodeInstance, edge.from_node_instance_id
            )
            if from_node.status == NodeStatus.completed:
                completed_count += 1

            # 检查条件边
            if edge.condition and from_node.status != NodeStatus.completed:
                return False

        # 根据输入逻辑判断是否可以启动
        if input_logic == InputLogicType.AND:
            return completed_count == len(incoming_edges)
        else:  # OR logic
            return completed_count > 0

    async def update_workflow_status(self, workflow_instance_id: int):
        """更新工作流状态"""
        # 获取工作流所有节点
        nodes_result = await self.db.execute(
            select(NodeInstance).filter(
                NodeInstance.workflow_instance_id == workflow_instance_id
            )
        )
        nodes = nodes_result.scalars().all()

        # 检查节点状态
        all_completed = all(
            node.status == NodeStatus.completed for node in nodes
        )
        any_error = any(node.status == NodeStatus.error for node in nodes)
        any_active = any(node.status == NodeStatus.active for node in nodes)

        # 更新工作流状态
        new_status = None
        if all_completed:
            new_status = WorkflowStatus.completed
        elif any_error:
            new_status = WorkflowStatus.terminated
        elif any_active:
            new_status = WorkflowStatus.active

        if new_status:
            await self.db.execute(
                update(WorkflowInstance)
                .where(WorkflowInstance.id == workflow_instance_id)
                .values(
                    status=new_status,
                    end_time=datetime.now()
                    if new_status == WorkflowStatus.completed
                    else None,
                )
            )

    async def handle_subprocess_status(self, node_instance: NodeInstance):
        """处理子流程状态"""
        if node_instance.subprocess_instance_id:
            # 如果节点状态是完成的，检查子流程是否也完成
            subprocess_result = await self.db.execute(
                select(WorkflowInstance).filter(
                    WorkflowInstance.id == node_instance.subprocess_instance_id
                )
            )
            subprocess = subprocess_result.scalar_one_or_none()

            if subprocess:
                if node_instance.status == NodeStatus.completed:
                    # 如果节点完成，子流程也应该完成
                    if subprocess.status != WorkflowStatus.completed:
                        await self.db.execute(
                            update(WorkflowInstance)
                            .where(WorkflowInstance.id == subprocess.id)
                            .values(
                                status=WorkflowStatus.completed,
                                end_time=datetime.now(),
                            )
                        )
                elif node_instance.status == NodeStatus.active:
                    # 如果节点激活，子流程也应该激活
                    if subprocess.status == WorkflowStatus.pending:
                        await self.db.execute(
                            update(WorkflowInstance)
                            .where(WorkflowInstance.id == subprocess.id)
                            .values(
                                status=WorkflowStatus.active,
                                start_time=datetime.now(),
                            )
                        )

    async def update_node_status(
        self,
        node_id: int,
        status: NodeStatus,
        update_data: schemas.NodeInstanceUpdate = None,
    ) -> bool:
        """更新节点状态并处理级联更新"""
        if node_id in self.processed_nodes:
            return True  # 避免循环处理

        self.processed_nodes.add(node_id)

        try:
            # 1. 准备更新数据
            update_values = {'status': status}
            if update_data:
                update_values.update(update_data.dict(exclude_unset=True))

            if status == NodeStatus.completed:
                update_values['end_time'] = datetime.now()
            elif status == NodeStatus.active and not update_values.get(
                'start_time'
            ):
                update_values['start_time'] = datetime.now()

            # 2. 更新节点状态
            await self.db.execute(
                update(NodeInstance)
                .where(NodeInstance.id == node_id)
                .values(**update_values)
            )

            # 3. 获取更新后的节点实例
            result = await self.db.execute(
                select(NodeInstance).filter(NodeInstance.id == node_id)
            )
            node_instance = result.scalar_one_or_none()

            if not node_instance:
                return False

            # 4. 处理子流程状态
            await self.handle_subprocess_status(node_instance)

            nodes_need_to_notify = []
            # 5. 处理下游节点
            if status == NodeStatus.completed:
                downstream_nodes = await self.get_downstream_nodes(node_id)
                for down_node in downstream_nodes:
                    node_instance = down_node[0]  # NodeInstance对象
                    project_id = down_node[1]  # project_id
                    task_url = down_node[2]  # task_url
                    if node_instance.status == NodeStatus.pending:
                        if await self.check_node_ready(node_instance):
                            await self.update_node_status(
                                node_instance.id, NodeStatus.active
                            )
                            nodes_need_to_notify.append(node_instance.id)

            # 6. 更新工作流状态
            await self.update_workflow_status(
                node_instance.workflow_instance_id
            )
            await self.db.commit()
            await self.db.flush()

            for node_id in nodes_need_to_notify:
                # 发送通知
                notify_info = await notification_management.notification.get_task_info_by_node_id(
                    self.db, node_id
                )
                if notify_info:
                    (
                        task,
                        project_id,
                        task_url,
                        remaining_time,
                    ) = notify_info
                    # 设置默认任务URL
                    task_url = (
                        task_url
                        if task_url
                        else 'http://winddata.leadintelligent.com'
                    )
                    if task.need_approval:
                        task_url = f'{task_url}'
                    if task.assigned_to:
                        await task_checker.task_checker.send_notification(
                            self.db,
                            task.assigned_to,
                            {
                                'project_id': f'{project_id}(主机)'
                                if task.device_type == 'main_machine'
                                else f'{project_id}(出料机)',
                                'task_url': task_url,
                                'remaining_time': remaining_time,
                                'assigned_to': task.assigned_to.split(',')[0],
                                'task_name': task.name,
                            },
                            task,
                        )
            return True

        except Exception as e:
            await self.db.rollback()
            raise e
        finally:
            self.processed_nodes.remove(node_id)


class CRUDNodeInstance(CRUDBase):
    async def create_node_instance(
        self, db: AsyncSession, node: schemas.NodeInstanceCreate
    ):
        db_node = NodeInstance(**node.dict())
        db.add(db_node)
        await db.commit()
        await db.refresh(db_node)
        return db_node

    async def update_node_instance(
        self,
        db: AsyncSession,
        node_id: int,
        node_update: schemas.NodeInstanceUpdate,
    ):
        try:
            # 1. 执行更新操作
            await db.execute(
                update(self.model)
                .where(self.model.id == node_id)
                .values(**node_update.dict(exclude_unset=True))
            )

            # 2. 获取更新后的记录
            result = await db.execute(
                select(self.model).filter(self.model.id == node_id)
            )

            # 3. 提交事务
            await db.commit()

            return result.scalar_one_or_none()

        except Exception as e:
            await db.rollback()
            raise e

    async def get_node_instance(self, db: AsyncSession, node_id: int):
        result = await db.execute(
            select(self.model)
            .filter(self.model.id == node_id)
            .options(selectinload(self.model.definition))
        )
        return result.scalar_one_or_none()

    async def get_workflow_nodes(
        self, db: AsyncSession, workflow_instance_id: int
    ):
        result = await db.execute(
            select(self.model).filter(
                self.model.workflow_instance_id == workflow_instance_id
            )
        )
        return result.scalars().all()

    async def update_node_status(
        self,
        db: AsyncSession,
        node_id: int,
        status: NodeStatus,
        update_data: schemas.NodeInstanceUpdate = None,
    ):
        service = NodeStatusService(db)
        return await service.update_node_status(node_id, status, update_data)

    async def get_user_tasks(self, db: AsyncSession, user_id: str):
        result = await db.execute(
            select(self.model)
            .filter(self.model.assigned_to == user_id)
            .order_by(self.model.created_at.desc())
        )
        return result.scalars().all()


node_instance = CRUDNodeInstance(NodeInstance)
