# app/crud/gantt_service.py
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from database.crud.base import CRUDBase
from database.models import (
    WorkflowInstance,
    NodeInstance,
    NodeStatus,
    NodeType,
    WorkflowStatus,
    EdgeInstance,
)
import schemas


class CRUDGanttChart(CRUDBase):
    """甘特图数据 CRUD 操作类"""

    async def get_project_workflows(
        self,
        db: AsyncSession,
        project_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> List[WorkflowInstance]:
        """获取项目的工作流实例列表"""
        query = (
            select(WorkflowInstance)
            .options(
                selectinload(WorkflowInstance.nodes).selectinload(
                    NodeInstance.definition
                ),
                selectinload(WorkflowInstance.definition),
                selectinload(WorkflowInstance.edges),
            )
            .filter(WorkflowInstance.project_id == project_id)
        )

        if start_date:
            query = query.filter(WorkflowInstance.start_time >= start_date)
        if end_date:
            query = query.filter(WorkflowInstance.end_time <= end_date)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_workflow_with_details(
        self, db: AsyncSession, workflow_id: int
    ) -> Optional[WorkflowInstance]:
        """获取工作流实例及其所有相关数据"""
        result = await db.execute(
            select(WorkflowInstance)
            .options(
                selectinload(WorkflowInstance.nodes).selectinload(
                    NodeInstance.definition
                ),
                selectinload(WorkflowInstance.definition),
                selectinload(WorkflowInstance.edges),
            )
            .filter(WorkflowInstance.id == workflow_id)
        )
        return result.scalar_one_or_none()

    async def get_project_statistics(
        self, db: AsyncSession, project_id: str
    ) -> Dict[str, Any]:
        """获取项目的统计信息"""
        workflows_query = select(WorkflowInstance).filter(
            WorkflowInstance.project_id == project_id
        )
        result = await db.execute(workflows_query)
        workflows = result.scalars().all()

        total_workflows = len(workflows)
        active_workflows = sum(
            1 for wf in workflows if wf.status == WorkflowStatus.active
        )
        completed_workflows = sum(
            1 for wf in workflows if wf.status == WorkflowStatus.completed
        )

        # 计算平均完成时间
        completion_times = [
            (wf.end_time - wf.start_time).total_seconds() / 3600
            for wf in workflows
            if wf.end_time and wf.status == WorkflowStatus.completed
        ]
        avg_completion_time = (
            sum(completion_times) / len(completion_times)
            if completion_times
            else 0
        )

        return {
            'total_workflows': total_workflows,
            'active_workflows': active_workflows,
            'completed_workflows': completed_workflows,
            'average_completion_time': round(avg_completion_time, 2),  # 小时
            'completion_rate': round(
                completed_workflows / total_workflows * 100, 2
            )
            if total_workflows > 0
            else 0,
        }


class GanttChartService:
    """甘特图数据服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.crud = CRUDGanttChart(WorkflowInstance)

    async def get_project_gantt_data(
        self,
        project_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> schemas.GanttDataResponse:
        """获取项目甘特图数据"""
        try:
            workflows = await self.crud.get_project_workflows(
                self.db, project_id, start_date, end_date
            )

            tasks: List[schemas.GanttTask] = []
            links: List[schemas.GanttLink] = []
            task_id_counter = 1

            # 处理工作流和节点数据
            for workflow in workflows:
                workflow_task = self._create_workflow_task(workflow)
                tasks.append(workflow_task)

                # 处理节点任务
                node_tasks, node_links = await self._process_workflow_nodes(
                    workflow, workflow_task['id']
                )
                tasks.extend(node_tasks)
                links.extend(node_links)
                task_id_counter += len(node_links)

            # 创建响应数据
            settings = schemas.GanttSettings(
                project_id=project_id,
                start_date=min(
                    (t.get('start_date') for t in tasks), default=None
                ),
                end_date=max((t.get('end_date') for t in tasks), default=None),
            )

            return schemas.GanttDataResponse(
                data=tasks, links=links, settings=settings
            )

        except Exception as e:
            print(f'获取甘特图数据时发生错误: {str(e)}')
            raise e

    def _create_workflow_task(
        self, workflow: WorkflowInstance
    ) -> Dict[str, Any]:
        """创建工作流任务数据"""
        return {
            'id': f'wf_{workflow.id}',
            'text': workflow.definition.name
            if workflow.definition
            else f'Workflow {workflow.id}',
            'start_date': workflow.start_time.strftime('%Y-%m-%d %H:%M'),
            'end_date': (workflow.end_time or datetime.now()).strftime(
                '%Y-%m-%d %H:%M'
            ),
            'type': 'project',
            'progress': self._calculate_workflow_progress(workflow),
            'status': workflow.status.value,
            'open': True,  # 默认展开项目组
        }

    async def _process_workflow_nodes(
        self, workflow: WorkflowInstance, workflow_task_id: str
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """处理工作流节点数据"""
        tasks = []
        links = []
        link_id = 1

        # 添加 edges 的预加载
        query = (
            select(NodeInstance)
            .options(
                selectinload(NodeInstance.outgoing_edges).selectinload(
                    EdgeInstance.to_node_instance
                ),
                selectinload(NodeInstance.definition),
            )
            .filter(NodeInstance.workflow_instance_id == workflow.id)
        )
        result = await self.db.execute(query)
        nodes = result.scalars().all()

        for node in nodes:
            if node.definition.type in [NodeType.start, NodeType.end]:
                continue

            # 创建节点任务
            node_task = self._create_node_task(node, workflow_task_id)
            tasks.append(node_task)

            # 处理节点间依赖关系
            for edge in node.outgoing_edges:
                if (
                    edge.to_node_instance
                    and edge.to_node_instance.definition.type != NodeType.end
                ):
                    links.append(
                        {
                            'id': f'link_{link_id}',
                            'source': f'node_{node.id}',
                            'target': f'node_{edge.to_node_instance_id}',
                            'type': '0',
                        }
                    )
                    link_id += 1

        return tasks, links

    def _create_node_task(
        self, node: NodeInstance, workflow_task_id: str
    ) -> Dict[str, Any]:
        """创建节点任务数据"""
        start_date = node.start_time or node.workflow.start_time
        end_date = node.end_time
        if not end_date:
            if node.definition.expected_duration:
                end_date = start_date + timedelta(
                    hours=node.definition.expected_duration
                )
            else:
                end_date = datetime.now()

        return {
            'id': f'node_{node.id}',
            'text': node.definition.name,
            'start_date': start_date.strftime('%Y-%m-%d %H:%M'),
            'end_date': end_date.strftime('%Y-%m-%d %H:%M'),
            'parent': workflow_task_id,
            'progress': self._calculate_node_progress(node),
            'type': 'task',
            'status': node.status.value,
            'assigned_to': node.assigned_to,
            'color': node.definition.color,
            'duration': node.actual_duration
            or node.definition.expected_duration,
            'duration_unit': 'hours',
            'expected_duration': node.definition.expected_duration,
            'need_approval': node.definition.need_approval,
        }

    async def get_workflow_timeline(
        self, workflow_id: int
    ) -> schemas.TimelineResponse:
        """获取工作流时间线数据"""
        try:
            workflow = await self.crud.get_workflow_with_details(
                self.db, workflow_id
            )
            if not workflow:
                raise ValueError(f'Workflow instance {workflow_id} not found')

            timeline_events = self._create_timeline_events(workflow)

            return schemas.TimelineResponse(
                workflow_id=workflow_id,
                workflow_name=workflow.definition.name,
                timeline=sorted(timeline_events, key=lambda x: x['date']),
                status=workflow.status.value,
                duration=self._calculate_workflow_duration(workflow),
            )

        except Exception as e:
            print(f'获取时间线数据时发生错误: {str(e)}')
            raise e

    def _create_timeline_events(
        self, workflow: WorkflowInstance
    ) -> List[schemas.TimelineEvent]:
        """创建时间线事件数据"""
        events = []

        # 工作流启动事件
        events.append(
            {
                'date': workflow.start_time.strftime('%Y-%m-%d %H:%M'),
                'type': 'workflow_start',
                'text': f"工作流 '{workflow.definition.name}' 启动",
                'category': 'workflow',
            }
        )

        # 节点事件
        for node in workflow.nodes:
            if node.definition.type in [NodeType.start, NodeType.end]:
                continue

            if node.start_time:
                events.append(
                    {
                        'date': node.start_time.strftime('%Y-%m-%d %H:%M'),
                        'type': 'node_start',
                        'text': f"节点 '{node.definition.name}' 开始",
                        'assigned_to': node.assigned_to,
                        'status': node.status.value,
                        'category': 'node',
                    }
                )

            if node.end_time:
                events.append(
                    {
                        'date': node.end_time.strftime('%Y-%m-%d %H:%M'),
                        'type': 'node_end',
                        'text': f"节点 '{node.definition.name}' 完成",
                        'duration': node.actual_duration,
                        'status': node.status.value,
                        'category': 'node',
                    }
                )

        # 工作流结束事件
        if workflow.end_time:
            events.append(
                {
                    'date': workflow.end_time.strftime('%Y-%m-%d %H:%M'),
                    'type': 'workflow_end',
                    'text': f"工作流 '{workflow.definition.name}' 完成",
                    'category': 'workflow',
                }
            )

        return events

    def _calculate_workflow_progress(
        self, workflow: WorkflowInstance
    ) -> float:
        """计算工作流进度"""
        if not workflow.nodes:
            return 0.0

        completed_nodes = sum(
            1
            for node in workflow.nodes
            if node.status == NodeStatus.completed
            and node.definition.type not in [NodeType.start, NodeType.end]
        )
        total_nodes = sum(
            1
            for node in workflow.nodes
            if node.definition.type not in [NodeType.start, NodeType.end]
        )

        return (
            round(completed_nodes / total_nodes * 100, 2)
            if total_nodes > 0
            else 0.0
        )

    def _calculate_node_progress(self, node: NodeInstance) -> float:
        """计算节点进度"""
        if node.status == NodeStatus.completed:
            return 100.0
        elif node.status == NodeStatus.active:
            if node.start_time and node.definition.expected_duration:
                elapsed_time = (
                    datetime.now() - node.start_time
                ).total_seconds() / 3600
                progress = (
                    elapsed_time / node.definition.expected_duration
                ) * 100
                return round(min(progress, 99.0), 2)
            return 50.0
        return 0.0

    def _calculate_workflow_duration(
        self, workflow: WorkflowInstance
    ) -> Optional[float]:
        """计算工作流持续时间（小时）"""
        if workflow.start_time and workflow.end_time:
            return round(
                (workflow.end_time - workflow.start_time).total_seconds()
                / 3600,
                2,
            )
        return None


# 创建全局服务实例
gantt_crud = CRUDGanttChart(WorkflowInstance)
