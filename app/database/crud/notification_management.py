from sqlalchemy.orm import joinedload
from sqlalchemy.future import select
from database.crud.base import CRUDBase
from database.models import (
    NodeInstance,
    NodeStatus,
    WorkflowInstance,
    MachineTypeDuration,
    NodeDefinition,
)
import schemas
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_
from datetime import datetime, timezone
from utils.work_time_utils import WorkTimeCalculator


class CRUDNotification(CRUDBase):
    def to_beijing_time(self, dt: datetime) -> datetime:
        """将时间转换为北京时间"""
        if dt is None:
            return None
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(WorkTimeCalculator.BEIJING_TZ)

    async def get_overdue_tasks(self, db: AsyncSession):
        now = datetime.now(WorkTimeCalculator.BEIJING_TZ)

        result = await db.execute(
            select(
                NodeInstance,
                WorkflowInstance.project_id,
                NodeDefinition.task_url,
                MachineTypeDuration.expected_duration,
            )
            .options(
                joinedload(NodeInstance.definition),
                joinedload(NodeInstance.workflow),
            )
            .join(
                WorkflowInstance,
                NodeInstance.workflow_instance_id == WorkflowInstance.id,
            )
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .outerjoin(
                MachineTypeDuration,
                and_(
                    MachineTypeDuration.node_definition_id
                    == NodeInstance.node_definition_id,
                    MachineTypeDuration.machine_type
                    == WorkflowInstance.machine_type,
                    MachineTypeDuration.device_type
                    == WorkflowInstance.device_type,
                ),
            )
            .where(
                and_(
                    NodeInstance.status == NodeStatus.active,
                    NodeInstance.start_time.isnot(None),
                    MachineTypeDuration.expected_duration.isnot(None),
                )
            )
        )
        tasks = result.all()

        overdue_tasks = []
        for task, project_id, task_url, duration in tasks:
            if task.start_time:
                # 将UTC时间转换为北京时间
                start_time = self.to_beijing_time(task.start_time)
                # 使用北京时间计算预期结束时间
                expected_end_time = (
                    WorkTimeCalculator.calculate_working_duration(
                        start_time, duration
                    )
                )
                if expected_end_time < now:
                    remaining_time = 0
                    overdue_tasks.append(
                        (task, project_id, task_url, remaining_time)
                    )

        return [
            (
                schemas.TaskNotification.from_orm(task),
                project_id,
                task_url,
                remaining_time,
            )
            for task, project_id, task_url, remaining_time in overdue_tasks
        ]

    async def get_tasks_due_soon(
        self, db: AsyncSession, hours: int = 1, min_hours: float = 0
    ):
        now = datetime.now(WorkTimeCalculator.BEIJING_TZ)
        check_time = WorkTimeCalculator.calculate_working_duration(
            now, hours * 60
        )
        min_check_time = WorkTimeCalculator.calculate_working_duration(
            now, min_hours * 60
        )

        result = await db.execute(
            select(
                NodeInstance,
                WorkflowInstance.project_id,
                NodeDefinition.task_url,
                MachineTypeDuration.expected_duration,
            )
            .options(
                joinedload(NodeInstance.definition),
                joinedload(NodeInstance.workflow),
            )
            .join(
                WorkflowInstance,
                NodeInstance.workflow_instance_id == WorkflowInstance.id,
            )
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .outerjoin(
                MachineTypeDuration,
                and_(
                    MachineTypeDuration.node_definition_id
                    == NodeInstance.node_definition_id,
                    MachineTypeDuration.machine_type
                    == WorkflowInstance.machine_type,
                    MachineTypeDuration.device_type
                    == WorkflowInstance.device_type,
                ),
            )
            .where(
                and_(
                    NodeInstance.status == NodeStatus.active,
                    NodeInstance.start_time.isnot(None),
                    MachineTypeDuration.expected_duration.isnot(None),
                )
            )
        )
        tasks = result.all()

        due_soon_tasks = []
        for task, project_id, task_url, duration in tasks:
            if task.start_time:
                # 将UTC时间转换为北京时间
                start_time = self.to_beijing_time(task.start_time)
                # 使用北京时间计算预期结束时间
                expected_end_time = (
                    WorkTimeCalculator.calculate_working_duration(
                        start_time, duration
                    )
                )
                # 只处理未超时的任务，并且在指定的时间范围内
                if (
                    expected_end_time > now
                    and min_check_time < expected_end_time <= check_time
                ):
                    remaining_time = round(
                        (expected_end_time - now).total_seconds() / 60 / 60, 2
                    )
                    due_soon_tasks.append(
                        (task, project_id, task_url, remaining_time)
                    )

        return [
            (
                schemas.TaskNotification.from_orm(task),
                project_id,
                task_url,
                remaining_time,
            )
            for task, project_id, task_url, remaining_time in due_soon_tasks
        ]

    async def get_task_expected_duration(
        self, db: AsyncSession, task: NodeInstance
    ) -> int:
        """获取任务的预期工时"""
        workflow = await db.get(WorkflowInstance, task.workflow_instance_id)
        if not workflow:
            return None

        result = await db.execute(
            select(MachineTypeDuration.expected_duration).where(
                and_(
                    MachineTypeDuration.node_definition_id
                    == task.node_definition_id,
                    MachineTypeDuration.machine_type == workflow.machine_type,
                )
            )
        )
        duration = result.scalar_one_or_none()
        return duration

    async def get_task_info_by_node_id(self, db: AsyncSession, node_id: int):
        """
        通过node_id获取任务的详细信息，包括剩余时间
        返回: (task_notification, project_id, task_url, remaining_time)
        """
        now = datetime.now(WorkTimeCalculator.BEIJING_TZ)

        result = await db.execute(
            select(
                NodeInstance,
                WorkflowInstance.project_id,
                NodeDefinition.task_url,
                MachineTypeDuration.expected_duration,
            )
            .options(
                joinedload(NodeInstance.definition),
                joinedload(NodeInstance.workflow),
            )
            .join(
                WorkflowInstance,
                NodeInstance.workflow_instance_id == WorkflowInstance.id,
            )
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .outerjoin(
                MachineTypeDuration,
                and_(
                    MachineTypeDuration.node_definition_id
                    == NodeInstance.node_definition_id,
                    MachineTypeDuration.machine_type
                    == WorkflowInstance.machine_type,
                    MachineTypeDuration.device_type
                    == WorkflowInstance.device_type,
                ),
            )
            .where(
                and_(
                    NodeInstance.id == node_id,
                    NodeInstance.status == NodeStatus.active,
                    NodeInstance.start_time.isnot(None),
                    MachineTypeDuration.expected_duration.isnot(None),
                )
            )
        )

        task_info = result.first()
        if not task_info:
            return None

        task, project_id, task_url, duration = task_info

        if not task.start_time:
            return None

        # 将UTC时间转换为北京时间
        start_time = self.to_beijing_time(task.start_time)
        # 使用北京时间计算预期结束时间
        expected_end_time = WorkTimeCalculator.calculate_working_duration(
            start_time, duration
        )

        if expected_end_time < now:
            remaining_time = 0
        else:
            remaining_time = round(
                (expected_end_time - now).total_seconds() / 60 / 60, 2
            )

        return (
            schemas.TaskNotification.from_orm(task),
            project_id,
            task_url,
            remaining_time,
        )


notification = CRUDNotification(NodeInstance)
