# app/crud/project_report_service.py
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import schemas

from database.models import (
    WorkflowInstance,
    NodeInstance,
    NodeDefinition,
    NodeStatus,
)


class ProjectReportService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_projects_task_report(
        self,
        project_ids: Optional[List[str]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        task_status: Optional[schemas.NodeStatus] = None,
    ) -> Dict[str, Any]:
        """
        获取项目任务报表数据

        Args:
            project_ids: 项目ID列表，如果为None则获取所有项目
            start_date: 开始日期过滤
            end_date: 结束日期过滤

        Returns:
            Dict 包含项目任务统计和详细数据
        """
        try:
            # 构建基础查询
            base_query = select(WorkflowInstance).options(
                selectinload(WorkflowInstance.nodes).selectinload(
                    NodeInstance.definition
                ),
                selectinload(WorkflowInstance.definition),
            )

            # 添加过滤条件
            if project_ids:
                base_query = base_query.filter(
                    WorkflowInstance.project_id.in_(project_ids)
                )
            if start_date:
                base_query = base_query.filter(
                    WorkflowInstance.start_time >= start_date
                )
            if end_date:
                base_query = base_query.filter(
                    WorkflowInstance.start_time <= end_date
                )

            result = await self.db.execute(base_query)
            workflows = result.scalars().all()

            # 处理项目数据
            project_stats = defaultdict(
                lambda: {
                    'project_id': '',
                    'total_tasks': 0,
                    'completed_tasks': 0,
                    'delayed_tasks': 0,
                    'total_duration': 0,
                    'completed_duration': 0,
                    'tasks': [],
                }
            )

            # 统计数据
            for workflow in workflows:
                stats = project_stats[workflow.project_id]
                stats['project_id'] = workflow.project_id

                for node in workflow.nodes:
                    # if node.definition.type in [NodeType.start, NodeType.end]:
                    #     continue

                    if task_status and node.status.value != task_status.value:
                        continue
                    # 更新统计数据
                    stats['total_tasks'] += 1

                    if node.status == NodeStatus.completed:
                        stats['completed_tasks'] += 1
                        if node.actual_duration:
                            stats['completed_duration'] += node.actual_duration

                    if (
                        node.actual_duration
                        and node.definition.expected_duration
                        and node.actual_duration
                        > node.definition.expected_duration
                    ):
                        stats['delayed_tasks'] += 1

                    # 添加任务详情
                    stats['tasks'].append(
                        {
                            'task_id': node.id,
                            'workflow_name': workflow.definition.name,
                            'task_name': node.definition.name,
                            'assigned_to': node.assigned_to,
                            'status': node.status.value,
                            'planned_duration': node.definition.expected_duration,
                            'actual_duration': node.actual_duration,
                            'start_time': node.start_time.isoformat()
                            if node.start_time
                            else None,
                            'end_time': node.end_time.isoformat()
                            if node.end_time
                            else None,
                            'delay': (
                                node.actual_duration
                                - node.definition.expected_duration
                                if node.actual_duration
                                and node.definition.expected_duration
                                else 0
                            ),
                        }
                    )

            # 计算汇总指标
            projects_report = []
            for project_id, stats in project_stats.items():
                total_tasks = stats['total_tasks']
                if total_tasks > 0:
                    completion_rate = (
                        stats['completed_tasks'] / total_tasks
                    ) * 100
                    delay_rate = (stats['delayed_tasks'] / total_tasks) * 100
                    avg_completion_time = (
                        stats['completed_duration'] / stats['completed_tasks']
                        if stats['completed_tasks'] > 0
                        else 0
                    )
                else:
                    completion_rate = delay_rate = avg_completion_time = 0

                projects_report.append(
                    {
                        'project_id': project_id,
                        'total_tasks': total_tasks,
                        'completion_rate': round(completion_rate, 2),
                        'delay_rate': round(delay_rate, 2),
                        'avg_completion_time': round(avg_completion_time, 2),
                        'tasks': sorted(
                            stats['tasks'],
                            key=lambda x: x['start_time'] or '',
                            reverse=True,
                        ),
                    }
                )

            return {
                'projects': sorted(
                    projects_report,
                    key=lambda x: x['completion_rate'],
                    reverse=True,
                ),
                'total_projects': len(projects_report),
                'total_tasks': sum(p['total_tasks'] for p in projects_report),
                'avg_completion_rate': round(
                    sum(p['completion_rate'] for p in projects_report)
                    / len(projects_report)
                    if projects_report
                    else 0,
                    2,
                ),
            }

        except Exception as e:
            print(f'获取项目任务报表时发生错误: {str(e)}')
            raise e

    async def get_project_task_trends(
        self, project_id: str, days: int = 30
    ) -> Dict[str, Any]:
        """
        获取项目任务趋势数据

        Args:
            project_id: 项目ID
            days: 统计天数

        Returns:
            Dict 包含任务完成趋势数据
        """
        try:
            start_date = datetime.now() - timedelta(days=days)

            # 查询时间范围内的节点数据
            query = (
                select(NodeInstance)
                .join(
                    WorkflowInstance,
                    NodeInstance.workflow_instance_id == WorkflowInstance.id,
                )
                .join(
                    NodeDefinition,
                    NodeInstance.node_definition_id == NodeDefinition.id,
                )
                .filter(
                    and_(
                        WorkflowInstance.project_id == project_id,
                        NodeInstance.end_time >= start_date,
                        # NodeDefinition.type.notin_(
                        #     [NodeType.start, NodeType.end]
                        # ),
                    )
                )
                .options(selectinload(NodeInstance.definition))
            )

            result = await self.db.execute(query)
            nodes = result.scalars().all()

            # 按日期分组统计
            daily_stats = defaultdict(
                lambda: {
                    'date': None,
                    'completed_tasks': 0,
                    'delayed_tasks': 0,
                    'avg_completion_time': 0,
                }
            )

            for node in nodes:
                if node.end_time:
                    date_key = node.end_time.date().isoformat()
                    stats = daily_stats[date_key]
                    stats['date'] = date_key

                    if node.status == NodeStatus.completed:
                        stats['completed_tasks'] += 1
                        if node.actual_duration:
                            stats['avg_completion_time'] = (
                                stats['avg_completion_time']
                                * (stats['completed_tasks'] - 1)
                                + node.actual_duration
                            ) / stats['completed_tasks']

                    if (
                        node.actual_duration
                        and node.definition.expected_duration
                        and node.actual_duration
                        > node.definition.expected_duration
                    ):
                        stats['delayed_tasks'] += 1

            # 转换为列表并排序
            trends = [
                {
                    'date': stats['date'],
                    'completed_tasks': stats['completed_tasks'],
                    'delayed_tasks': stats['delayed_tasks'],
                    'avg_completion_time': round(
                        stats['avg_completion_time'], 2
                    ),
                }
                for stats in daily_stats.values()
            ]

            return {
                'project_id': project_id,
                'trends': sorted(trends, key=lambda x: x['date']),
                'total_days': days,
            }

        except Exception as e:
            print(f'获取项目任务趋势数据时发生错误: {str(e)}')
            raise e


# 创建服务实例
project_report_service = ProjectReportService
