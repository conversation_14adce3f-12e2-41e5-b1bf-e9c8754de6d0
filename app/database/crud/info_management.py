from database.crud.base import CRUDBase

# from database.models import Status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
from sqlalchemy import text


class CRUDStatus(CRUDBase):
    def __init__(self, model):
        super().__init__(model)
        self.erp_framework_list = None
        self.erp_info = None

    async def get_erp_info(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        查询ERP基础信息
        """
        sql = """
        SELECT
            ERP,
            `客户`,
            `型号`,
            `类别`,
            `数量`
        FROM
            erp_base_info
        WHERE
            bom_time > '2025-06-01'
        """

        result = await db.execute(text(sql))
        raw_data = result.mappings().all()
        erp_info = []
        for row in raw_data:
            try:
                machine_num = (
                    int(row['数量'].strip())
                    if '+' not in row['数量']
                    else [int(num.strip()) for num in row['数量'].split('+')]
                )
            except Exception as e:
                machine_num = 0
            erp_info.append(
                {
                    'erp': row['ERP'],
                    'customer': row['客户'],
                    'model': row['型号'],
                    'type': row['类别'],
                    'machine_num': machine_num,
                }
            )
        return erp_info

    async def get_erp_info_dict(self, db: AsyncSession):
        erp_info = await self.get_erp_info(db)
        return {row['erp']: row for row in erp_info}

    async def get_erp_framework_list(
        self,
        db: AsyncSession,
        create_time: str,
    ) -> List[Dict[str, Any]]:
        """
        总装部件梳理状态 0:未完成 1:待审核 2:已完成
        """
        sql = """
        SELECT
            erp,
            status
        FROM
            inner_erp_framework_list
        WHERE
            created_at >= :create_time
        """

        result = await db.execute(text(sql), {'create_time': create_time})
        rows = result.mappings().all()
        return [dict(row) for row in rows]

    async def get_user_list(
        self,
        db: AsyncSession,
    ) -> List[Dict[str, Any]]:
        """
        获取用户列表
        """
        sql = """
        SELECT
            realname,
            username
        FROM
            flask_user
        """

        result = await db.execute(text(sql))
        rows = result.mappings().all()
        return [dict(row) for row in rows]

    async def get_erp_part_numbers(
        self, db: AsyncSession, erp: str
    ) -> List[Dict[str, Any]]:
        """
        获取erp的部件列表
        """
        sql = """
        SELECT
            partNumber
        FROM
            inner_erp_framework_list
        WHERE
            erp = :erp and is_delete = 0
        """

        result = await db.execute(text(sql), {'erp': erp})
        rows = result.mappings().all()
        return [dict(row) for row in rows]

    async def get_mech_info_change_records(
        self, db: AsyncSession
    ) -> List[Dict[str, Any]]:
        """
        获取部件变更记录
        """
        sql = """
            SELECT DISTINCT erp,partNumber,submitCount,created_at,created_by FROM `inner_erp_mech_info` WHERE submitCount <> ''
        """
        result = await db.execute(text(sql))
        rows = result.mappings().all()
        return [dict(row) for row in rows]


info = CRUDStatus(None)
