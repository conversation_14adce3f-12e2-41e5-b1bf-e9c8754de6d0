from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func, distinct
from sqlalchemy.orm import selectinload

from database.models import InspectionTemplate, ProjectInspection
from database.crud.base import CRUDBase
from schemas.inspection import (
    InspectionTemplateCreate,
    InspectionTemplateUpdate,
    ProjectInspectionCreate,
    ProjectInspectionUpdate,
)


class CRUDInspectionTemplate(
    CRUDBase[
        InspectionTemplate, InspectionTemplateCreate, InspectionTemplateUpdate
    ]
):
    """点检表模板CRUD操作"""

    async def get_by_type(
        self,
        db: AsyncSession,
        *,
        template_type: str,
        skip: int = 0,
        limit: int = 100,
    ) -> List[InspectionTemplate]:
        """根据类型获取模板列表"""
        query = (
            select(self.model)
            .filter(
                self.model.type == template_type, self.model.is_active == True
            )
            .order_by(self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_types(self, db: AsyncSession) -> List[str]:
        """获取所有模板类型"""
        query = select(distinct(self.model.type)).filter(
            self.model.is_active == True
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_type_and_sequence(
        self, db: AsyncSession, *, template_type: str, sequence_number: int
    ) -> Optional[InspectionTemplate]:
        """根据类型和序号获取模板"""
        query = select(self.model).filter(
            self.model.type == template_type,
            self.model.sequence_number == sequence_number,
            self.model.is_active == True,
        )
        result = await db.execute(query)
        return result.scalars().first()

    async def get_active_templates(
        self, db: AsyncSession, skip: int = 0, limit: int = 1000
    ) -> List[InspectionTemplate]:
        """获取所有启用的模板"""
        query = (
            select(self.model)
            .filter(self.model.is_active == True)
            .order_by(self.model.type, self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def count_by_type(self, db: AsyncSession, template_type: str) -> int:
        """统计指定类型的模板数量"""
        query = select(func.count(self.model.id)).filter(
            self.model.type == template_type, self.model.is_active == True
        )
        result = await db.execute(query)
        return result.scalar()

    async def deactivate(
        self, db: AsyncSession, *, id: int
    ) -> Optional[InspectionTemplate]:
        """停用模板（软删除）"""
        db_obj = await self.get(db, id=id)
        if db_obj:
            db_obj.is_active = False
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def batch_create(
        self, db: AsyncSession, *, obj_in_list: List[InspectionTemplateCreate]
    ) -> List[InspectionTemplate]:
        """批量创建模板"""
        db_objs = []
        for obj_in in obj_in_list:
            db_obj = self.model(**obj_in.dict())
            db.add(db_obj)
            db_objs.append(db_obj)

        await db.commit()
        for db_obj in db_objs:
            await db.refresh(db_obj)
        return db_objs


class CRUDProjectInspection(
    CRUDBase[
        ProjectInspection, ProjectInspectionCreate, ProjectInspectionUpdate
    ]
):
    """项目点检表CRUD操作"""

    async def get_by_project(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        skip: int = 0,
        limit: int = 1000,
    ) -> List[ProjectInspection]:
        """获取项目的所有点检表"""
        query = (
            select(self.model)
            .options(selectinload(self.model.template))
            .filter(self.model.project_id == project_id)
            .order_by(self.model.type, self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_project_and_type(
        self, db: AsyncSession, *, project_id: str, inspection_type: str
    ) -> List[ProjectInspection]:
        """获取项目指定类型的点检表"""
        query = (
            select(self.model)
            .options(selectinload(self.model.template))
            .filter(
                self.model.project_id == project_id,
                self.model.type == inspection_type,
            )
            .order_by(self.model.sequence_number)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_project_and_template(
        self, db: AsyncSession, *, project_id: str, template_id: int
    ) -> Optional[ProjectInspection]:
        """根据项目ID和模板ID获取点检表"""
        query = select(self.model).filter(
            self.model.project_id == project_id,
            self.model.template_id == template_id,
        )
        result = await db.execute(query)
        return result.scalars().first()

    async def count_by_project(self, db: AsyncSession, project_id: str) -> int:
        """统计项目的点检表总数"""
        query = select(func.count(self.model.id)).filter(
            self.model.project_id == project_id
        )
        result = await db.execute(query)
        return result.scalar()

    async def count_completed_by_project(
        self, db: AsyncSession, project_id: str
    ) -> int:
        """统计项目已完成的点检表数量"""
        query = select(func.count(self.model.id)).filter(
            self.model.project_id == project_id,
            self.model.is_completed == True,
        )
        result = await db.execute(query)
        return result.scalar()

    async def get_project_summary(
        self, db: AsyncSession, project_id: str
    ) -> Dict[str, Any]:
        """获取项目点检表汇总信息"""
        total_count = await self.count_by_project(db, project_id)
        completed_count = await self.count_completed_by_project(db, project_id)

        # 获取所有类型
        types_query = select(distinct(self.model.type)).filter(
            self.model.project_id == project_id
        )
        types_result = await db.execute(types_query)
        types = types_result.scalars().all()

        completion_rate = (
            (completed_count / total_count * 100) if total_count > 0 else 0
        )

        return {
            'project_id': project_id,
            'total_count': total_count,
            'completed_count': completed_count,
            'completion_rate': round(completion_rate, 2),
            'types': types,
        }

    async def batch_create_from_templates(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        templates: List[InspectionTemplate],
        created_by: Optional[str] = None,
    ) -> List[ProjectInspection]:
        """基于模板批量创建项目点检表"""
        db_objs = []

        try:
            for template in templates:
                try:
                    # 检查是否已存在
                    existing = await self.get_by_project_and_template(
                        db, project_id=project_id, template_id=template.id
                    )
                    if existing:
                        continue

                    obj_data = {
                        'project_id': project_id,
                        'template_id': template.id,
                        'type': template.type,
                        'sequence_number': template.sequence_number,
                        'inspection_item': template.inspection_item,
                        'area_function': template.area_function,
                        'category': template.category,
                        'specific_plan': template.specific_plan,
                        'inspection_method': template.inspection_method,
                        'created_by': created_by,
                    }
                    db_obj = self.model(**obj_data)
                    db.add(db_obj)
                    db_objs.append(db_obj)

                except Exception as e:
                    print(
                        f'Warning: Failed to process template {template.id}: {str(e)}'
                    )
                    continue

            if db_objs:
                try:
                    await db.commit()
                    # 不使用refresh，直接返回创建的对象
                    # refresh可能会导致greenlet错误
                except Exception as e:
                    await db.rollback()
                    raise e

        except Exception as e:
            await db.rollback()
            raise e

        return db_objs

    async def simple_batch_create_from_templates(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        templates: List[InspectionTemplate],
        created_by: Optional[str] = None,
    ) -> int:
        """简化版批量创建，避免greenlet问题，返回创建数量"""
        created_count = 0

        # 预先提取所有模板数据，避免在循环中访问ORM属性
        template_data_list = []
        for i, template in enumerate(templates):
            try:
                template_data = {
                    'id': template.id,
                    'type': template.type,
                    'sequence_number': template.sequence_number,
                    'inspection_item': template.inspection_item,
                    'area_function': template.area_function,
                    'category': template.category,
                    'specific_plan': template.specific_plan,
                    'inspection_method': template.inspection_method,
                    'index': i,
                }
                template_data_list.append(template_data)
            except Exception as e:
                print(
                    f'Warning: Failed to extract data from template {i}: {str(e)}'
                )
                continue

        # 现在使用提取的数据进行创建
        for template_data in template_data_list:
            try:
                template_id = template_data['id']

                # 检查是否已存在
                existing = await self.get_by_project_and_template(
                    db, project_id=project_id, template_id=template_id
                )
                if existing:
                    continue

                obj_data = {
                    'project_id': project_id,
                    'template_id': template_id,
                    'type': template_data['type'],
                    'sequence_number': template_data['sequence_number'],
                    'inspection_item': template_data['inspection_item'],
                    'area_function': template_data['area_function'],
                    'category': template_data['category'],
                    'specific_plan': template_data['specific_plan'],
                    'inspection_method': template_data['inspection_method'],
                    'created_by': created_by,
                }
                db_obj = self.model(**obj_data)
                db.add(db_obj)
                created_count += 1

                # 每创建一个就提交一次，避免大批量操作
                await db.commit()

            except Exception as e:
                await db.rollback()
                template_info = (
                    f"template_{template_data.get('index', 'unknown')}"
                )
                print(
                    f'Warning: Failed to create inspection for {template_info}: {str(e)}'
                )
                continue

        return created_count

    async def raw_sql_batch_create_from_templates(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        templates: List[InspectionTemplate],
        created_by: Optional[str] = None,
    ) -> int:
        """使用原始SQL的批量创建，完全避免ORM问题"""
        from sqlalchemy import text

        created_count = 0

        for i, template in enumerate(templates):
            try:
                # 使用原始SQL检查是否已存在
                check_sql = text(
                    """
                    SELECT COUNT(*) as count
                    FROM project_inspections
                    WHERE project_id = :project_id AND template_id = :template_id
                """
                )

                result = await db.execute(
                    check_sql,
                    {'project_id': project_id, 'template_id': template.id},
                )
                count = result.scalar()

                if count > 0:
                    continue

                # 使用原始SQL插入，包含is_completed字段
                insert_sql = text(
                    """
                    INSERT INTO project_inspections (
                        project_id, template_id, type, sequence_number,
                        inspection_item, area_function, category,
                        specific_plan, inspection_method, created_by,
                        is_completed, created_at, updated_at
                    ) VALUES (
                        :project_id, :template_id, :type, :sequence_number,
                        :inspection_item, :area_function, :category,
                        :specific_plan, :inspection_method, :created_by,
                        0, NOW(), NOW()
                    )
                """
                )

                await db.execute(
                    insert_sql,
                    {
                        'project_id': project_id,
                        'template_id': template.id,
                        'type': template.type,
                        'sequence_number': template.sequence_number,
                        'inspection_item': template.inspection_item,
                        'area_function': template.area_function,
                        'category': template.category,
                        'specific_plan': template.specific_plan,
                        'inspection_method': template.inspection_method,
                        'created_by': created_by,
                    },
                )

                await db.commit()
                created_count += 1

            except Exception as e:
                await db.rollback()
                print(
                    f'Warning: Failed to create inspection for template {i}: {str(e)}'
                )
                continue

        return created_count

    async def pure_sql_batch_create_from_template_ids(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        template_ids: List[int],
        created_by: Optional[str] = None,
    ) -> int:
        """使用纯SQL的批量创建，只需要template_ids，完全避免ORM问题"""
        from sqlalchemy import text

        created_count = 0

        for template_id in template_ids:
            try:
                # 使用原始SQL检查是否已存在
                check_sql = text(
                    """
                    SELECT COUNT(*) as count
                    FROM project_inspections
                    WHERE project_id = :project_id AND template_id = :template_id
                """
                )

                result = await db.execute(
                    check_sql,
                    {'project_id': project_id, 'template_id': template_id},
                )
                count = result.scalar()

                if count > 0:
                    continue

                # 使用原始SQL从模板获取数据并插入
                insert_sql = text(
                    """
                    INSERT INTO project_inspections (
                        project_id, template_id, type, sequence_number,
                        inspection_item, area_function, category,
                        specific_plan, inspection_method, created_by,
                        is_completed, created_at, updated_at
                    )
                    SELECT
                        :project_id, t.id, t.type, t.sequence_number,
                        t.inspection_item, t.area_function, t.category,
                        t.specific_plan, t.inspection_method, :created_by,
                        0, NOW(), NOW()
                    FROM inspection_templates t
                    WHERE t.id = :template_id AND t.is_active = 1
                """
                )

                result = await db.execute(
                    insert_sql,
                    {
                        'project_id': project_id,
                        'template_id': template_id,
                        'created_by': created_by,
                    },
                )

                if result.rowcount > 0:
                    await db.commit()
                    created_count += 1
                else:
                    await db.rollback()

            except Exception as e:
                await db.rollback()
                print(
                    f'Warning: Failed to create inspection for template_id {template_id}: {str(e)}'
                )
                continue

        return created_count

    async def delete_by_project(
        self, db: AsyncSession, *, project_id: str
    ) -> int:
        """删除项目的所有点检表"""
        query = delete(self.model).filter(self.model.project_id == project_id)
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    async def delete_by_project_and_type(
        self, db: AsyncSession, *, project_id: str, inspection_type: str
    ) -> int:
        """删除项目指定类型的点检表"""
        query = delete(self.model).filter(
            self.model.project_id == project_id,
            self.model.type == inspection_type,
        )
        result = await db.execute(query)
        await db.commit()
        return result.rowcount


# 创建CRUD实例
inspection_template = CRUDInspectionTemplate(InspectionTemplate)
project_inspection = CRUDProjectInspection(ProjectInspection)
