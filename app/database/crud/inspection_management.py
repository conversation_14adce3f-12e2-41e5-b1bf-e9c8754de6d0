from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func, distinct
from sqlalchemy.orm import selectinload

from database.models import InspectionTemplate, ProjectInspection
from database.crud.base import CRUDBase
from schemas.inspection import (
    InspectionTemplateCreate,
    InspectionTemplateUpdate,
    ProjectInspectionCreate,
    ProjectInspectionUpdate,
)


class CRUDInspectionTemplate(
    CRUDBase[
        InspectionTemplate, InspectionTemplateCreate, InspectionTemplateUpdate
    ]
):
    """点检表模板CRUD操作"""

    async def get_by_type(
        self,
        db: AsyncSession,
        *,
        template_type: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[InspectionTemplate]:
        """根据类型获取模板列表"""
        query = (
            select(self.model)
            .filter(
                self.model.type == template_type, self.model.is_active == True
            )
            .order_by(self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_types(self, db: AsyncSession) -> List[str]:
        """获取所有模板类型"""
        query = select(distinct(self.model.type)).filter(
            self.model.is_active == True
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_type_and_sequence(
        self, db: AsyncSession, *, template_type: str, sequence_number: int
    ) -> Optional[InspectionTemplate]:
        """根据类型和序号获取模板"""
        query = select(self.model).filter(
            self.model.type == template_type,
            self.model.sequence_number == sequence_number,
            self.model.is_active == True,
        )
        result = await db.execute(query)
        return result.scalars().first()

    async def get_active_templates(
        self, db: AsyncSession, skip: int = 0, limit: int = 1000
    ) -> List[InspectionTemplate]:
        """获取所有启用的模板"""
        query = (
            select(self.model)
            .filter(self.model.is_active == True)
            .order_by(self.model.type, self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def count_by_type(self, db: AsyncSession, template_type: str) -> int:
        """统计指定类型的模板数量"""
        query = select(func.count(self.model.id)).filter(
            self.model.type == template_type, self.model.is_active == True
        )
        result = await db.execute(query)
        return result.scalar()

    async def deactivate(
        self, db: AsyncSession, *, id: int
    ) -> Optional[InspectionTemplate]:
        """停用模板（软删除）"""
        db_obj = await self.get(db, id=id)
        if db_obj:
            db_obj.is_active = False
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def batch_create(
        self, db: AsyncSession, *, obj_in_list: List[InspectionTemplateCreate]
    ) -> List[InspectionTemplate]:
        """批量创建模板"""
        db_objs = []
        for obj_in in obj_in_list:
            db_obj = self.model(**obj_in.dict())
            db.add(db_obj)
            db_objs.append(db_obj)

        await db.commit()
        for db_obj in db_objs:
            await db.refresh(db_obj)
        return db_objs


class CRUDProjectInspection(
    CRUDBase[
        ProjectInspection, ProjectInspectionCreate, ProjectInspectionUpdate
    ]
):
    """项目点检表CRUD操作"""

    async def get_by_project(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        skip: int = 0,
        limit: int = 1000
    ) -> List[ProjectInspection]:
        """获取项目的所有点检表"""
        query = (
            select(self.model)
            .options(selectinload(self.model.template))
            .filter(self.model.project_id == project_id)
            .order_by(self.model.type, self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_project_and_type(
        self, db: AsyncSession, *, project_id: str, inspection_type: str
    ) -> List[ProjectInspection]:
        """获取项目指定类型的点检表"""
        query = (
            select(self.model)
            .options(selectinload(self.model.template))
            .filter(
                self.model.project_id == project_id,
                self.model.type == inspection_type,
            )
            .order_by(self.model.sequence_number)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_project_and_template(
        self, db: AsyncSession, *, project_id: str, template_id: int
    ) -> Optional[ProjectInspection]:
        """根据项目ID和模板ID获取点检表"""
        query = select(self.model).filter(
            self.model.project_id == project_id,
            self.model.template_id == template_id,
        )
        result = await db.execute(query)
        return result.scalars().first()

    async def count_by_project(self, db: AsyncSession, project_id: str) -> int:
        """统计项目的点检表总数"""
        query = select(func.count(self.model.id)).filter(
            self.model.project_id == project_id
        )
        result = await db.execute(query)
        return result.scalar()

    async def count_completed_by_project(
        self, db: AsyncSession, project_id: str
    ) -> int:
        """统计项目已完成的点检表数量"""
        query = select(func.count(self.model.id)).filter(
            self.model.project_id == project_id,
            self.model.is_completed == True,
        )
        result = await db.execute(query)
        return result.scalar()

    async def get_project_summary(
        self, db: AsyncSession, project_id: str
    ) -> Dict[str, Any]:
        """获取项目点检表汇总信息"""
        total_count = await self.count_by_project(db, project_id)
        completed_count = await self.count_completed_by_project(db, project_id)

        # 获取所有类型
        types_query = select(distinct(self.model.type)).filter(
            self.model.project_id == project_id
        )
        types_result = await db.execute(types_query)
        types = types_result.scalars().all()

        completion_rate = (
            (completed_count / total_count * 100) if total_count > 0 else 0
        )

        return {
            'project_id': project_id,
            'total_count': total_count,
            'completed_count': completed_count,
            'completion_rate': round(completion_rate, 2),
            'types': types,
        }

    async def batch_create_from_templates(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        templates: List[InspectionTemplate],
        created_by: Optional[str] = None
    ) -> List[ProjectInspection]:
        """基于模板批量创建项目点检表"""
        db_objs = []
        for template in templates:
            # 检查是否已存在
            existing = await self.get_by_project_and_template(
                db, project_id=project_id, template_id=template.id
            )
            if existing:
                continue

            obj_data = {
                'project_id': project_id,
                'template_id': template.id,
                'type': template.type,
                'sequence_number': template.sequence_number,
                'inspection_item': template.inspection_item,
                'area_function': template.area_function,
                'category': template.category,
                'specific_plan': template.specific_plan,
                'inspection_method': template.inspection_method,
                'created_by': created_by,
            }
            db_obj = self.model(**obj_data)
            db.add(db_obj)
            db_objs.append(db_obj)

        if db_objs:
            await db.commit()
            for db_obj in db_objs:
                await db.refresh(db_obj)
        return db_objs

    async def delete_by_project(
        self, db: AsyncSession, *, project_id: str
    ) -> int:
        """删除项目的所有点检表"""
        query = delete(self.model).filter(self.model.project_id == project_id)
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    async def delete_by_project_and_type(
        self, db: AsyncSession, *, project_id: str, inspection_type: str
    ) -> int:
        """删除项目指定类型的点检表"""
        query = delete(self.model).filter(
            self.model.project_id == project_id,
            self.model.type == inspection_type,
        )
        result = await db.execute(query)
        await db.commit()
        return result.rowcount


# 创建CRUD实例
inspection_template = CRUDInspectionTemplate(InspectionTemplate)
project_inspection = CRUDProjectInspection(ProjectInspection)
