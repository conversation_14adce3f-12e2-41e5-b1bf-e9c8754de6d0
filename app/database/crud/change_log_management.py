# app/crud/change_log_management.py
from sqlalchemy.future import select
from database.crud.base import CRUDBase
from database.models import ChangeLog
import schemas
from sqlalchemy.ext.asyncio import AsyncSession


class CRUDChangeLog(CRUDBase):
    async def get_workflow_changes(
        self, db: AsyncSession, workflow_instance_id: int
    ):
        result = await db.execute(
            select(self.model)
            .filter(self.model.workflow_instance_id == workflow_instance_id)
            .order_by(self.model.created_at.desc())
        )
        return result.scalars().all()

    async def create_change_log(
        self, db: AsyncSession, change_log: schemas.ChangeLogCreate
    ):
        db_change_log = ChangeLog(**change_log.dict())
        db.add(db_change_log)
        await db.commit()
        await db.refresh(db_change_log)
        return db_change_log


change_log = CRUDChangeLog(ChangeLog)
