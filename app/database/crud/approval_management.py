import asyncio
from sqlalchemy import select, and_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from database.crud.base import CRUDBase
from database.models import (
    ApprovalRecord,
    NodeInstance,
    NodeStatus,
    EdgeCondition,
    WorkflowInstance,
    NodeDefinition,
)
import schemas
from .node_status_service import EnhancedNodeStatusService
from typing import Optional, List
from datetime import datetime


from schemas.base import ApprovalStatus, NodeType


class ApprovalManagementService:
    def __init__(self):
        self.node_status_service = None
        self._lock = asyncio.Lock()  # 添加异步锁

    async def get_node_with_details(
        self, db, node_instance_id: int
    ) -> Optional[NodeInstance]:
        """获取节点及其审批配置信息"""
        result = await db.execute(
            select(NodeInstance)
            .options(selectinload(NodeInstance.definition))
            .filter(NodeInstance.id == node_instance_id)
        )
        return result.scalar_one_or_none()

    def init_node_status_service(self, db):
        if not self.node_status_service:
            self.node_status_service = EnhancedNodeStatusService(db)
            self.node_status_service._lock = self._lock

    async def handle_approval(
        self, db, node_instance_id: int, approval: schemas.ApprovalRecordCreate
    ) -> dict:
        """
        处理审批请求 - 优化版本，减少锁等待时间

        Args:
            node_instance_id: 节点实例ID
            approval: 审批记录创建数据

        Returns:
            dict: 包含处理结果的字典
        """
        # 使用节点协调服务避免与轮询服务冲突
        from services.node_coordination_service import NodeLockContext

        try:
            async with NodeLockContext(
                node_instance_id, 'approval', timeout_seconds=30
            ):
                async with self._lock:
                    if not self.node_status_service:
                        self.node_status_service = EnhancedNodeStatusService(
                            db
                        )

                    try:
                        # 使用单一事务处理整个审批流程，避免多次提交导致的锁等待
                        # # 1. 先检查节点是否已经被审批过（防止重复审批）
                        # existing_approval = await db.execute(
                        #     select(ApprovalRecord).filter(
                        #         ApprovalRecord.node_instance_id == node_instance_id,
                        #         ApprovalRecord.approval_status == approval.approval_status
                        #     ).limit(1)
                        # )
                        # if existing_approval.scalar_one_or_none():
                        #     raise ValueError(f'Node {node_instance_id} has already been {approval.approval_status}')

                        # 2. 使用 SELECT FOR UPDATE NOWAIT 避免长时间等待

                        # 由于已经通过节点协调服务获取了锁，这里不需要使用 NOWAIT
                        # 使用普通的 FOR UPDATE 确保数据一致性
                        node_stmt = (
                            select(NodeInstance)
                            .options(selectinload(NodeInstance.definition))
                            .filter(NodeInstance.id == node_instance_id)
                            .with_for_update()  # 移除 nowait=True，避免与节点协调锁冲突
                        )

                        result = await db.execute(node_stmt)
                        node = result.scalar_one_or_none()

                        if not node:
                            raise ValueError(
                                f'Node instance {node_instance_id} not found'
                            )

                        if not node.definition.need_approval:
                            raise ValueError(
                                f'Node {node_instance_id} does not require approval'
                            )

                        # 3. 检查当前节点状态
                        if node.status not in [NodeStatus.active]:
                            raise ValueError(
                                f'Cannot approve/reject node in {node.status.value} status'
                            )

                        if (
                            node.definition.type == NodeType.process
                            and approval.approval_status
                            != ApprovalStatus.approved.value
                        ):
                            raise ValueError(
                                'Process node can only be approved'
                            )

                        # 4. 在同一事务中创建审批记录和更新节点状态
                        approval_record = ApprovalRecord(
                            node_instance_id=node_instance_id,
                            approved_by=approval.approved_by,
                            approval_status=approval.approval_status,
                            comments=approval.comments,
                            created_at=datetime.now(),
                        )
                        db.add(approval_record)

                        # 5. 计算更新数据
                        update_data = {
                            'actual_duration': (
                                datetime.now() - node.start_time
                            ).total_seconds()
                            / 3600
                            if node.start_time
                            else None
                        }

                        # 6. 根据审批状态处理不同逻辑
                        new_status = None
                        decision_result = None
                        is_return = False

                        if (
                            approval.approval_status
                            == ApprovalStatus.approved.value
                        ):
                            # 审批通过：节点完成，激活下游
                            new_status = NodeStatus.completed
                            # 决策节点审批通过后，像普通节点一样正常往下走，不需要特殊的decision_result
                            decision_result = None
                        else:  # rejected = 退回
                            # 退回逻辑：当前节点变为pending，激活上游节点
                            new_status = NodeStatus.pending
                            is_return = True

                        # 7. 更新节点状态
                        update_values = {
                            'status': new_status,
                        }

                        # 退回时不设置结束时间，其他情况设置结束时间
                        if not is_return:
                            update_values['end_time'] = datetime.now()

                        if update_data.get('actual_duration'):
                            update_values['actual_duration'] = update_data[
                                'actual_duration'
                            ]

                        await db.execute(
                            update(NodeInstance)
                            .where(NodeInstance.id == node_instance_id)
                            .values(**update_values)
                        )

                        # 8. 一次性提交所有更改
                        await db.commit()

                        # 9. 获取审批记录ID
                        await db.refresh(approval_record)
                        approval_record_id = approval_record.id

                        # 10. 异步处理后续流程（避免在事务中处理复杂逻辑）
                        if is_return:
                            # 退回逻辑：激活上游节点
                            asyncio.create_task(
                                self._handle_return_workflow(node_instance_id)
                            )
                        elif new_status == NodeStatus.completed:
                            # 正常完成：处理下游流程
                            asyncio.create_task(
                                self._handle_post_approval_workflow(
                                    node_instance_id, decision_result
                                )
                            )

                        return {
                            'id': approval_record_id,
                            'node_status': new_status.value,
                            'node_instance_id': node_instance_id,
                            'approved_by': approval.approved_by,
                            'approval_status': approval.approval_status,
                            'comments': approval.comments,
                            'created_at': datetime.now(),
                        }

                    except Exception as e:
                        await db.rollback()
                        raise e

        except RuntimeError as e:
            if 'Failed to acquire lock' in str(e):
                raise ValueError(
                    f'Node {node_instance_id} is currently being processed by another service. Please try again.'
                )
            raise e

    async def _handle_post_approval_workflow(
        self,
        node_instance_id: int,
        decision_result: Optional[EdgeCondition] = None,
    ):
        """
        异步处理审批后的工作流程，避免在主事务中处理复杂逻辑
        """
        try:
            from database.base import AsyncSessionLocal

            async with AsyncSessionLocal() as new_db:
                # 使用新的数据库会话处理后续流程
                node_status_service = EnhancedNodeStatusService(new_db)

                # 处理子流程状态更新
                node = await node_status_service.get_node_with_definition(
                    node_instance_id
                )
                if node:
                    await node_status_service.handle_subprocess_status_from_info(
                        {
                            'id': node.id,
                            'workflow_instance_id': node.workflow_instance_id,
                            'subprocess_instance_id': node.subprocess_instance_id,
                            'status': node.status,
                        }
                    )

                    # 更新工作流状态
                    await node_status_service.update_workflow_status(
                        node.workflow_instance_id
                    )

                    # 激活下游节点 - 对所有完成的节点都要检查下游激活
                    try:
                        if decision_result:
                            # 决策节点：根据决策结果获取特定路径的下游节点
                            downstream_nodes = await node_status_service._get_downstream_nodes(
                                node_instance_id, decision_result
                            )
                        else:
                            # 普通节点：获取所有下游节点
                            downstream_nodes = await node_status_service._get_downstream_nodes(
                                node_instance_id
                            )

                        if downstream_nodes:
                            await node_status_service._process_node_activation_queue(
                                downstream_nodes
                            )

                    except Exception as e:
                        import logging

                        logging.error(
                            f'Error activating downstream nodes for {node_instance_id}: {str(e)}'
                        )
                        # 下游激活失败不应该影响主流程

                    await new_db.commit()

        except Exception as e:
            # 记录错误但不影响主流程
            import logging

            logging.error(
                f'Post-approval workflow processing failed for node {node_instance_id}: {str(e)}'
            )
            # 可以考虑将失败的任务加入重试队列

    async def _handle_return_workflow(self, node_instance_id: int):
        """
        处理退回工作流程：激活上游节点
        """
        try:
            from database.base import AsyncSessionLocal

            async with AsyncSessionLocal() as new_db:
                # 使用新的数据库会话处理退回流程
                node_status_service = EnhancedNodeStatusService(new_db)

                # 获取当前节点信息
                node = await node_status_service.get_node_with_definition(
                    node_instance_id
                )
                if not node:
                    return

                # 获取上游节点
                upstream_nodes = await self._get_upstream_nodes(
                    new_db, node_instance_id
                )

                if upstream_nodes:
                    # 激活所有上游节点
                    for upstream_node in upstream_nodes:
                        # 将上游节点状态设为 active
                        await new_db.execute(
                            update(NodeInstance)
                            .where(NodeInstance.id == upstream_node.id)
                            .values(
                                status=NodeStatus.active,
                                end_time=None,  # 清除结束时间
                            )
                        )

                    await new_db.commit()

                    # 记录退回日志
                    import logging

                    logging.info(
                        f'Node {node_instance_id} returned, activated upstream nodes: {[n.id for n in upstream_nodes]}'
                    )

        except Exception as e:
            # 记录错误但不影响主流程
            import logging

            logging.error(
                f'Error in return workflow for node {node_instance_id}: {str(e)}'
            )

    async def _get_upstream_nodes(
        self, db: AsyncSession, node_instance_id: int
    ) -> List[NodeInstance]:
        """
        获取指定节点的上游节点
        """
        try:
            # 通过边实例查找上游节点
            from database.models import EdgeInstance

            result = await db.execute(
                select(NodeInstance)
                .join(
                    EdgeInstance,
                    EdgeInstance.from_node_instance_id == NodeInstance.id,
                )
                .filter(EdgeInstance.to_node_instance_id == node_instance_id)
                .options(selectinload(NodeInstance.definition))
            )

            upstream_nodes = result.scalars().all()

            # 过滤掉开始节点，只返回可以重新激活的节点
            valid_upstream_nodes = []
            for node in upstream_nodes:
                if node.definition.type != NodeType.start:
                    valid_upstream_nodes.append(node)

            return valid_upstream_nodes

        except Exception as e:
            import logging

            logging.error(
                f'Error getting upstream nodes for {node_instance_id}: {str(e)}'
            )
            return []


class CRUDApprovalRecord(CRUDBase):
    def __init__(self, model):
        super().__init__(model)
        self.service = ApprovalManagementService()

    async def create_approval_record(
        self,
        db: AsyncSession,
        approval: schemas.ApprovalRecordCreate,
        node_instance_id: int,
    ) -> dict:
        """创建审批记录并处理节点状态"""
        # service = ApprovalManagementService(db)
        return await self.service.handle_approval(
            db, node_instance_id, approval
        )

    async def get_node_approvals(
        self, db: AsyncSession, node_instance_id: int
    ) -> List[ApprovalRecord]:
        """获取节点的所有审批记录"""
        result = await db.execute(
            select(ApprovalRecord)
            .filter(ApprovalRecord.node_instance_id == node_instance_id)
            .order_by(ApprovalRecord.created_at.desc())
        )
        return result.scalars().all()

    async def get_pending_approvals(
        self, db: AsyncSession, approver_id: str
    ) -> List[dict]:
        """获取待审批任务列表"""
        base_query = (
            select(NodeInstance)
            .join(NodeInstance.definition)  # 显式join NodeDefinition
            .join(NodeInstance.workflow)  # 显式join WorkflowInstance
            .filter(
                and_(
                    NodeInstance.assigned_to == approver_id,
                    NodeInstance.status == NodeStatus.active,
                    NodeDefinition.need_approval == True,
                )
            )
            .options(
                selectinload(NodeInstance.definition),
                selectinload(NodeInstance.workflow).selectinload(
                    WorkflowInstance.definition
                ),
            )
        )

        # 执行查询
        result = await db.execute(base_query)
        nodes = result.unique().scalars().all()

        # 获取每个节点的最新审批记录
        pending_tasks = []
        for node in nodes:
            latest_approval = await db.execute(
                select(ApprovalRecord)
                .filter(ApprovalRecord.node_instance_id == node.id)
                .order_by(ApprovalRecord.created_at.desc())
                .limit(1)
            )
            latest_approval = latest_approval.scalar_one_or_none()

            # 如果没有审批记录或最新记录是拒绝状态
            if (
                not latest_approval
                or latest_approval.approval_status == ApprovalStatus.rejected
            ):
                pending_tasks.append(
                    {
                        'node_id': node.id,
                        'node_name': node.definition.name,
                        'workflow_id': node.workflow_instance_id,
                        'workflow_name': node.workflow.definition.name,
                        'project_id': node.workflow.project_id,
                        'node_status': node.status.value,
                        'start_time': node.start_time,
                        'previous_approval': latest_approval.approval_status
                        if latest_approval
                        else None,
                    }
                )

        return pending_tasks

    async def get_node_with_details(
        self, db: AsyncSession, node_id: int
    ) -> Optional[NodeInstance]:
        """获取节点实例及其所有相关详情"""
        result = await db.execute(
            select(NodeInstance)
            .options(
                selectinload(NodeInstance.definition),
                selectinload(NodeInstance.workflow).selectinload(
                    WorkflowInstance.definition
                ),
            )
            .filter(NodeInstance.id == node_id)
        )
        return result.scalar_one_or_none()


approval_record = CRUDApprovalRecord(ApprovalRecord)
