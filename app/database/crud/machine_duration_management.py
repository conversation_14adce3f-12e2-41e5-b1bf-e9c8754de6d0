from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
from database.crud.base import CRUDBase
from database.models import MachineTypeDuration, NodeDefinition
from sqlalchemy.ext.asyncio import AsyncSession
import schemas
from schemas.base import DeviceType
from typing import List, Optional


class CRUDMachineTypeDuration(CRUDBase):
    async def get_by_node_and_type(
        self,
        db: AsyncSession,
        node_definition_id: int,
        machine_type: str,
        device_type: DeviceType,
    ) -> Optional[MachineTypeDuration]:
        """获取特定节点和机型的预期工时配置"""
        result = await db.execute(
            select(self.model).filter(
                self.model.node_definition_id == node_definition_id,
                self.model.machine_type == machine_type,
                self.model.device_type == device_type,
            )
        )
        return result.scalar_one_or_none()

    async def get_by_node(
        self, db: AsyncSession, node_definition_id: int
    ) -> List[MachineTypeDuration]:
        """获取某个节点的所有机型工时配置"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.node_definition_id == node_definition_id)
            .order_by(self.model.machine_type, self.model.device_type)
        )
        return result.scalars().all()

    async def create_duration(
        self, db: AsyncSession, duration: schemas.MachineTypeDurationCreate
    ) -> MachineTypeDuration:
        """创建新的机型工时配置"""
        db_duration = MachineTypeDuration(**duration.dict())
        db.add(db_duration)
        await db.commit()
        await db.refresh(db_duration)
        return db_duration

    async def update_duration(
        self,
        db: AsyncSession,
        duration_id: int,
        duration: schemas.MachineTypeDurationUpdate,
        user_id: str,
    ) -> Optional[MachineTypeDuration]:
        """更新机型工时配置"""
        db_duration = await self.get(db, duration_id)
        if not db_duration:
            return None

        update_data = duration.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_duration, field, value)
        setattr(db_duration, 'created_by', user_id)
        await db.commit()
        await db.refresh(db_duration)
        return db_duration

    async def bulk_create_durations(
        self,
        db: AsyncSession,
        durations: List[schemas.MachineTypeDurationCreate],
    ) -> List[MachineTypeDuration]:
        """批量创建机型工时配置"""
        db_durations = [
            MachineTypeDuration(**duration.dict()) for duration in durations
        ]
        db.add_all(db_durations)
        await db.commit()
        for duration in db_durations:
            await db.refresh(duration)
        return db_durations

    async def get_multiple_by_machine_type(
        self,
        db: AsyncSession,
        machine_type: str,
        device_type: DeviceType,
        user_id: str,
    ) -> List[MachineTypeDuration]:
        """
        获取某个机型的所有节点工时配置
        如果某节点没有配置，则自动创建一条记录
        """
        # 获取所有节点定义
        nodes_result = await db.execute(
            select(NodeDefinition).order_by(NodeDefinition.id)
        )
        nodes = nodes_result.scalars().all()

        # 获取现有的机型工时配置
        result = await db.execute(
            select(self.model)
            .options(joinedload(self.model.node_definition))
            .filter(
                self.model.machine_type == machine_type,
                self.model.device_type == device_type,
            )
            .order_by(self.model.node_definition_id)
        )
        existing_durations = result.scalars().all()

        # 将现有配置转换为字典，方便查找
        duration_dict = {d.node_definition_id: d for d in existing_durations}

        # 需要创建的新配置列表
        new_durations = []
        for node in nodes:
            if node.id not in duration_dict:
                new_duration = MachineTypeDuration(
                    node_definition_id=node.id,
                    machine_type=machine_type,
                    device_type=device_type,
                    expected_duration=None,
                    created_by=user_id,
                )
                new_duration.node_definition = node
                db.add(new_duration)
                new_durations.append(new_duration)

        # 如果有新创建的配置，提交到数据库
        if new_durations:
            await db.commit()
            for duration in new_durations:
                await db.refresh(duration)

        # 重新查询以获取所有配置（包括新创建的）
        final_result = await db.execute(
            select(self.model)
            .options(joinedload(self.model.node_definition))
            .filter(
                self.model.machine_type == machine_type,
                self.model.device_type == device_type,
            )
            .order_by(self.model.node_definition_id)
        )

        durations = final_result.scalars().all()

        # 为每个 duration 对象添加 node_name 属性
        for duration in durations:
            setattr(duration, 'node_name', duration.node_definition.name)

        return durations


machine_duration = CRUDMachineTypeDuration(MachineTypeDuration)
