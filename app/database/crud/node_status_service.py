from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, and_
from sqlalchemy.orm import selectinload
from datetime import datetime
from typing import List, Set, Dict, Optional, Tuple
from database.models import (
    NodeInstance,
    EdgeInstance,
    NodeStatus,
    WorkflowInstance,
    WorkflowStatus,
    InputLogicType,
    NodeType,
    EdgeCondition,
    NodeDefinition,
    EdgeDefinition,
)
import schemas
from database import logger
from services import task_checker
from database.crud import notification_management
import asyncio


class EnhancedNodeStatusService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.processed_nodes: Set[int] = set()
        self.reset_processed_nodes: Set[int] = set()
        self.pending_status_updates: Dict[int, NodeStatus] = {}
        self._lock = asyncio.Lock()  # 添加异步锁

    async def get_node_with_definition(self, node_instance_id: int):
        """获取节点及其定义信息"""
        result = await self.db.execute(
            select(NodeInstance)
            .options(
                selectinload(NodeInstance.definition),
                selectinload(NodeInstance.workflow),
            )
            .filter(NodeInstance.id == node_instance_id)
        )
        return result.scalar_one_or_none()

    async def get_edges_by_condition(
        self,
        node_instance_id: int,
        is_incoming: bool = True,
        condition: Optional[EdgeCondition] = None,
    ) -> List[EdgeInstance]:
        """获取指定条件的边"""
        query = select(EdgeInstance)
        if is_incoming:
            query = query.filter(
                EdgeInstance.to_node_instance_id == node_instance_id
            )
        else:
            query = query.filter(
                EdgeInstance.from_node_instance_id == node_instance_id
            )
        if condition is not None:
            query = query.filter(
                EdgeInstance.definition.has(
                    EdgeDefinition.condition == condition.value
                )
            )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_connected_nodes(
        self,
        node_instance_id: int,
        is_incoming: bool = True,
        condition: Optional[EdgeCondition] = None,
    ) -> List[NodeInstance]:
        """获取连接的节点"""
        edges = await self.get_edges_by_condition(
            node_instance_id, is_incoming, condition
        )
        node_ids = [
            edge.from_node_instance_id
            if is_incoming
            else edge.to_node_instance_id
            for edge in edges
        ]

        if not node_ids:
            return []

        result = await self.db.execute(
            select(NodeInstance).filter(NodeInstance.id.in_(node_ids))
        )
        return result.scalars().all()

    async def should_activate_node(self, node_instance: NodeInstance) -> bool:
        """检查节点是否应该被激活"""
        try:
            stmt = select(NodeDefinition).where(
                NodeDefinition.id == node_instance.node_definition_id
            )
            result = await self.db.execute(stmt)
            node_def = result.scalar_one()

            if not node_def:
                raise ValueError(
                    f'Node definition not found for node {node_instance.id}'
                )

            input_logic = (
                node_def.input_logic
                if node_def.input_logic
                else InputLogicType.AND
            )

            edge_stmt = (
                select(EdgeInstance)
                .join(
                    EdgeDefinition,
                    EdgeInstance.edge_definition_id == EdgeDefinition.id,
                )
                .options(selectinload(EdgeInstance.definition))
                .where(EdgeInstance.to_node_instance_id == node_instance.id)
            )

            edge_result = await self.db.execute(edge_stmt)
            edges = edge_result.scalars().all()

            if not edges:
                return True

            completed_count = 0
            for edge_instance in edges:
                from_node_stmt = select(NodeInstance).where(
                    NodeInstance.id == edge_instance.from_node_instance_id
                )
                from_node_result = await self.db.execute(from_node_stmt)
                from_node = from_node_result.scalar_one()

                if from_node.status == NodeStatus.completed:
                    completed_count += 1
                    if (
                        edge_instance.definition.condition
                        and edge_instance.definition.condition
                        != EdgeCondition.default
                    ):
                        return True

            if input_logic == InputLogicType.AND:
                return completed_count == len(edges)
            else:
                return completed_count > 0

        except Exception as e:
            logger.error(
                f'Error in should_activate_node for node {node_instance.id}: {str(e)}'
            )
            raise

    async def handle_decision_node(
        self, node_instance: NodeInstance, decision_result: EdgeCondition
    ) -> List[NodeInstance]:
        """处理决策节点的状态转换"""
        next_nodes = []
        outgoing_edges = await self.get_edges_by_condition(
            node_instance.id, is_incoming=False, condition=decision_result
        )

        for edge in outgoing_edges:
            to_node = await self.db.get(NodeInstance, edge.to_node_instance_id)
            next_nodes.append(to_node)

            if decision_result == EdgeCondition.no:
                await self.reset_branch_status(to_node.id)

        return next_nodes

    async def reset_branch_status(self, start_node_id: int):
        """重置分支上的所有节点状态"""
        if start_node_id in self.reset_processed_nodes:
            return

        self.reset_processed_nodes.add(start_node_id)
        try:
            node = await self.get_node_with_definition(start_node_id)
            if not node:
                return

            await self.db.execute(
                update(NodeInstance)
                .where(NodeInstance.id == start_node_id)
                .values(
                    status=NodeStatus.pending,
                    start_time=None,
                    end_time=None,
                    actual_duration=None,
                )
            )

            downstream_nodes = await self.get_connected_nodes(
                start_node_id, is_incoming=False
            )
            for down_node in downstream_nodes:
                await self.reset_branch_status(down_node.id)

        finally:
            self.reset_processed_nodes.remove(start_node_id)

    async def _update_single_node_status(
        self,
        node_id: int,
        status: NodeStatus,
        update_data: Optional[schemas.NodeInstanceUpdate] = None,
        decision_result: Optional[EdgeCondition] = None,
    ) -> Tuple[Dict, List[int]]:
        """更新单个节点状态，返回节点信息字典和需要激活的下游节点ID列表"""
        # 使用 SELECT FOR UPDATE 防止并发更新，并预加载所需的关联数据
        stmt = (
            select(NodeInstance)
            .options(
                selectinload(NodeInstance.definition),
                selectinload(NodeInstance.workflow),
            )
            .where(NodeInstance.id == node_id)
            .with_for_update()
        )
        result = await self.db.execute(stmt)
        node = result.scalar_one()

        if not node:
            raise ValueError(f'Node {node_id} not found')

        # 提取需要的节点信息到字典中，避免detached问题
        node_info = {
            'id': node.id,
            'status': node.status,
            'start_time': node.start_time,
            'subprocess_instance_id': node.subprocess_instance_id,
            'workflow_instance_id': node.workflow_instance_id,
            'node_definition_id': node.node_definition_id,
        }

        # 准备更新数据
        update_values = {'status': status}
        if update_data:
            update_values.update(update_data)

        # 更新时间戳
        if status == NodeStatus.completed:
            update_values['end_time'] = datetime.now()
            if node.start_time:
                duration = (
                    datetime.now() - node.start_time
                ).total_seconds() / 3600
                update_values['actual_duration'] = round(duration, 2)
        elif status == NodeStatus.active:
            update_values['start_time'] = datetime.now()
            update_values['end_time'] = None
            update_values['actual_duration'] = None

        # 更新节点状态
        await self.db.execute(
            update(NodeInstance)
            .where(NodeInstance.id == node_id)
            .values(**update_values)
        )

        # 更新node_info中的状态
        node_info['status'] = status

        # 获取需要激活的下游节点
        nodes_to_activate = []
        if status == NodeStatus.completed:
            # 检查是否是子流程的最后一个节点
            is_subprocess_last_node = await self._is_subprocess_last_node(node)

            if is_subprocess_last_node:
                # 如果是子流程的最后一个节点，获取主流程的下游节点
                logger.info(
                    f'节点 {node_id} 是子流程 {node.workflow_instance_id} 的最后一个节点'
                )
                parent_downstream_nodes = (
                    await self._get_parent_workflow_downstream_nodes(
                        node.workflow_instance_id
                    )
                )
                nodes_to_activate.extend(parent_downstream_nodes)
            else:
                # 普通情况：获取当前节点的下游节点
                def_stmt = select(NodeDefinition).where(
                    NodeDefinition.id == node.node_definition_id
                )
                def_result = await self.db.execute(def_stmt)
                node_def = def_result.scalar_one()

                if node_def.type == NodeType.decision:
                    # 决策节点处理
                    next_nodes = await self.get_connected_nodes(
                        node_id, is_incoming=False, condition=decision_result
                    )

                    for next_node in next_nodes:
                        if next_node.node_definition_id == 11:
                            continue
                        if await self.should_activate_node(next_node):
                            # 检查下游节点是否包含子流程
                            if next_node.subprocess_instance_id:
                                logger.info(
                                    f'决策节点下游 {next_node.id} 包含子流程 {next_node.subprocess_instance_id}'
                                )
                                nodes_to_activate.append(next_node.id)

                                # 获取子流程的首个节点
                                subprocess_first_nodes = (
                                    await self._get_subprocess_first_nodes(
                                        next_node.subprocess_instance_id
                                    )
                                )
                                nodes_to_activate.extend(
                                    subprocess_first_nodes
                                )
                            else:
                                # 普通节点
                                nodes_to_activate.append(next_node.id)
                else:
                    # 普通节点处理
                    next_nodes = await self.get_connected_nodes(
                        node_id, is_incoming=False
                    )

                    for next_node in next_nodes:
                        if next_node.node_definition_id == 11:
                            continue
                        if await self.should_activate_node(next_node):
                            # 检查下游节点是否包含子流程
                            if next_node.subprocess_instance_id:
                                logger.info(
                                    f'下游节点 {next_node.id} 包含子流程 {next_node.subprocess_instance_id}'
                                )
                                nodes_to_activate.append(next_node.id)

                                # 获取子流程的首个节点
                                subprocess_first_nodes = (
                                    await self._get_subprocess_first_nodes(
                                        next_node.subprocess_instance_id
                                    )
                                )
                                nodes_to_activate.extend(
                                    subprocess_first_nodes
                                )
                            else:
                                # 普通节点
                                nodes_to_activate.append(next_node.id)

        return node_info, nodes_to_activate

    async def _is_subprocess_last_node(self, node: NodeInstance) -> bool:
        """检查节点是否是子流程的最后一个节点"""
        try:
            # 获取当前节点的下游节点
            next_nodes = await self.get_connected_nodes(
                node.id, is_incoming=False
            )

            # 如果没有下游节点，可能是最后一个节点
            if not next_nodes:
                # 检查是否存在对应的主流程节点（通过subprocess_instance_id）
                parent_node_stmt = select(NodeInstance).filter(
                    NodeInstance.subprocess_instance_id
                    == node.workflow_instance_id
                )
                parent_result = await self.db.execute(parent_node_stmt)
                parent_nodes = parent_result.scalars().all()

                if parent_nodes:
                    logger.info(
                        f'节点 {node.id} 是子流程最后节点，对应主流程节点: {[p.id for p in parent_nodes]}'
                    )
                    return True

            # 检查下游节点是否都是结束节点
            if next_nodes:
                end_node_count = 0
                for next_node in next_nodes:
                    next_def_stmt = select(NodeDefinition).where(
                        NodeDefinition.id == next_node.node_definition_id
                    )
                    next_def_result = await self.db.execute(next_def_stmt)
                    next_def = next_def_result.scalar_one()

                    if next_def.type == NodeType.end:
                        end_node_count += 1

                # 如果所有下游节点都是结束节点，则当前节点是最后一个有效节点
                if end_node_count == len(next_nodes):
                    parent_node_stmt = select(NodeInstance).filter(
                        NodeInstance.subprocess_instance_id
                        == node.workflow_instance_id
                    )
                    parent_result = await self.db.execute(parent_node_stmt)
                    parent_nodes = parent_result.scalars().all()

                    if parent_nodes:
                        logger.info(f'节点 {node.id} 的下游都是结束节点，是子流程最后节点')
                        return True

            return False

        except Exception as e:
            logger.error(f'检查节点 {node.id} 是否为子流程最后节点时出错: {str(e)}')
            return False

    async def _get_parent_workflow_downstream_nodes(
        self, subprocess_id: int
    ) -> List[int]:
        """获取主流程的下游节点ID列表"""
        try:
            # 1. 找到对应的主流程节点
            parent_node_stmt = select(NodeInstance).filter(
                NodeInstance.subprocess_instance_id == subprocess_id
            )
            parent_result = await self.db.execute(parent_node_stmt)
            parent_nodes = parent_result.scalars().all()

            if not parent_nodes:
                logger.warning(f'未找到子流程 {subprocess_id} 对应的主流程节点')
                return []

            all_downstream_nodes = []

            for parent_node in parent_nodes:
                logger.info(f'处理主流程节点 {parent_node.id}，完成状态更新')

                # 2. 完成主流程节点
                await self.db.execute(
                    update(NodeInstance)
                    .where(NodeInstance.id == parent_node.id)
                    .values(
                        status=NodeStatus.completed, end_time=datetime.now()
                    )
                )

                # 3. 获取主流程节点的下游节点
                downstream_nodes = await self.get_connected_nodes(
                    parent_node.id, is_incoming=False
                )

                for downstream_node in downstream_nodes:
                    if downstream_node.node_definition_id == 11:  # 跳过特殊节点
                        continue

                    if await self.should_activate_node(downstream_node):
                        # 4. 检查下游节点是否包含子流程
                        if downstream_node.subprocess_instance_id:
                            logger.info(
                                f'主流程下游节点 {downstream_node.id} 包含子流程 {downstream_node.subprocess_instance_id}'
                            )
                            all_downstream_nodes.append(downstream_node.id)

                            # 5. 获取子流程的首个节点
                            subprocess_first_nodes = (
                                await self._get_subprocess_first_nodes(
                                    downstream_node.subprocess_instance_id
                                )
                            )
                            all_downstream_nodes.extend(subprocess_first_nodes)
                        else:
                            # 普通节点
                            all_downstream_nodes.append(downstream_node.id)

            logger.info(
                f'子流程 {subprocess_id} 完成后需要激活的节点: {all_downstream_nodes}'
            )
            return all_downstream_nodes

        except Exception as e:
            logger.error(f'获取主流程下游节点时出错: {str(e)}')
            return []

    async def _get_subprocess_first_nodes(
        self, subprocess_id: int
    ) -> List[int]:
        """获取子流程的首个节点ID列表"""
        try:
            first_node = await self._find_subprocess_first_node(subprocess_id)
            if first_node:
                logger.info(f'找到子流程 {subprocess_id} 的首个节点: {first_node.id}')
                return [first_node.id]
            return []
        except Exception as e:
            logger.error(f'获取子流程 {subprocess_id} 首个节点时出错: {str(e)}')
            return []

    async def update_node_status(
        self,
        node_id: int,
        status: NodeStatus,
        update_data: Optional[schemas.NodeInstanceUpdate] = None,
        decision_result: Optional[EdgeCondition] = None,
    ) -> bool:
        """更新节点状态并处理相关逻辑 - 优化版本"""

        # 使用异步锁防止并发问题
        async with self._lock:
            if node_id in self.processed_nodes:
                return True
            self.processed_nodes.add(node_id)

            try:
                # 分批处理，避免长事务
                (
                    node_info,
                    nodes_to_activate,
                ) = await self._update_single_node_status(
                    node_id, status, update_data, decision_result
                )

                # 提交当前节点的更新
                await self.db.commit()

                # 处理子流程状态 - 使用node_info字典而不是detached对象
                await self.handle_subprocess_status_from_info(node_info)
                await self.db.commit()

                # 更新工作流状态
                await self.update_workflow_status(
                    node_info['workflow_instance_id']
                )
                await self.db.commit()
                # 处理下游节点激活 - 使用队列方式避免递归
                await self._process_node_activation_queue(nodes_to_activate)

                return True

            except Exception as e:
                await self.db.rollback()
                logger.error(f'Error updating node {node_id}: {str(e)}')
                raise e
            finally:
                self.processed_nodes.discard(node_id)
                self.pending_status_updates.clear()

    async def _process_node_activation_queue(self, initial_nodes: List[int]):
        """优化的节点激活队列处理"""
        activation_queue = initial_nodes.copy()
        processed_in_batch = set()
        while activation_queue:
            current_node_id = activation_queue.pop(0)

            if current_node_id in processed_in_batch:
                continue

            if current_node_id in self.processed_nodes:
                continue
            self.processed_nodes.add(current_node_id)

            try:
                # 获取节点信息以确定是否需要特殊处理
                node_stmt = select(NodeInstance).where(
                    NodeInstance.id == current_node_id
                )
                node_result = await self.db.execute(node_stmt)
                node = node_result.scalar_one_or_none()

                if not node:
                    logger.warning(f'节点 {current_node_id} 不存在')
                    continue

                # 激活节点
                node_info, next_nodes = await self._update_single_node_status(
                    current_node_id, NodeStatus.active
                )
                await self.db.commit()

                # 处理子流程状态（如果节点包含子流程）
                if node_info.get('subprocess_instance_id'):
                    await self.handle_subprocess_status_from_info(node_info)
                    await self.db.commit()

                # 发送通知
                await self._send_node_notification(current_node_id)

                # 将下游节点加入队列（现在next_nodes已经包含了正确的节点）
                for next_node_id in next_nodes:
                    if next_node_id not in processed_in_batch:
                        activation_queue.append(next_node_id)

                processed_in_batch.add(current_node_id)

            except Exception as e:
                await self.db.rollback()
                logger.error(
                    f'Error activating node {current_node_id}: {str(e)}'
                )
            finally:
                self.processed_nodes.discard(current_node_id)

    async def _get_subprocess_ready_nodes(
        self, subprocess_id: int
    ) -> List[int]:
        """获取子流程中准备激活的节点"""
        try:
            # 找到子流程中状态为 active 的节点的下游节点
            active_nodes_stmt = select(NodeInstance).filter(
                and_(
                    NodeInstance.workflow_instance_id == subprocess_id,
                    NodeInstance.status == NodeStatus.active,
                )
            )

            active_result = await self.db.execute(active_nodes_stmt)
            active_nodes = active_result.scalars().all()

            ready_nodes = []
            for active_node in active_nodes:
                # 获取每个激活节点的下游节点
                next_nodes = await self.get_connected_nodes(
                    active_node.id, is_incoming=False
                )

                for next_node in next_nodes:
                    if next_node.node_definition_id == 11:  # 跳过特殊节点
                        continue
                    if await self.should_activate_node(next_node):
                        ready_nodes.append(next_node.id)

            return ready_nodes

        except Exception as e:
            logger.error(
                f'Error getting subprocess ready nodes for {subprocess_id}: {str(e)}'
            )
            return []

    async def _send_node_notification(self, node_id: int):
        """发送节点通知"""
        try:
            notify_info = await notification_management.notification.get_task_info_by_node_id(
                self.db, node_id
            )
            if notify_info:
                task, project_id, task_url, remaining_time = notify_info
                task_url = (
                    task_url
                    if task_url
                    else 'http://winddata.leadintelligent.com'
                )
                if task.need_approval:
                    task_url = f'{task_url}'
                if task.assigned_to:
                    await task_checker.task_checker.send_notification(
                        self.db,
                        task.assigned_to,
                        {
                            'project_id': f'{project_id}(主机)'
                            if task.device_type == 'main_machine'
                            else f'{project_id}(出料机)',
                            'task_url': task_url,
                            'remaining_time': remaining_time,
                            'assigned_to': task.assigned_to.split(',')[0],
                            'task_name': task.name,
                        },
                        task,
                    )
        except Exception as e:
            logger.error(
                f'Error sending notification for node {node_id}: {str(e)}'
            )

    async def handle_subprocess_status_from_info(self, node_info: Dict):
        """从节点信息字典处理子流程状态，避免detached对象问题"""
        if not node_info.get('subprocess_instance_id'):
            return

        subprocess = await self.db.get(
            WorkflowInstance, node_info['subprocess_instance_id']
        )

        if not subprocess:
            return

        if node_info['status'].value == NodeStatus.completed.value:
            # 子流程节点完成时，标记子流程为完成
            if subprocess.status != WorkflowStatus.completed:
                await self.db.execute(
                    update(WorkflowInstance)
                    .where(WorkflowInstance.id == subprocess.id)
                    .values(
                        status=WorkflowStatus.completed,
                        end_time=datetime.now(),
                    )
                )

        elif node_info['status'].value == NodeStatus.active.value:
            # 主流程节点激活时，启动子流程
            if subprocess.status != WorkflowStatus.active:  # 修复条件判断
                logger.info(f'启动子流程 {subprocess.id}')

                # 启动子流程
                await self.db.execute(
                    update(WorkflowInstance)
                    .where(WorkflowInstance.id == subprocess.id)
                    .values(
                        status=WorkflowStatus.active, start_time=datetime.now()
                    )
                )

            # 找到子流程的第一个节点（开始节点的下游节点）
            first_node = await self._find_subprocess_first_node(subprocess.id)
            if first_node:
                logger.info(f'激活子流程 {subprocess.id} 的第一个节点 {first_node.id}')
                # 直接激活第一个节点，不使用队列方式避免递归
                await self._activate_single_node(first_node.id)
                # 提交子流程首节点的激活
                await self.db.commit()

    async def _find_subprocess_first_node(
        self, workflow_instance_id: int
    ) -> Optional[NodeInstance]:
        """找到子流程的第一个可执行节点（开始节点的下游节点）"""
        # 先找到开始节点
        start_node_stmt = (
            select(NodeInstance)
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .filter(
                and_(
                    NodeInstance.workflow_instance_id == workflow_instance_id,
                    NodeDefinition.type == NodeType.start,
                )
            )
        )

        start_result = await self.db.execute(start_node_stmt)
        start_node = start_result.scalar_one_or_none()

        if not start_node:
            # 如果没有开始节点，找第一个普通节点
            first_node_stmt = (
                select(NodeInstance)
                .join(
                    NodeDefinition,
                    NodeInstance.node_definition_id == NodeDefinition.id,
                )
                .filter(
                    and_(
                        NodeInstance.workflow_instance_id
                        == workflow_instance_id,
                        NodeDefinition.type.notin_(
                            [NodeType.start, NodeType.end]
                        ),
                    )
                )
                .order_by(NodeDefinition.id)
                .limit(1)
            )
            result = await self.db.execute(first_node_stmt)
            return result.scalar_one_or_none()

        # 找到开始节点的下游节点
        next_nodes = await self.get_connected_nodes(
            start_node.id, is_incoming=False
        )
        if next_nodes:
            return next_nodes[0]  # 返回第一个下游节点

        return None

    async def _activate_single_node(self, node_id: int):
        """激活单个节点，不触发下游传播"""
        try:
            await self.db.execute(
                update(NodeInstance)
                .where(NodeInstance.id == node_id)
                .values(
                    status=NodeStatus.active,
                    start_time=datetime.now(),
                    end_time=None,
                    actual_duration=None,
                )
            )
            logger.info(f'成功激活节点 {node_id}')

            # 发送通知
            await self._send_node_notification(node_id)

        except Exception as e:
            logger.error(f'激活节点 {node_id} 失败: {str(e)}')
            raise

    async def _check_if_subprocess_completed(
        self, workflow_instance_id: int
    ) -> bool:
        """检查子流程是否所有节点都已完成"""
        # 获取子流程中所有非开始/结束节点
        stmt = (
            select(NodeInstance)
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .filter(
                and_(
                    NodeInstance.workflow_instance_id == workflow_instance_id,
                    NodeDefinition.type.notin_([NodeType.start, NodeType.end]),
                )
            )
        )

        result = await self.db.execute(stmt)
        nodes = result.scalars().all()

        if not nodes:
            return True

        # 检查是否所有节点都已完成
        return all(
            node.status.value == NodeStatus.completed.value for node in nodes
        )

    async def update_workflow_status(self, workflow_instance_id: int):
        """更新工作流状态 - 改进版本"""

        # 方法1: 检查结束节点状态（推荐）
        end_nodes_stmt = (
            select(NodeInstance)
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .filter(
                and_(
                    NodeInstance.workflow_instance_id == workflow_instance_id,
                    NodeDefinition.type == NodeType.end,
                )
            )
        )

        end_result = await self.db.execute(end_nodes_stmt)
        end_nodes = end_result.scalars().all()

        # 获取所有非开始/结束节点用于错误和活跃状态检查
        all_nodes_stmt = (
            select(NodeInstance)
            .join(
                NodeDefinition,
                NodeInstance.node_definition_id == NodeDefinition.id,
            )
            .filter(
                and_(
                    NodeInstance.workflow_instance_id == workflow_instance_id,
                    NodeDefinition.type.notin_([NodeType.start, NodeType.end]),
                )
            )
        )

        all_result = await self.db.execute(all_nodes_stmt)
        all_nodes = all_result.scalars().all()

        # 判断工作流状态
        new_status = None

        # 检查是否有错误节点
        has_error = any(node.status == NodeStatus.error for node in all_nodes)
        if has_error:
            new_status = WorkflowStatus.terminated

        # 检查是否有结束节点完成（表示至少一条路径走完了）
        elif end_nodes and any(
            node.status == NodeStatus.completed for node in end_nodes
        ):
            new_status = WorkflowStatus.completed

        # 检查是否有活跃节点
        elif any(node.status == NodeStatus.active for node in all_nodes):
            new_status = WorkflowStatus.active

        # 如果没有结束节点，使用叶子节点判断法
        elif not end_nodes:
            # 获取叶子节点（没有下游节点的节点）
            leaf_nodes = await self._get_leaf_nodes(workflow_instance_id)
            if leaf_nodes and any(
                node.status == NodeStatus.completed for node in leaf_nodes
            ):
                new_status = WorkflowStatus.completed

        if new_status:
            await self._update_workflow_status_and_handle_completion(
                workflow_instance_id, new_status
            )

    async def _get_leaf_nodes(
        self, workflow_instance_id: int
    ) -> List[NodeInstance]:
        """获取叶子节点（没有下游连接的节点）"""
        # 获取该工作流的所有节点
        all_nodes_stmt = select(NodeInstance).filter(
            NodeInstance.workflow_instance_id == workflow_instance_id
        )
        all_result = await self.db.execute(all_nodes_stmt)
        all_nodes = all_result.scalars().all()

        leaf_nodes = []
        for node in all_nodes:
            # 检查节点是否有下游连接
            outgoing_edges_stmt = select(EdgeInstance).filter(
                EdgeInstance.from_node_instance_id == node.id
            )
            outgoing_result = await self.db.execute(outgoing_edges_stmt)
            outgoing_edges = outgoing_result.scalars().all()

            if not outgoing_edges:  # 没有下游连接的就是叶子节点
                leaf_nodes.append(node)

        return leaf_nodes

    async def _get_downstream_nodes(
        self, node_instance_id: int, condition: Optional[EdgeCondition] = None
    ) -> List[int]:
        """
        获取下游节点ID列表

        Args:
            node_instance_id: 当前节点ID
            condition: 边条件（用于决策节点）

        Returns:
            List[int]: 下游节点ID列表
        """
        try:
            # 获取下游节点
            downstream_nodes = await self.get_connected_nodes(
                node_instance_id, is_incoming=False, condition=condition
            )

            nodes_to_activate = []
            for downstream_node in downstream_nodes:
                # 跳过特殊节点（如结束节点等）
                if downstream_node.node_definition_id == 11:
                    continue

                # 检查节点是否应该被激活
                if await self.should_activate_node(downstream_node):
                    nodes_to_activate.append(downstream_node.id)

            return nodes_to_activate

        except Exception as e:
            logger.error(
                f'Error getting downstream nodes for {node_instance_id}: {str(e)}'
            )
            return []

    async def _update_workflow_status_and_handle_completion(
        self, workflow_instance_id: int, new_status: WorkflowStatus
    ):
        """更新工作流状态并处理完成逻辑"""
        update_values = {'status': new_status}

        if new_status == WorkflowStatus.completed:
            update_values['end_time'] = datetime.now()

            # 处理子流程完成后的主流程节点激活
            await self._handle_subprocess_completion(workflow_instance_id)

        await self.db.execute(
            update(WorkflowInstance)
            .where(WorkflowInstance.id == workflow_instance_id)
            .values(**update_values)
        )

    async def _handle_subprocess_completion(self, subprocess_id: int):
        """处理子流程完成后的主流程节点激活"""
        try:
            # 找到对应的主流程节点（subprocess_instance_id 指向这个子流程的节点）
            parent_node_stmt = select(NodeInstance).filter(
                NodeInstance.subprocess_instance_id == subprocess_id
            )

            parent_result = await self.db.execute(parent_node_stmt)
            parent_nodes = parent_result.scalars().all()

            for parent_node in parent_nodes:
                logger.info(f'子流程 {subprocess_id} 完成，处理主流程节点 {parent_node.id}')

                # 完成主流程中的这个子流程节点
                await self.db.execute(
                    update(NodeInstance)
                    .where(NodeInstance.id == parent_node.id)
                    .values(
                        status=NodeStatus.completed, end_time=datetime.now()
                    )
                )

                # 找到这个节点的下游节点并激活它们
                next_nodes = await self.get_connected_nodes(
                    parent_node.id, is_incoming=False
                )

                nodes_to_activate = []
                for next_node in next_nodes:
                    if next_node.node_definition_id == 11:  # 跳过特殊节点
                        continue
                    if await self.should_activate_node(next_node):
                        nodes_to_activate.append(next_node.id)
                        logger.info(f'准备激活主流程下游节点 {next_node.id}')

                # 激活下游节点
                if nodes_to_activate:
                    await self._process_node_activation_queue(
                        nodes_to_activate
                    )

        except Exception as e:
            logger.error(f'处理子流程 {subprocess_id} 完成时出错: {str(e)}')
            raise

    async def _process_parent_node_completion(
        self, parent_node_ids: List[int]
    ):
        """处理父节点完成 - 已被 _handle_subprocess_completion 替代"""
        # 这个方法已经被新的 _handle_subprocess_completion 方法替代
        # 保留是为了兼容性，但建议使用新方法
        for node_id in parent_node_ids:
            try:
                node_info, _ = await self._update_single_node_status(
                    node_id, NodeStatus.completed
                )
                await self.db.commit()
            except Exception as e:
                await self.db.rollback()
                logger.error(
                    f'Error completing parent node {node_id}: {str(e)}'
                )

    async def handle_subprocess_status(self, node_instance: NodeInstance):
        """处理子流程状态 - 保留原方法以兼容性"""
        # 将NodeInstance对象转换为字典
        node_info = {
            'id': node_instance.id,
            'status': node_instance.status,
            'subprocess_instance_id': node_instance.subprocess_instance_id,
        }
        await self.handle_subprocess_status_from_info(node_info)
