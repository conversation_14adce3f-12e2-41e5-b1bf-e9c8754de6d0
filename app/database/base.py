from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.sql import text
from tenacity import retry, stop_after_attempt, wait_fixed
import config


engine = create_async_engine(
    config.SQLALCHEMY_DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=3600,
    pool_size=10,
    max_overflow=20,
)

AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, autocommit=False, autoflush=False
)

Base = declarative_base()


@asynccontextmanager
@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_db_session():
    """获取数据库会话的异步上下文管理器"""
    session = AsyncSessionLocal()
    try:
        await session.execute(text('SELECT 1'))
        yield session
        await session.commit()
    except Exception as e:
        await session.rollback()
        raise e
    finally:
        await session.close()


async def get_db_dependency():
    async with get_db_session() as session:
        yield session


async def create_tables():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
