from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import text
from typing import Dict, Optional, Any
from database.models import (
    WorkflowDefinition,
    NodeDefinition,
    EdgeDefinition,
    NodeType,
    EdgeCondition,
)
import traceback


class WorkflowModificationHandler:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.new_node_mapping = {}  # 用于存储新增节点的临时ID映射

    async def get_workflow_definition(
        self, workflow_id: int
    ) -> Optional[WorkflowDefinition]:
        """获取工作流定义"""
        result = await self.db.execute(
            text(
                """
                SELECT * FROM workflow_definitions
                WHERE id = :workflow_id
            """
            ),
            {'workflow_id': workflow_id},
        )
        return result.fetchone()

    async def create_workflow_version(
        self, old_workflow_id: int, modifications: Dict[str, Any]
    ) -> int:
        """创建新版本的工作流定义"""
        # 1. 获取原工作流定义
        old_workflow = await self.get_workflow_definition(old_workflow_id)
        if not old_workflow:
            raise ValueError('工作流定义不存在')

        # 2. 更新旧版本的is_latest标志
        await self.db.execute(
            text(
                """
                UPDATE workflow_definitions
                SET is_latest = FALSE
                WHERE id = :workflow_id
            """
            ),
            {'workflow_id': old_workflow_id},
        )

        # 3. 创建新版本工作流定义
        new_workflow = WorkflowDefinition(
            name=old_workflow.name,
            description=old_workflow.description,
            version=old_workflow.version + 1,
            is_latest=True,
            is_subprocess=old_workflow.is_subprocess,
        )
        self.db.add(new_workflow)
        await self.db.flush()

        # 4. 复制并修改节点定义
        await self.copy_and_modify_nodes(
            old_workflow_id, new_workflow.id, modifications.get('nodes', {})
        )

        # 5. 复制并修改边定义
        await self.copy_and_modify_edges(
            old_workflow_id, new_workflow.id, modifications.get('edges', {})
        )

        return new_workflow.id

    async def copy_and_modify_nodes(
        self,
        old_workflow_id: int,
        new_workflow_id: int,
        node_modifications: Dict[str, Dict],
    ):
        """复制并修改节点定义"""
        # 1. 获取原有节点
        result = await self.db.execute(
            text(
                """
                SELECT * FROM workflow_node_definitions
                WHERE workflow_definition_id = :workflow_id
            """
            ),
            {'workflow_id': old_workflow_id},
        )
        old_nodes = result.fetchall()

        # 2. 节点ID映射表 (old_id -> new_id)
        node_id_mapping = {}

        # 3. 处理节点复制和修改
        for old_node in old_nodes:
            node_mod = node_modifications.get(str(old_node.id), {})

            # 如果节点被标记为删除，跳过复制
            if node_mod.get('action') == 'delete':
                continue
            # 创建新节点
            new_node = NodeDefinition(
                workflow_definition_id=new_workflow_id,
                name=node_mod.get('name', old_node.name),
                type=NodeType(node_mod.get('type', old_node.type)),
                subprocess_id=node_mod.get(
                    'subprocess_id', old_node.subprocess_id
                ),
                color=node_mod.get('color', old_node.color),
                task_url=node_mod.get('task_url', old_node.task_url),
                need_approval=node_mod.get(
                    'need_approval', old_node.need_approval
                ),
                input_logic=node_mod.get('input_logic', old_node.input_logic),
                status_update_mode=node_mod.get(
                    'status_update_mode', old_node.status_update_mode
                ),
                status_query=node_mod.get(
                    'status_query', old_node.status_query
                ),
            )
            self.db.add(new_node)
            await self.db.flush()

            node_id_mapping[old_node.id] = new_node.id

        # 4. 处理新增节点
        for i, node_data in enumerate(node_modifications.get('new_nodes', [])):
            # 生成临时ID用于边的引用
            temp_id = f'new_{i}'

            new_node = NodeDefinition(
                workflow_definition_id=new_workflow_id,
                name=node_data['name'],
                type=NodeType(node_data['type']),
                subprocess_id=node_data.get('subprocess_id'),
                color=node_data.get('color'),
                expected_duration=node_data.get('expected_duration'),
                task_url=node_data.get('task_url'),
                need_approval=node_data.get('need_approval', False),
            )
            self.db.add(new_node)
            await self.db.flush()

            # 存储新节点ID映射
            self.new_node_mapping[temp_id] = new_node.id
            node_id_mapping[temp_id] = new_node.id  # 同时添加到总映射中

        return node_id_mapping

    async def copy_and_modify_edges(
        self,
        old_workflow_id: int,
        new_workflow_id: int,
        edge_modifications: Dict[str, Dict],
    ):
        """复制并修改边定义"""
        # 1. 获取原有边
        result = await self.db.execute(
            text(
                """
                SELECT * FROM workflow_edge_definitions
                WHERE workflow_definition_id = :workflow_id
            """
            ),
            {'workflow_id': old_workflow_id},
        )
        old_edges = result.fetchall()

        # 2. 获取节点ID映射
        result = await self.db.execute(
            text(
                """
                SELECT old.id as old_id, new.id as new_id
                FROM workflow_node_definitions old
                JOIN workflow_node_definitions new
                    ON old.name = new.name
                    AND new.workflow_definition_id = :new_workflow_id
                WHERE old.workflow_definition_id = :old_workflow_id
            """
            ),
            {
                'old_workflow_id': old_workflow_id,
                'new_workflow_id': new_workflow_id,
            },
        )
        node_mapping = {row.old_id: row.new_id for row in result}
        # 合并新节点映射
        node_mapping.update(self.new_node_mapping)

        # 3. 处理边的复制和修改
        for old_edge in old_edges:
            edge_mod = edge_modifications.get(str(old_edge.id), {})

            # 如果边被标记为删除，跳过复制
            if edge_mod.get('action') == 'delete':
                continue

            # 获取新的节点ID
            from_node_id = node_mapping.get(
                edge_mod.get('from_node_id', old_edge.from_node_id)
            )
            to_node_id = node_mapping.get(
                edge_mod.get('to_node_id', old_edge.to_node_id)
            )

            # 如果源节点或目标节点不存在，跳过该边
            if not from_node_id or not to_node_id:
                continue

            # 创建新边
            new_edge = EdgeDefinition(
                workflow_definition_id=new_workflow_id,
                from_node_id=from_node_id,
                to_node_id=to_node_id,
                condition=EdgeCondition(
                    edge_mod.get('condition', old_edge.condition.value)
                )
                if edge_mod.get('condition') or old_edge.condition
                else None,
            )
            self.db.add(new_edge)

        # 4. 处理新增边
        for edge_data in edge_modifications.get('new_edges', []):
            from_node_id = node_mapping.get(str(edge_data['from_node_id']))
            to_node_id = node_mapping.get(str(edge_data['to_node_id']))

            if from_node_id and to_node_id:
                new_edge = EdgeDefinition(
                    workflow_definition_id=new_workflow_id,
                    from_node_id=from_node_id,
                    to_node_id=to_node_id,
                    condition=EdgeCondition(edge_data['condition'])
                    if 'condition' in edge_data
                    else None,
                )
                self.db.add(new_edge)

        await self.db.flush()

    async def migrate_instances(
        self, old_workflow_id: int, new_workflow_id: int
    ):
        """迁移现有实例到新版本的工作流定义"""
        # 1. 获取所有活动的工作流实例
        result = await self.db.execute(
            text(
                """
                SELECT * FROM workflow_instances
                WHERE workflow_definition_id = :workflow_id
                AND status = 'terminated'
            """
            ),
            {'workflow_id': old_workflow_id},
        )
        active_instances = result.fetchall()

        for instance in active_instances:
            # 2. 更新工作流实例的定义ID
            await self.db.execute(
                text(
                    """
                    UPDATE workflow_instances
                    SET workflow_definition_id = :new_workflow_id
                    WHERE id = :instance_id
                """
                ),
                {
                    'new_workflow_id': new_workflow_id,
                    'instance_id': instance.id,
                },
            )

            # 3. 调整节点实例
            await self.adjust_node_instances(
                instance.id, old_workflow_id, new_workflow_id
            )

            # 4. 调整边实例
            await self.adjust_edge_instances(
                instance.id, old_workflow_id, new_workflow_id
            )

    async def adjust_node_instances(
        self, instance_id: int, old_workflow_id: int, new_workflow_id: int
    ):
        """调整节点实例以匹配新的工作流定义"""
        # 1. 获取工作流实例的所有节点实例
        result = await self.db.execute(
            text(
                """
                SELECT ni.*, nd.name as node_name
                FROM workflow_node_instances ni
                JOIN workflow_node_definitions nd ON ni.node_definition_id = nd.id
                WHERE ni.workflow_instance_id = :instance_id
            """
            ),
            {'instance_id': instance_id},
        )
        current_node_instances = {
            row.node_definition_id: row for row in result.fetchall()
        }

        # 2. 获取新旧节点定义的映射关系
        result = await self.db.execute(
            text(
                """
                SELECT
                    old.id as old_id,
                    old.name as old_name,
                    new.id as new_id,
                    new.name as new_name,
                    new.type as new_type
                FROM workflow_node_definitions old
                LEFT JOIN workflow_node_definitions new
                    ON old.name = new.name
                    AND new.workflow_definition_id = :new_workflow_id
                WHERE old.workflow_definition_id = :old_workflow_id
            """
            ),
            {
                'old_workflow_id': old_workflow_id,
                'new_workflow_id': new_workflow_id,
            },
        )
        node_mappings = result.fetchall()

        # 3. 获取新工作流中的所有节点
        result = await self.db.execute(
            text(
                """
                SELECT id, name, type
                FROM workflow_node_definitions
                WHERE workflow_definition_id = :workflow_id
            """
            ),
            {'workflow_id': new_workflow_id},
        )
        new_nodes = result.fetchall()

        # 4. 处理节点实例的调整
        for new_node in new_nodes:
            # 查找对应的旧节点映射
            old_mapping = next(
                (m for m in node_mappings if m.new_id == new_node.id), None
            )

            if old_mapping and old_mapping.old_id in current_node_instances:
                # 更新现有节点实例
                old_instance = current_node_instances[old_mapping.old_id]
                await self.db.execute(
                    text(
                        """
                        UPDATE workflow_node_instances
                        SET node_definition_id = :new_node_id
                        WHERE id = :instance_id
                    """
                    ),
                    {
                        'new_node_id': new_node.id,
                        'instance_id': old_instance.id,
                    },
                )
            else:
                # 创建新节点实例
                await self.db.execute(
                    text(
                        """
                        INSERT INTO workflow_node_instances
                        (workflow_instance_id, node_definition_id, status, assigned_to)
                        VALUES (:workflow_instance_id, :node_definition_id, 'pending', NULL)
                    """
                    ),
                    {
                        'workflow_instance_id': instance_id,
                        'node_definition_id': new_node.id,
                    },
                )

        # 5. 删除不再存在的节点实例
        new_node_ids = [node.id for node in new_nodes]
        for old_node_id, old_instance in current_node_instances.items():
            mapped_new_id = next(
                (m.new_id for m in node_mappings if m.old_id == old_node_id),
                None,
            )
            if mapped_new_id not in new_node_ids:
                await self.db.execute(
                    text(
                        """
                        DELETE FROM workflow_node_instances
                        WHERE id = :instance_id
                    """
                    ),
                    {'instance_id': old_instance.id},
                )

    async def adjust_edge_instances(
        self, instance_id: int, old_workflow_id: int, new_workflow_id: int
    ):
        """调整边实例以匹配新的工作流定义"""
        # 1. 获取当前的边实例
        result = await self.db.execute(
            text(
                """
                SELECT ei.*, ed.from_node_id as def_from_id, ed.to_node_id as def_to_id
                FROM workflow_edge_instances ei
                JOIN workflow_edge_definitions ed ON ei.edge_definition_id = ed.id
                WHERE ei.workflow_instance_id = :instance_id
            """
            ),
            {'instance_id': instance_id},
        )
        current_edge_instances = {
            row.edge_definition_id: row for row in result.fetchall()
        }

        # 2. 获取节点实例映射
        result = await self.db.execute(
            text(
                """
                SELECT
                    ni.id as instance_id,
                    ni.node_definition_id as definition_id
                FROM workflow_node_instances ni
                WHERE ni.workflow_instance_id = :instance_id
            """
            ),
            {'instance_id': instance_id},
        )
        node_instance_mapping = {
            row.definition_id: row.instance_id for row in result.fetchall()
        }

        # 3. 获取新的边定义
        result = await self.db.execute(
            text(
                """
                SELECT id, from_node_id, to_node_id
                FROM workflow_edge_definitions
                WHERE workflow_definition_id = :workflow_id
            """
            ),
            {'workflow_id': new_workflow_id},
        )
        new_edges = result.fetchall()

        # 4. 获取新旧边定义的映射关系
        result = await self.db.execute(
            text(
                """
                SELECT
                    old_edge.id as old_id,
                    new_edge.id as new_id,
                    old_edge.from_node_id as old_from_id,
                    old_edge.to_node_id as old_to_id,
                    new_edge.from_node_id as new_from_id,
                    new_edge.to_node_id as new_to_id
                FROM workflow_edge_definitions old_edge
                JOIN workflow_edge_definitions new_edge
                    ON new_edge.workflow_definition_id = :new_workflow_id
                WHERE old_edge.workflow_definition_id = :old_workflow_id
                    AND old_edge.from_node_id = new_edge.from_node_id
                    AND old_edge.to_node_id = new_edge.to_node_id
            """
            ),
            {
                'old_workflow_id': old_workflow_id,
                'new_workflow_id': new_workflow_id,
            },
        )
        edge_mappings = result.fetchall()

        # 5. 处理边实例的调整
        for new_edge in new_edges:
            # 找到对应的节点实例
            from_node_instance = node_instance_mapping.get(
                new_edge.from_node_id
            )
            to_node_instance = node_instance_mapping.get(new_edge.to_node_id)

            if not from_node_instance or not to_node_instance:
                continue

            # 查找对应的旧边映射
            old_mapping = next(
                (m for m in edge_mappings if m.new_id == new_edge.id), None
            )

            if old_mapping and old_mapping.old_id in current_edge_instances:
                # 更新现有边实例
                old_instance = current_edge_instances[old_mapping.old_id]
                await self.db.execute(
                    text(
                        """
                        UPDATE workflow_edge_instances
                        SET edge_definition_id = :new_edge_id,
                            from_node_instance_id = :from_node_instance_id,
                            to_node_instance_id = :to_node_instance_id
                        WHERE id = :instance_id
                    """
                    ),
                    {
                        'new_edge_id': new_edge.id,
                        'from_node_instance_id': from_node_instance,
                        'to_node_instance_id': to_node_instance,
                        'instance_id': old_instance.id,
                    },
                )
            else:
                # 创建新边实例
                await self.db.execute(
                    text(
                        """
                        INSERT INTO workflow_edge_instances
                        (workflow_instance_id, edge_definition_id,
                         from_node_instance_id, to_node_instance_id)
                        VALUES (:workflow_instance_id, :edge_definition_id,
                               :from_node_instance_id, :to_node_instance_id)
                    """
                    ),
                    {
                        'workflow_instance_id': instance_id,
                        'edge_definition_id': new_edge.id,
                        'from_node_instance_id': from_node_instance,
                        'to_node_instance_id': to_node_instance,
                    },
                )

        # 6. 删除不再存在的边实例
        new_edge_ids = [edge.id for edge in new_edges]
        for old_edge_id, old_instance in current_edge_instances.items():
            mapped_new_id = next(
                (m.new_id for m in edge_mappings if m.old_id == old_edge_id),
                None,
            )
            if mapped_new_id not in new_edge_ids:
                await self.db.execute(
                    text(
                        """
                        DELETE FROM workflow_edge_instances
                        WHERE id = :instance_id
                    """
                    ),
                    {'instance_id': old_instance.id},
                )

        await self.db.flush()


async def modify_workflow(
    db: AsyncSession, workflow_id: int, modifications: Dict[str, Any]
) -> int:
    """修改工作流定义的主函数"""
    try:
        handler = WorkflowModificationHandler(db)

        # # 1. 创建新版本的工作流定义
        # new_workflow_id = await handler.create_workflow_version(workflow_id, modifications)

        # 2. 迁移现有实例
        await handler.migrate_instances(workflow_id, 8)

        # 3. 提交事务
        await db.commit()

        return 8

    except Exception as e:
        await db.rollback()
        print(traceback.format_exc())
        raise Exception(f'修改工作流时发生错误: {str(e)}')


async def test_workflow_modification():
    """测试工作流修改功能"""
    from database.base import engine, create_tables

    async with AsyncSession(engine) as db:
        try:
            # 确保表已创建
            await create_tables()

            # 测试修改 - 以主流程为例
            workflow_id = 1  # 假设主流程的ID为1

            # 示例修改 - 包含新节点和关联边
            modifications = {
                'nodes': {
                    # 添加新节点
                    'new_nodes': [
                        {
                            'name': '机型负责人录入版本信息',
                            'type': 'process',
                            'need_approval': True,
                        },
                        {
                            'name': '生成IO信息',
                            'type': 'process',
                            'need_approval': True,
                        },
                        {
                            'name': 'EM/IO信息补充',
                            'type': 'process',
                            'need_approval': True,
                        },
                        {
                            'name': '硬件开发负责人审核',
                            'type': 'process',
                            'need_approval': True,
                        },
                    ]
                },
                'edges': {
                    # 删除某个边
                    '5': {'action': 'delete'},
                    '8': {'action': 'delete'},
                    '15': {'action': 'delete'},
                    # 添加新边 - 使用临时ID引用新节点
                    'new_edges': [
                        {
                            'from_node_id': '4',  # 现有节点ID
                            'to_node_id': 'new_0',  # 引用第一个新节点
                        },
                        {
                            'from_node_id': 'new_0',  # 第一个新节点
                            'to_node_id': '6',  # 第二个新节点
                        },
                        {
                            'from_node_id': '6',  # 第一个新节点
                            'to_node_id': 'new_1',  # 第二个新节点
                        },
                        {
                            'from_node_id': 'new_1',  # 第一个新节点
                            'to_node_id': 'new_2',  # 第二个新节点
                        },
                        {
                            'from_node_id': 'new_2',  # 第一个新节点
                            'to_node_id': '7',  # 第二个新节点
                        },
                        {
                            'from_node_id': '12',  # 第一个新节点
                            'to_node_id': 'new_3',  # 第二个新节点
                        },
                        {
                            'from_node_id': 'new_3',  # 第一个新节点
                            'to_node_id': '13',  # 第二个新节点
                        },
                    ],
                },
            }

            # 执行修改
            new_workflow_id = await modify_workflow(
                db, workflow_id, modifications
            )
            print(f'已创建新版本工作流，ID: {new_workflow_id}')

            # 输出修改后的工作流结构
            result = await db.execute(
                text(
                    """
                    SELECT n.name, n.type, e.from_node_id, e.to_node_id
                    FROM workflow_node_definitions n
                    LEFT JOIN workflow_edge_definitions e ON n.workflow_definition_id = e.workflow_definition_id
                    WHERE n.workflow_definition_id = :workflow_id
                    ORDER BY n.id
                """
                ),
                {'workflow_id': new_workflow_id},
            )
            nodes_and_edges = result.fetchall()

            print('\n修改后的工作流结构:')
            print('节点列表:')
            nodes_seen = set()
            for row in nodes_and_edges:
                if row.name not in nodes_seen:
                    print(f'- {row.name} ({row.type})')
                    nodes_seen.add(row.name)

            print('\n连线列表:')
            for row in nodes_and_edges:
                if row.from_node_id and row.to_node_id:
                    print(f'- 从节点{row.from_node_id}到节点{row.to_node_id}')

        except Exception as e:
            print(f'测试过程中发生错误: {str(e)}')
            raise
        finally:
            if db:
                await db.close()
                await engine.dispose()


if __name__ == '__main__':
    import asyncio

    # 运行测试
    asyncio.run(test_workflow_modification())
