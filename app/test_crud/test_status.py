import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import asyncio

# Import your CRUDStatus class and dependencies
from database.crud.status_management import status as crud_status
from database.base import get_db_dependency, engine


async def test_crud_status():
    """Test various methods of CRUDStatus"""

    # Get database session
    async for db in get_db_dependency():
        try:
            print('\n=== Testing CRUDStatus Methods ===')

            # Test get_erp_info
            print('\n1. Testing get_erp_info:')
            erp_info = await crud_status.get_erp_info(db)
            print(f'Found {len(erp_info)} ERP records')

            # Test get_erp_framework_list
            print('\n2. Testing get_erp_framework_list:')
            framework_list = await crud_status.get_erp_framework_list(db)
            print(f'Found {len(framework_list)} framework records')
            if framework_list:
                print('Sample framework record:', framework_list[0])

            # Test get_components_info
            print('\n3. Testing get_components_info:')
            components_info = await crud_status.get_components_info(db)
            print(f'Found {len(components_info)} component records')
            if components_info:
                print('Sample component record:', components_info[0])

            # Test check_device_research_status
            print('\n4. Testing check_device_research_status:')
            research_status = await crud_status.check_device_research_status(
                db, '37130'
            )
            print(f'Device research status for ERP 37130:', research_status)

            # Test check_assembly_parts_list
            print('\n5. Testing check_assembly_parts_list:')
            assembly_status = await crud_status.check_assembly_parts_list(
                db, '37130'
            )
            print(
                f'Assembly parts list status for ERP 37130:', assembly_status
            )

            # Test check_component_servo_info
            print('\n6. Testing check_component_servo_info:')
            servo_info = await crud_status.check_component_servo_info(
                db, '37130'
            )
            print(f'Component servo info for ERP 37130:', servo_info)

            # Test check_component_full_info
            print('\n7. Testing check_component_full_info:')
            full_info = await crud_status.check_component_full_info(
                db, '37130'
            )
            print(f'Component full info for ERP 37130:', full_info)

            # Test check_erp_mech_info
            print('\n8. Testing check_erp_mech_info:')
            mech_info = await crud_status.check_erp_mech_info(db, '37130')
            print(f'ERP mech info for ERP 37130:', mech_info)

            # Test check_em_status
            print('\n9. Testing check_em_status:')
            em_status = await crud_status.check_em_status(db, '37130')
            print(f'EM status for ERP 37130:', em_status)

            # Test check_activity_completeness
            print('\n10. Testing check_activity_completeness:')
            activity_status = await crud_status.check_activity_completeness(
                db, '37130'
            )
            print(f'Activity completeness for ERP 37130:', activity_status)

            # Test check_plc_hmi_status
            print('\n11. Testing check_plc_hmi_status:')
            plc_hmi_status = await crud_status.check_plc_hmi_status(
                db, '37130'
            )
            print(f'PLC/HMI status for ERP 37130:', plc_hmi_status)

            # Test check_erp_machine_num
            print('\n12. Testing check_erp_machine_num:')
            machine_num_status = await crud_status.check_erp_machine_num(
                db, '37130'
            )
            print(f'Machine number status for ERP 37130:', machine_num_status)

            # Test check_erp_bom_accuracy
            print('\n13. Testing check_erp_bom_accuracy:')
            bom_accuracy = await crud_status.check_erp_bom_accuracy(
                db, '37130'
            )
            print(f'BOM accuracy for ERP 37130:', bom_accuracy)

            # Test check_erp_bom_info
            print('\n14. Testing check_erp_bom_info:')
            bom_info = await crud_status.check_erp_bom_info(db, '37130')
            print(f'BOM info for ERP 37130:', bom_info)

            # Test get_erp_machine_num_info
            print('\n15. Testing get_erp_machine_num_info:')
            machine_num_info = await crud_status.get_erp_machine_num_info(db)
            print('Machine number info:', machine_num_info)

            # Test get_all_erp_latest_statistics
            print('\n16. Testing get_all_erp_latest_statistics:')
            statistics = await crud_status.get_all_erp_latest_statistics(db)
            print(f'Found statistics for {len(statistics)} ERPs')
            if statistics:
                print('Sample statistics:', statistics[0])

            # Test always_true method
            print('\n17. Testing always_true:')
            always_true_result = await crud_status.always_true()
            print('Always true result:', always_true_result)

        except Exception as e:
            print(f'Error during testing: {str(e)}')
            import traceback

            traceback.print_exc()


async def main():
    """Main function to run the tests"""
    try:
        print('Starting CRUDStatus tests...')
        await test_crud_status()
        print('\nTests completed.')
    except Exception as e:
        print(f'Error during testing: {str(e)}')
    finally:
        await engine.dispose()


if __name__ == '__main__':
    # Run the async main function
    asyncio.run(main())
