# 在脚本开头添加
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# test_approval.py
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from datetime import datetime
from typing import List

from database.base import engine, create_tables
from database.models import (
    WorkflowDefinition,
    WorkflowInstance,
    NodeDefinition,
    NodeInstance,
    NodeStatus,
    NodeType,
    WorkflowStatus,
)
import schemas
from database.crud.approval_management import approval_record


class TestData:
    def __init__(self):
        self.workflow_id: int = None
        self.node_ids: List[int] = []
        self.workflow = None
        self.nodes = []


async def create_test_data(db: AsyncSession) -> TestData:
    """创建测试所需的基础数据，并预先获取所有需要的ID"""
    test_data = TestData()
    try:
        # 1. 创建工作流定义
        workflow_def = WorkflowDefinition(
            name='测试工作流', description='用于测试的工作流定义', version=1, is_latest=True
        )
        db.add(workflow_def)
        await db.flush()
        workflow_def_id = workflow_def.id

        # 2. 创建工作流实例
        workflow_instance = WorkflowInstance(
            workflow_definition_id=workflow_def_id,
            status=WorkflowStatus.active,
            start_time=datetime.now(),
            created_by='test_user',
            project_id='TEST-001',
        )
        db.add(workflow_instance)
        await db.flush()
        test_data.workflow_id = workflow_instance.id
        test_data.workflow = workflow_instance

        # 3. 创建节点定义
        node_defs = []
        for i in range(2):  # 创建两个测试节点
            node_def = NodeDefinition(
                workflow_definition_id=workflow_def_id,
                name=f'测试节点{i+1}',
                type=NodeType.process,
            )
            db.add(node_def)
            await db.flush()
            node_defs.append((node_def.id, node_def))

        # 4. 创建节点实例并预先获取ID
        for node_def_id, node_def in node_defs:
            node = NodeInstance(
                workflow_instance_id=test_data.workflow_id,
                node_definition_id=node_def_id,
                status=NodeStatus.active,
                start_time=datetime.now(),
                assigned_to='test_user',
            )
            db.add(node)
            await db.flush()
            node_id = node.id
            test_data.node_ids.append(node_id)
            test_data.nodes.append(node)

        await db.commit()
        return test_data

    except Exception as e:
        await db.rollback()
        raise e


async def test_approval_management():
    """测试审批管理功能"""
    async with AsyncSession(engine) as db:
        try:
            print('开始审批管理测试...')

            # 1. 创建测试数据
            test_data = await create_test_data(db)
            print('基础数据创建完成')

            # 2. 为第一个节点创建审批记录
            node_id = test_data.node_ids[0]
            approval_data = schemas.ApprovalRecordCreate(
                node_instance_id=node_id,
                approved_by='测试审批人',
                approval_status='approved',
                comments='测试审批通过',
            )

            created_approval = await approval_record.create_approval_record(
                db, approval_data
            )
            print(f'创建审批记录成功: {created_approval.id}')

            # 3. 获取节点的审批记录
            approvals = await approval_record.get_node_approvals(db, node_id)
            print(f'\n获取到 {len(approvals)} 条审批记录:')
            for approval in approvals:
                print(f'- 审批人: {approval.approved_by}')
                print(f'- 状态: {approval.approval_status}')
                print(f'- 评论: {approval.comments}')

            # 4. 创建多个审批记录
            additional_approvals = [
                ('审批人2', 'rejected', '需要修改'),
                ('审批人3', 'approved', '修改后通过'),
            ]

            for approved_by, status, comments in additional_approvals:
                approval_data = schemas.ApprovalRecordCreate(
                    node_instance_id=node_id,
                    approved_by=approved_by,
                    approval_status=status,
                    comments=comments,
                )
                await approval_record.create_approval_record(db, approval_data)

            # 5. 再次获取并验证所有审批记录
            all_approvals = await approval_record.get_node_approvals(
                db, node_id
            )
            print(f'\n总共获取到 {len(all_approvals)} 条审批记录')

            await db.commit()
            print('\n所有审批管理测试完成')

        except Exception as e:
            await db.rollback()
            print(f'测试过程中发生错误: {str(e)}')
            raise


async def run_test():
    """运行测试"""
    try:
        await create_tables()
        await test_approval_management()
    finally:
        await engine.dispose()


if __name__ == '__main__':
    asyncio.run(run_test())
