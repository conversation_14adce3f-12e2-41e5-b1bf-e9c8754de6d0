# test_change_log.py
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from datetime import datetime
from dataclasses import dataclass
from typing import List

from database.base import engine, create_tables
from database.models import (
    WorkflowInstance,
    WorkflowDefinition,
    WorkflowStatus,
)
import schemas
from database.crud.change_log_management import change_log


@dataclass
class TestWorkflowData:
    """测试数据结构"""

    workflow_def_id: int
    workflow_instance_id: int
    workflow_instance: WorkflowInstance


async def create_test_data(db: AsyncSession) -> TestWorkflowData:
    """创建测试所需的基础数据并预先获取ID"""
    try:
        # 1. 创建工作流定义
        workflow_def = WorkflowDefinition(
            name='测试工作流', description='用于测试的工作流定义', version=1, is_latest=True
        )
        db.add(workflow_def)
        await db.flush()
        workflow_def_id = workflow_def.id

        # 2. 创建工作流实例并立即获取ID
        workflow_instance = WorkflowInstance(
            workflow_definition_id=workflow_def_id,
            status=WorkflowStatus.active,
            start_time=datetime.now(),
            created_by='test_user',
            project_id='TEST-001',
        )
        db.add(workflow_instance)
        await db.flush()

        return TestWorkflowData(
            workflow_def_id=workflow_def_id,
            workflow_instance_id=workflow_instance.id,
            workflow_instance=workflow_instance,
        )

    except Exception as e:
        await db.rollback()
        raise e


async def create_change_logs(
    db: AsyncSession, workflow_instance_id: int, test_logs: List[tuple]
) -> List[int]:
    """创建一组变更日志"""
    log_ids = []
    try:
        for change_type, details in test_logs:
            log_create = schemas.ChangeLogCreate(
                workflow_instance_id=workflow_instance_id,
                changed_by='test_user',
                change_type=change_type,
                change_details=details,
            )

            created_log = await change_log.create_change_log(db, log_create)
            log_ids.append(created_log.id)

        await db.flush()
        return log_ids
    except Exception as e:
        raise e


async def test_change_log_management():
    """测试变更日志功能"""
    async with AsyncSession(engine) as db:
        try:
            print('开始变更日志测试...')

            # 1. 创建测试数据
            test_data = await create_test_data(db)
            print('测试数据创建完成')

            # 2. 创建多个变更日志
            change_types = [
                ('workflow_started', '工作流启动'),
                ('node_completed', '节点1完成'),
                ('status_changed', '状态更新为进行中'),
                ('user_assigned', '分配给新用户'),
            ]

            log_ids = await create_change_logs(
                db, test_data.workflow_instance_id, change_types
            )
            print(f'\n成功创建 {len(log_ids)} 条变更日志')

            # 3. 获取工作流的变更历史
            changes = await change_log.get_workflow_changes(
                db, test_data.workflow_instance_id
            )
            print(f'\n获取到 {len(changes)} 条变更记录:')
            for change in changes:
                print(
                    f"- [{change.created_at.strftime('%Y-%m-%d %H:%M:%S')}] "
                    f'{change.change_type}: {change.change_details}'
                )

            # 4. 验证日志顺序（应该是按时间倒序）
            is_ordered = all(
                changes[i].created_at >= changes[i + 1].created_at
                for i in range(len(changes) - 1)
            )
            print(f'\n日志时间排序正确: {is_ordered}')

            # 5. 再次添加一条日志验证实时性
            new_log = await change_log.create_change_log(
                db,
                schemas.ChangeLogCreate(
                    workflow_instance_id=test_data.workflow_instance_id,
                    changed_by='test_user',
                    change_type='test_added',
                    change_details='测试添加新日志',
                ),
            )
            print(f'\n新增日志ID: {new_log.id}')

            # 6. 再次获取并验证数量
            updated_changes = await change_log.get_workflow_changes(
                db, test_data.workflow_instance_id
            )
            print(f'更新后总日志数: {len(updated_changes)}')

            await db.commit()
            print('\n所有变更日志测试完成')

        except Exception as e:
            await db.rollback()
            print(f'测试过程中发生错误: {str(e)}')
            raise


async def run_test():
    """运行测试"""
    try:
        await create_tables()
        await test_change_log_management()
    finally:
        await engine.dispose()


if __name__ == '__main__':
    asyncio.run(run_test())
