# test_task_management.py
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from datetime import datetime
from dataclasses import dataclass
from typing import List

from database.base import engine, create_tables
from database.models import (
    WorkflowDefinition,
    WorkflowInstance,
    NodeDefinition,
    NodeInstance,
    NodeStatus,
    NodeType,
    WorkflowStatus,
)
import schemas
from database.crud.task_management import node_instance


@dataclass
class TestNode:
    """节点测试数据"""

    id: int
    definition_id: int
    name: str


@dataclass
class TestData:
    """测试数据结构"""

    workflow_id: int
    nodes: List[TestNode]


async def create_test_data(db: AsyncSession) -> TestData:
    """创建测试所需的基础数据"""
    try:
        # 1. 创建工作流定义
        workflow_def = WorkflowDefinition(
            name='测试工作流', description='用于测试的工作流定义', version=1, is_latest=True
        )
        db.add(workflow_def)
        await db.flush()
        workflow_def_id = workflow_def.id

        # 2. 创建工作流实例
        workflow_instance = WorkflowInstance(
            workflow_definition_id=workflow_def_id,
            status=WorkflowStatus.active,
            start_time=datetime.now(),
            created_by='test_user',
            project_id='TEST-001',
        )
        db.add(workflow_instance)
        await db.flush()
        workflow_instance_id = workflow_instance.id

        # 3. 创建测试节点
        test_nodes = []
        for i, (name, node_type) in enumerate(
            [
                ('开始节点', NodeType.start),
                ('处理节点1', NodeType.process),
                ('处理节点2', NodeType.process),
                ('决策节点', NodeType.decision),
                ('结束节点', NodeType.end),
            ]
        ):
            # 创建节点定义
            node_def = NodeDefinition(
                workflow_definition_id=workflow_def_id,
                name=name,
                type=node_type,
            )
            db.add(node_def)
            await db.flush()
            node_def_id = node_def.id

            # 创建节点实例
            node = NodeInstance(
                workflow_instance_id=workflow_instance_id,
                node_definition_id=node_def_id,
                status=NodeStatus.pending,
            )
            db.add(node)
            await db.flush()

            test_nodes.append(
                TestNode(id=node.id, definition_id=node_def_id, name=name)
            )

        await db.commit()
        return TestData(workflow_id=workflow_instance_id, nodes=test_nodes)

    except Exception as e:
        await db.rollback()
        raise e


async def test_task_management():
    """测试任务管理功能"""
    async with AsyncSession(engine) as db:
        try:
            print('开始任务管理测试...')

            # 1. 创建测试数据
            test_data = await create_test_data(db)
            print('基础数据创建完成')

            # 2. 获取工作流节点
            workflow_nodes = await node_instance.get_workflow_nodes(
                db, test_data.workflow_id
            )
            print(f'\n获取到工作流节点数量: {len(workflow_nodes)}')

            # 3. 更新节点状态
            process_node = next(
                node for node in test_data.nodes if '处理节点1' in node.name
            )

            status_update = schemas.NodeInstanceUpdate(
                start_time=datetime.now(), assigned_to='test_user'
            )

            success = await node_instance.update_node_status(
                db, process_node.id, NodeStatus.active, status_update
            )
            print(f"\n节点状态更新: {'成功' if success else '失败'}")

            # 4. 获取节点详情
            node = await node_instance.get_node_instance(db, process_node.id)
            print(f'节点状态: {node.status}')

            # 5. 测试用户任务查询
            user_tasks = await node_instance.get_user_tasks(db, 'test_user')
            print(f'\n查询到用户任务数量: {len(user_tasks)}')

            await db.commit()
            print('\n所有任务管理测试完成')

        except Exception as e:
            await db.rollback()
            print(f'测试过程中发生错误: {str(e)}')
            raise


async def run_test():
    """运行测试"""
    try:
        await create_tables()
        await test_task_management()
    finally:
        await engine.dispose()


if __name__ == '__main__':
    asyncio.run(run_test())
