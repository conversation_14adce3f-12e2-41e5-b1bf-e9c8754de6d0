# test_notification.py
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List

from database.base import engine, create_tables
from database.models import (
    WorkflowDefinition,
    WorkflowInstance,
    NodeDefinition,
    NodeInstance,
    NodeStatus,
    NodeType,
    WorkflowStatus,
)
from database.crud.notification_management import notification


@dataclass
class TestNode:
    """节点测试数据"""

    id: int
    definition_id: int
    workflow_instance_id: int
    name: str
    assigned_to: str


@dataclass
class TestData:
    """测试数据结构"""

    workflow_definition_id: int
    workflow_instance_id: int
    nodes: List[TestNode]


async def create_test_data(db: AsyncSession) -> TestData:
    """创建测试所需的基础数据"""
    try:
        # 1. 创建工作流定义
        workflow_def = WorkflowDefinition(
            name='测试工作流', description='用于测试的工作流定义', version=1, is_latest=True
        )
        db.add(workflow_def)
        await db.flush()
        workflow_def_id = workflow_def.id

        # 2. 创建工作流实例
        workflow_instance = WorkflowInstance(
            workflow_definition_id=workflow_def_id,
            status=WorkflowStatus.active,
            start_time=datetime.now(),
            created_by='test_user',
            project_id='TEST-001',
        )
        db.add(workflow_instance)
        await db.flush()
        workflow_instance_id = workflow_instance.id

        # 3. 创建节点定义和实例
        test_nodes = []
        for i, (name, duration) in enumerate(
            [('任务1', 30), ('任务2', 60), ('任务3', 120)]
        ):
            # 创建节点定义
            node_def = NodeDefinition(
                workflow_definition_id=workflow_def_id,
                name=name,
                type=NodeType.process,
                expected_duration=duration,
            )
            db.add(node_def)
            await db.flush()
            node_def_id = node_def.id

            # 创建节点实例
            node_instance = NodeInstance(
                workflow_instance_id=workflow_instance_id,
                node_definition_id=node_def_id,
                status=NodeStatus.active,
                start_time=datetime.now() - timedelta(minutes=duration - 10),
                assigned_to=f'user_{i}',
            )
            db.add(node_instance)
            await db.flush()

            test_nodes.append(
                TestNode(
                    id=node_instance.id,
                    definition_id=node_def_id,
                    workflow_instance_id=workflow_instance_id,
                    name=name,
                    assigned_to=f'user_{i}',
                )
            )

        return TestData(
            workflow_definition_id=workflow_def_id,
            workflow_instance_id=workflow_instance_id,
            nodes=test_nodes,
        )

    except Exception as e:
        await db.rollback()
        raise e


async def test_notification_management():
    """测试通知管理功能"""
    async with AsyncSession(engine) as db:
        try:
            print('开始通知管理测试...')

            # 1. 创建测试数据
            test_data = await create_test_data(db)
            await db.commit()
            print('基础数据创建完成')

            # 2. 测试获取即将到期任务
            due_soon = await notification.get_tasks_due_soon(db)
            print(f'\n发现 {len(due_soon)} 个即将到期的任务')
            for task in due_soon:
                print(f'- 任务: {task.name}')

            # 3. 测试获取逾期任务
            overdue = await notification.get_overdue_tasks(db)
            print(f'\n发现 {len(overdue)} 个逾期任务')
            for task in overdue:
                print(f'- 任务: {task.name}')

            # 4. 测试发送任务通知
            print('\n开始发送任务通知...')
            for node in test_data.nodes:
                result = await notification.send_task_notification(
                    db, node.id, 'status_update'
                )
                if result:
                    print(f'- 已发送通知至 {node.name}')
                    print(f"  消息: {result['message']}")

            # 5. 测试批量通知
            notified_overdue = await notification.notify_overdue_tasks(db)
            print(f'\n已通知 {notified_overdue} 个逾期任务')

            notified_soon = await notification.notify_tasks_due_soon(db)
            print(f'已通知 {notified_soon} 个即将到期任务')

            print('\n所有通知管理测试完成')

        except Exception as e:
            await db.rollback()
            print(f'测试过程中发生错误: {str(e)}')
            raise


async def run_test():
    """运行测试"""
    try:
        await create_tables()
        await test_notification_management()
    finally:
        await engine.dispose()


if __name__ == '__main__':
    asyncio.run(run_test())
