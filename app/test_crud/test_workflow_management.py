# test_workflow_management.py
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from sqlalchemy import select, func
from typing import Dict

from database.base import engine, create_tables
from database.models import (
    WorkflowDefinition,
    WorkflowInstance,
    NodeDefinition,
    EdgeDefinition,
    WorkflowStatus,
    NodeType,
    EdgeCondition,
    InputLogicType,
    NodeInstance,
    EdgeInstance,
)
from database.crud.workflow_management import (
    workflow_instance,
    workflow_definition,
)


async def create_test_workflow_definition(db: AsyncSession) -> Dict[str, int]:
    """创建完整的测试工作流定义"""
    try:
        # 1. 创建工作流定义
        workflow_def = WorkflowDefinition(
            name='测试工作流', description='用于测试的工作流定义', version=1, is_latest=True
        )
        db.add(workflow_def)
        await db.flush()

        # 显式查询获取 workflow_id
        result = await db.execute(
            select(WorkflowDefinition.id).filter(
                WorkflowDefinition.name == '测试工作流'
            )
        )
        workflow_id = result.scalar_one()

        # 2. 创建节点定义
        node_ids = {}
        node_types = [
            ('开始节点', NodeType.start),
            ('处理节点1', NodeType.process),
            ('处理节点2', NodeType.process),
            ('决策节点', NodeType.decision),
            ('结束节点', NodeType.end),
        ]

        for name, node_type in node_types:
            node = NodeDefinition(
                workflow_definition_id=workflow_id,
                name=name,
                type=node_type,
                input_logic=InputLogicType.AND,
                expected_duration=60
                if node_type == NodeType.process
                else None,
            )
            db.add(node)
            await db.flush()

            # 显式查询获取 node_id
            result = await db.execute(
                select(NodeDefinition.id).filter(
                    NodeDefinition.workflow_definition_id == workflow_id,
                    NodeDefinition.name == name,
                )
            )
            node_ids[name] = result.scalar_one()

        # 3. 创建边定义
        edges = [
            ('开始节点', '处理节点1', None),
            ('处理节点1', '处理节点2', None),
            ('处理节点2', '决策节点', None),
            ('决策节点', '结束节点', EdgeCondition.yes),
        ]

        for from_name, to_name, condition in edges:
            edge = EdgeDefinition(
                workflow_definition_id=workflow_id,
                from_node_id=node_ids[from_name],
                to_node_id=node_ids[to_name],
                condition=condition,
            )
            db.add(edge)

        await db.commit()
        return {'workflow_id': workflow_id, 'node_ids': node_ids}

    except Exception as e:
        await db.rollback()
        raise e


async def test_workflow_management():
    """测试工作流管理功能"""
    async with AsyncSession(engine) as db:
        try:
            print('开始工作流管理测试...')

            # 1. 创建完整的工作流定义
            test_data = await create_test_workflow_definition(db)
            print('工作流定义创建成功')

            # 等待数据库操作完成
            await db.flush()

            # 2. 获取工作流定义详情
            workflow_def = await workflow_definition.get_workflow(
                db, test_data['workflow_id']
            )
            print(f'\n获取到工作流定义: {workflow_def.name}')
            print(f'- 节点数量: {len(workflow_def.nodes)}')
            print(f'- 边数量: {len(workflow_def.edges)}')

            # 确保数据已提交
            await db.commit()

            # 3. 测试初始化完整工作流
            async with AsyncSession(engine) as new_db:
                try:
                    # 确保能找到工作流定义
                    definition = await new_db.execute(
                        select(WorkflowDefinition).filter(
                            WorkflowDefinition.name == '测试工作流',
                            WorkflowDefinition.is_latest == True,
                        )
                    )
                    if not definition.scalar_one_or_none():
                        raise ValueError('Cannot find workflow definition')

                    _ = await workflow_instance.initialize_complete_workflow(
                        new_db,
                        workflow_name='测试工作流',
                        project_id='TEST-002',
                        creator='test_user',
                        extra_variables={
                            'main': [
                                ('test_var1', 'value1'),
                                ('test_var2', 'value2'),
                            ]
                        },
                    )

                    # 获取实例ID
                    result = await new_db.execute(
                        select(WorkflowInstance.id).filter(
                            WorkflowInstance.project_id == 'TEST-002'
                        )
                    )
                    instance_id = result.scalar_one()

                    print('\n完整工作流初始化结果:')
                    print(f'- 主实例ID: {instance_id}')

                    # 获取节点和边数量
                    nodes_result = await new_db.execute(
                        select(func.count(NodeInstance.id)).filter(
                            NodeInstance.workflow_instance_id == instance_id
                        )
                    )
                    edges_result = await new_db.execute(
                        select(func.count(EdgeInstance.id)).filter(
                            EdgeInstance.workflow_instance_id == instance_id
                        )
                    )

                    nodes_count = nodes_result.scalar_one()
                    edges_count = edges_result.scalar_one()

                    print(f'- 节点实例数量: {nodes_count}')
                    print(f'- 边实例数量: {edges_count}')

                    await new_db.commit()
                except Exception as e:
                    await new_db.rollback()
                    raise e

            # 4. 测试项目相关功能
            async with AsyncSession(engine) as project_db:
                try:
                    project_workflows = (
                        await workflow_instance.get_project_workflows(
                            project_db, 'TEST-002'
                        )
                    )
                    print(f'\n项目工作流数量: {len(project_workflows)}')

                    project_summary = (
                        await workflow_instance.get_project_summary(
                            project_db, 'TEST-002'
                        )
                    )
                    if project_summary:
                        print('\n项目摘要:')
                        print(f"- 工作流数量: {project_summary['workflow_count']}")
                        print(
                            f"- 首个工作流: {project_summary['first_workflow_date']}"
                        )

                    # 5. 测试状态更新
                    for workflow in project_workflows:
                        result = await db.execute(
                            select(WorkflowInstance.id).filter(
                                WorkflowInstance.project_id == 'TEST-002'
                            )
                        )
                        wf_id = result.scalar_one()

                        success = await workflow_instance.update_workflow_instance_status(
                            project_db, wf_id, WorkflowStatus.completed
                        )
                        print(
                            f"\n工作流 {wf_id} 状态更新: {'成功' if success else '失败'}"
                        )

                    await project_db.commit()
                except Exception as e:
                    await project_db.rollback()
                    raise e

            print('\n所有工作流管理测试完成')

        except Exception as e:
            await db.rollback()
            print(f'测试过程中发生错误: {str(e)}')
            raise


async def run_test():
    """运行测试"""
    try:
        await create_tables()
        await test_workflow_management()
    finally:
        await engine.dispose()


if __name__ == '__main__':
    asyncio.run(run_test())
