import os
import uvicorn

from fastapi import FastAPI


from fastapi.middleware.cors import CORSMiddleware

from contextlib import asynccontextmanager

from database.base import create_tables, engine

import config
from logger import Logger
from routes import (
    workflow_routes,
    # notification_routes,
    # project_routes,
    # change_log_routes,
    task_routes,
    # approval_routes,
    # permission_routes
    gantt_routes,
    # change_log_routes,
    # project_report_routes,
    # external_task_routes,
    machine_type_duration_routes,
    inspection_routes,
)

from services.task_checker import task_checker

# from services.message_processor import message_processor
from services.polling_service import polling_service
from services.archived_erp_service import archived_erp_service

logger = Logger('main', log_level=os.getenv('LOG_LEVEL', 'INFO'))


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 在应用启动时执行的操作
    # 例如，初始化数据库连接
    await create_tables()
    task_checker.start()
    # await message_processor.start_processing()
    await polling_service.start()
    await archived_erp_service.start()

    yield

    # await message_processor.shutdown()
    await polling_service.stop()
    await archived_erp_service.stop()
    task_checker.stop()
    await engine.dispose()


app = FastAPI(
    title='卷绕数据系统工作流',
    description='卷绕数据系统工作流后端',
    version='1.0.0',
    contact={
        'name': 'qinghao.yan',
        'email': '<EMAIL>',
    },
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allow_headers=['*'],
)


logger.debug('CORS middleware configured')


# 包含各个路由

app.include_router(workflow_routes.router, prefix='/api/v1')
app.include_router(task_routes.router, prefix='/api/v1')
# app.include_router(notification_routes.router, prefix='/api/v1')
# app.include_router(external_task_routes.router, prefix='/api/v1')
# app.include_router(change_log_routes.router, prefix='/api/v1')
app.include_router(gantt_routes.router, prefix='/api/v1')
app.include_router(machine_type_duration_routes.router, prefix='/api/v1')
app.include_router(inspection_routes.router, prefix='/api/v1')
# app.include_router(project_report_routes.router, prefix='/api/v1')


@app.get('/')
async def serve_frontend():
    logger.debug('Serving frontend')
    return 'hello world'


# 挂载前端静态文件
# app.mount('/', StaticFiles(directory='./dist'), name='static')
logger.debug('Static files mounted')

if __name__ == '__main__':
    logger.info(f'Starting application on {config.HOST}:{config.PORT}')
    uvicorn.run('main:app', host=config.HOST, port=config.PORT, reload=True)
