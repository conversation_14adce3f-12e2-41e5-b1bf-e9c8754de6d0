from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from database.base import get_db_dependency
from database.crud.simulation_data import SimulationDataCRUD
from schemas.simulation import (
    SimulationDataCreate,
    SimulationDataUpdate,
    SimulationDataResponse,
)
from routes import logger

router = APIRouter(
    prefix='/simulation',
    tags=['仿真数据管理'],
)

# 创建CRUD实例
simulation_crud = SimulationDataCRUD()


@router.get(
    '/data',
    response_model=List[SimulationDataResponse],
    summary='获取所有ERP的仿真数据',
    description='获取所有项目的仿真数据列表，支持分页查询',
)
async def get_all_simulation_data(
    skip: int = Query(0, ge=0, description='跳过的记录数'),
    limit: int = Query(1000, ge=1, le=10000, description='返回的记录数'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取所有ERP的仿真数据
    
    Parameters:
        skip (int): 跳过的记录数，默认为0
        limit (int): 返回的记录数，默认为1000，最大10000
        
    Returns:
        List[SimulationDataResponse]: 仿真数据列表
            - id: 仿真数据ID
            - node_instance_id: 节点实例ID
            - project_id: 项目ID/ERP
            - simulation_efficiency: 仿真效率(PPM)
            - mechanical_issues: 机械问题个数
            - program_issues: 程序问题个数
            - remarks: 备注信息
            - simulation_3d_responsible: 仿真搭建负责人
            - simulation_3d_start_time: 搭建开始时间
            - simulation_3d_end_time: 搭建结束时间
            - created_by: 创建人
            - created_at: 创建时间
            - updated_at: 更新时间
    
    示例响应:
    ```json
    [
        {
            "id": 1,
            "node_instance_id": 123,
            "project_id": "PRJ-001",
            "simulation_efficiency": "95%",
            "mechanical_issues": "2",
            "program_issues": "1",
            "remarks": "测试完成",
            "simulation_3d_responsible": "张工",
            "simulation_3d_start_time": "2024-01-15T09:00:00",
            "simulation_3d_end_time": "2024-01-15T17:00:00",
            "created_by": "admin",
            "created_at": "2024-01-15T08:00:00",
            "updated_at": "2024-01-15T08:00:00"
        }
    ]
    ```
    """
    try:
        simulation_data_list = await simulation_crud.get_all_simulation_data(
            db, skip=skip, limit=limit
        )
        
        return [
            SimulationDataResponse.model_validate(data) 
            for data in simulation_data_list
        ]
        
    except Exception as e:
        logger.error(f'获取所有仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取仿真数据失败: {str(e)}')


@router.get(
    '/data/{erp}',
    response_model=Optional[SimulationDataResponse],
    summary='根据ERP获取仿真数据',
    description='根据项目ERP号获取对应的仿真数据，包括仿真调试和仿真三维搭建相关信息',
)
async def get_simulation_data_by_erp(
    erp: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    根据ERP获取仿真数据
    
    Parameters:
        erp (str): 项目ERP号
        
    Returns:
        Optional[SimulationDataResponse]: 仿真数据，如果不存在则返回null
    """
    try:
        result = await simulation_crud.get_simulation_data_by_erp(db, erp)
        
        if result:
            return SimulationDataResponse.model_validate(result)
        else:
            return SimulationDataResponse.model_validate({
                'id': None,
                'node_instance_id': None,
                'project_id': erp,
                'simulation_efficiency': None,
                'mechanical_issues': None,
                'program_issues': None,
                'remarks': None,
                'simulation_3d_responsible': None,
                'simulation_3d_start_time': None,
                'simulation_3d_end_time': None,
                'created_by': None,
                'created_at': None,
                'updated_at': None,
            })
            
    except Exception as e:
        logger.error(f'获取ERP {erp} 的仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取仿真数据失败: {str(e)}')


@router.post(
    '/data/{erp}',
    response_model=SimulationDataResponse,
    summary='为指定ERP创建仿真数据',
    description='为指定的项目ERP创建仿真数据记录',
)
async def create_simulation_data_for_erp(
    erp: str,
    simulation_data: SimulationDataCreate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    为指定ERP创建仿真数据
    
    Parameters:
        erp (str): 项目ERP号
        simulation_data (SimulationDataCreate): 仿真数据创建模型
            - node_instance_id: 节点实例ID（可选，如果不提供会自动查找）
            - simulation_efficiency: 仿真效率(PPM)
            - mechanical_issues: 机械问题个数
            - program_issues: 程序问题个数
            - remarks: 备注信息
            - simulation_3d_responsible: 仿真搭建负责人
            - simulation_3d_start_time: 搭建开始时间
            - simulation_3d_end_time: 搭建结束时间
            - created_by: 创建人
            
    Returns:
        SimulationDataResponse: 创建的仿真数据
        
    示例请求:
    ```json
    {
        "simulation_efficiency": "95%",
        "mechanical_issues": "2",
        "program_issues": "1",
        "remarks": "仿真调试完成",
        "simulation_3d_responsible": "张工",
        "simulation_3d_start_time": "2024-01-15T09:00:00",
        "simulation_3d_end_time": "2024-01-15T17:00:00",
        "created_by": "admin"
    }
    ```
    """
    try:
        # 设置project_id为ERP
        simulation_data.project_id = erp
        
        created_data = await simulation_crud.create_simulation_data_by_erp(
            db, erp, simulation_data
        )
        
        await db.commit()
        
        return SimulationDataResponse.model_validate(created_data)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        await db.rollback()
        logger.error(f'为ERP {erp} 创建仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'创建仿真数据失败: {str(e)}')


@router.put(
    '/data/{erp}',
    response_model=SimulationDataResponse,
    summary='更新指定ERP的仿真数据',
    description='更新指定项目ERP的仿真数据记录',
)
async def update_simulation_data_for_erp(
    erp: str,
    update_data: SimulationDataUpdate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    更新指定ERP的仿真数据
    
    Parameters:
        erp (str): 项目ERP号
        update_data (SimulationDataUpdate): 仿真数据更新模型
            - simulation_efficiency: 仿真效率(PPM)
            - mechanical_issues: 机械问题个数
            - program_issues: 程序问题个数
            - remarks: 备注信息
            - simulation_3d_responsible: 仿真搭建负责人
            - simulation_3d_start_time: 搭建开始时间
            - simulation_3d_end_time: 搭建结束时间
            
    Returns:
        SimulationDataResponse: 更新后的仿真数据
        
    示例请求:
    ```json
    {
        "simulation_efficiency": "98%",
        "mechanical_issues": "1",
        "program_issues": "0",
        "remarks": "仿真调试优化完成"
    }
    ```
    """
    try:
        updated_data = await simulation_crud.update_simulation_data_by_erp(
            db, erp, update_data
        )
        
        await db.commit()
        
        return SimulationDataResponse.model_validate(updated_data)
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        await db.rollback()
        logger.error(f'更新ERP {erp} 的仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'更新仿真数据失败: {str(e)}')
