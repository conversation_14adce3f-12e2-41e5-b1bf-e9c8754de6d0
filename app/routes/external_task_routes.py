from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select
from datetime import datetime, timezone
import json

from database.base import get_db_dependency
from database.models import ChangeLog, ApprovalRecord
import schemas
from services.message_processor import message_processor

router = APIRouter(
    prefix='/external-tasks',
    tags=['外部任务管理'],
    responses={404: {'description': '任务未找到'}, 500: {'description': '服务器内部错误'}},
)


@router.post(
    '/sync',
    response_model=dict,
    summary='同步外部任务状态',
    description='接收并处理来自外部系统的任务状态更新',
)
async def sync_external_task(
    message: schemas.ExternalTaskMessage,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    同步外部系统的任务状态变更信息

    Parameters:
        message (ExternalTaskMessage): 外部任务消息
            - project_id: 项目ID
            - workflow_id: 工作流ID
            - node_name: 节点名称
            - action: 操作类型（approve/reject/complete/rollback/reassign）
            - operator: 操作人ID
            - operator_name: 操作人名称
            - comments: 操作备注（可选）
            - timestamp: 操作时间戳（可选，默认为当前时间）

    Returns:
        dict: 包含处理结果的响应
            - success: 是否成功
            - message: 处理结果描述
            - request_id: 请求唯一标识

    Raises:
        HTTPException: 当处理失败时抛出500错误

    示例请求:
    ```json
    {
        "project_id": "proj_001",
        "workflow_id": "wf_001",
        "node_name": "approval_node",
        "action": "approve",
        "operator": "user_001",
        "operator_name": "John Doe",
        "comments": "Approved after review"
    }
    ```
    """
    try:
        if not message.timestamp:
            message.timestamp = datetime.now(timezone.utc)

        background_tasks.add_task(message_processor.enqueue_message, message)

        return {
            'success': True,
            'message': 'Task status sync request accepted',
            'request_id': f'{message.workflow_id}_{message.timestamp.timestamp()}',
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f'Failed to process external task message: {str(e)}',
        )


@router.get(
    '/status/{workflow_id}/{node_name}',
    response_model=dict,
    summary='获取任务状态',
    description='获取指定工作流节点的当前状态信息',
)
async def get_task_status(
    workflow_id: str,
    node_name: str,
    project_id: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取指定任务节点的当前详细状态

    Parameters:
        workflow_id (str): 工作流ID
        node_name (str): 节点名称
        project_id (str): 项目ID

    Returns:
        dict: 任务状态信息
            - node_id: 节点实例ID
            - status: 当前状态
            - assigned_to: 任务分配人
            - start_time: 开始时间
            - end_time: 结束时间
            - actual_duration: 实际持续时间

    Raises:
        HTTPException:
            - 404: 任务未找到
            - 500: 查询过程中发生错误

    示例响应:
    ```json
    {
        "node_id": 123,
        "status": "RUNNING",
        "assigned_to": "user_001",
        "start_time": "2024-01-01T12:00:00",
        "end_time": null,
        "actual_duration": 3600
    }
    ```
    """
    try:
        message = schemas.ExternalTaskMessage(
            project_id=project_id,
            workflow_id=workflow_id,
            workflow_name='',
            node_name=node_name,
            action=schemas.ExternalTaskAction.COMPLETE,
            operator='system',
            operator_name='System',
        )

        node = await message_processor._find_node_instance(db, message)
        if not node:
            raise HTTPException(status_code=404, detail='Task not found')

        return {
            'node_id': node.id,
            'status': node.status.value,
            'assigned_to': node.assigned_to,
            'start_time': node.start_time,
            'end_time': node.end_time,
            'actual_duration': node.actual_duration,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f'Failed to get task status: {str(e)}'
        )


@router.get(
    '/history/{workflow_id}/{node_name}',
    response_model=dict,
    summary='获取任务历史',
    description='获取指定工作流节点的所有历史操作记录，包括状态变更和审批记录',
)
async def get_task_history(
    workflow_id: str,
    node_name: str,
    project_id: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取指定任务节点的所有历史操作记录

    Parameters:
        workflow_id (str): 工作流ID
        node_name (str): 节点名称
        project_id (str): 项目ID

    Returns:
        dict: 任务历史记录
            - node_id: 节点实例ID
            - changes: 变更历史列表
                - timestamp: 变更时间
                - operator: 操作人
                - type: 变更类型
                - details: 变更详情
            - approvals: 审批记录列表
                - timestamp: 审批时间
                - operator: 审批人
                - status: 审批状态
                - comments: 审批意见

    Raises:
        HTTPException:
            - 404: 任务未找到
            - 500: 查询过程中发生错误

    示例响应:
    ```json
    {
        "node_id": 123,
        "changes": [
            {
                "timestamp": "2024-01-01T12:00:00",
                "operator": "user_001",
                "type": "STATUS_CHANGE",
                "details": {
                    "from": "PENDING",
                    "to": "RUNNING"
                }
            }
        ],
        "approvals": [
            {
                "timestamp": "2024-01-01T12:30:00",
                "operator": "user_002",
                "status": "APPROVED",
                "comments": "Reviewed and approved"
            }
        ]
    }
    ```
    """
    try:
        message = schemas.ExternalTaskMessage(
            project_id=project_id,
            workflow_id=workflow_id,
            workflow_name='',
            node_name=node_name,
            action=schemas.ExternalTaskAction.COMPLETE,
            operator='system',
            operator_name='System',
        )

        node = await message_processor._find_node_instance(db, message)
        if not node:
            raise HTTPException(status_code=404, detail='Task not found')

        # 获取变更日志
        result = await db.execute(
            select(ChangeLog)
            .filter(
                ChangeLog.workflow_instance_id == node.workflow_instance_id
            )
            .order_by(ChangeLog.created_at.desc())
        )
        logs = result.scalars().all()

        # 获取审批记录
        result = await db.execute(
            select(ApprovalRecord)
            .filter(ApprovalRecord.node_instance_id == node.id)
            .order_by(ApprovalRecord.created_at.desc())
        )
        approvals = result.scalars().all()

        return {
            'node_id': node.id,
            'changes': [
                {
                    'timestamp': log.created_at,
                    'operator': log.changed_by,
                    'type': log.change_type,
                    'details': json.loads(log.change_details),
                }
                for log in logs
            ],
            'approvals': [
                {
                    'timestamp': record.created_at,
                    'operator': record.approved_by,
                    'status': record.approval_status,
                    'comments': record.comments,
                }
                for record in approvals
            ],
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f'Failed to get task history: {str(e)}'
        )
