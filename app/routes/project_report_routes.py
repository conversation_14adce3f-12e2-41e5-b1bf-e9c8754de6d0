from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from datetime import datetime

import schemas
from database.base import get_db_dependency
from database.crud.project_report_service import ProjectReportService

router = APIRouter(
    prefix='/project-report',
    tags=['项目报表'],
    responses={404: {'description': '项目未找到'}, 500: {'description': '服务器内部错误'}},
)


@router.get(
    '/tasks',
    summary='获取项目任务报表',
    description='获取指定项目在特定时间范围内的任务统计报表',
    response_model=dict,
)
async def get_projects_task_report(
    project_ids: Optional[List[str]] = Query(
        None, description='项目ID列表', example=['proj_001', 'proj_002']
    ),
    start_date: Optional[datetime] = Query(
        None, description='开始日期', example='2024-01-01T00:00:00Z'
    ),
    end_date: Optional[datetime] = Query(
        None, description='结束日期', example='2024-12-31T23:59:59Z'
    ),
    task_status: Optional[schemas.NodeStatus] = Query(
        None, description='任务状态过滤', example='active'
    ),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取项目任务详细报表数据

    Parameters:
        project_ids (List[str], optional): 要查询的项目ID列表，不提供则返回所有项目数据
        start_date (datetime, optional): 统计起始日期
        end_date (datetime, optional): 统计结束日期

    Returns:
        dict: 项目任务报表数据
            - total_projects: 项目总数
            - total_tasks: 任务总数
            - avg_completion_rate: 平均完成率
            - projects: 项目详情列表
                - project_id: 项目ID
                - project_name: 项目名称
                - tasks: 任务列表
                    - task_id: 任务ID
                    - title: 任务标题
                    - status: 任务状态
                    - progress: 完成进度
                    - start_date: 开始时间
                    - due_date: 截止时间
                    - actual_completion_date: 实际完成时间

    示例响应:
    ```json
    {
        "total_projects": 2,
        "total_tasks": 50,
        "avg_completion_rate": 75.5,
        "projects": [
            {
                "project_id": "proj_001",
                "project_name": "产品开发项目",
                "tasks": [
                    {
                        "task_id": "task_001",
                        "title": "需求分析",
                        "status": "COMPLETED",
                        "progress": 100,
                        "start_date": "2024-01-01T00:00:00Z",
                        "due_date": "2024-01-15T00:00:00Z",
                        "actual_completion_date": "2024-01-14T18:00:00Z"
                    }
                ]
            }
        ]
    }
    ```
    """
    try:
        service = ProjectReportService(db)
        report = await service.get_projects_task_report(
            project_ids=project_ids,
            start_date=start_date,
            end_date=end_date,
            task_status=task_status,
        )
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'获取项目任务报表失败: {str(e)}')


@router.get(
    '/trends/{project_id}',
    summary='获取项目任务趋势',
    description='获取指定项目的任务完成趋势数据',
    response_model=dict,
)
async def get_project_task_trends(
    project_id: str,
    days: int = Query(
        30, ge=1, le=365, description='统计天数（1-365天）', example=30
    ),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取项目任务趋势统计数据

    Parameters:
        project_id (str): 项目ID
        days (int): 统计天数，范围1-365天，默认30天

    Returns:
        dict: 任务趋势数据
            - project_id: 项目ID
            - project_name: 项目名称
            - date_range: 统计日期范围
            - daily_stats: 每日统计数据列表
                - date: 日期
                - new_tasks: 新增任务数
                - completed_tasks: 完成任务数
                - active_tasks: 活跃任务数
                - delayed_tasks: 延期任务数
            - trend_analysis: 趋势分析
                - completion_trend: 完成率趋势
                - delay_trend: 延期率趋势

    示例响应:
    ```json
    {
        "project_id": "proj_001",
        "project_name": "产品开发项目",
        "date_range": {
            "start": "2024-01-01",
            "end": "2024-01-30"
        },
        "daily_stats": [
            {
                "date": "2024-01-01",
                "new_tasks": 5,
                "completed_tasks": 3,
                "active_tasks": 10,
                "delayed_tasks": 1
            }
        ],
        "trend_analysis": {
            "completion_trend": "上升",
            "delay_trend": "下降"
        }
    }
    ```
    """
    try:
        service = ProjectReportService(db)
        trends = await service.get_project_task_trends(
            project_id=project_id, days=days
        )
        return trends
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'获取项目任务趋势失败: {str(e)}')


@router.get(
    '/summary',
    summary='获取项目汇总信息',
    description='获取所有项目的简要统计信息',
    response_model=dict,
)
async def get_projects_summary(
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取所有项目的汇总统计信息

    Returns:
        dict: 项目汇总数据
            - total_projects: 项目总数
            - total_tasks: 任务总数
            - avg_completion_rate: 平均完成率
            - projects: 项目统计列表
                - project_id: 项目ID
                - total_tasks: 任务总数
                - completion_rate: 完成率
                - delay_rate: 延期率
                - avg_completion_time: 平均完成时间

    示例响应:
    ```json
    {
        "total_projects": 10,
        "total_tasks": 500,
        "avg_completion_rate": 78.5,
        "projects": [
            {
                "project_id": "proj_001",
                "total_tasks": 50,
                "completion_rate": 85.0,
                "delay_rate": 5.0,
                "avg_completion_time": 259200
            }
        ]
    }
    ```
    """
    try:
        service = ProjectReportService(db)
        report = await service.get_projects_task_report()

        summary = {
            'total_projects': report['total_projects'],
            'total_tasks': report['total_tasks'],
            'avg_completion_rate': report['avg_completion_rate'],
            'projects': [
                {
                    'project_id': p['project_id'],
                    'total_tasks': p['total_tasks'],
                    'completion_rate': p['completion_rate'],
                    'delay_rate': p['delay_rate'],
                    'avg_completion_time': p['avg_completion_time'],
                }
                for p in report['projects']
            ],
        }
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'获取项目汇总信息失败: {str(e)}')
