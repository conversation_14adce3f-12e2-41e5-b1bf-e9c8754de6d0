from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from database.base import get_db_dependency
from database.crud.notification_management import notification
import schemas
from typing import List

router = APIRouter(
    prefix='/notifications',
    tags=['通知管理'],
    responses={404: {'description': '任务未找到'}, 500: {'description': '服务器内部错误'}},
)


@router.post(
    '/task/{task_id}/notify',
    summary='发送任务通知',
    description='向指定任务的相关人员发送通知（如启动提醒、超时预警等）',
    response_model=dict,
)
async def notify_task(
    task_id: str,
    notification_type: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    发送指定类型的任务通知

    Parameters:
        task_id (str): 任务ID
        notification_type (str): 通知类型，可选值：
            - start: 任务启动提醒
            - overdue: 任务超时预警
            - complete: 任务完成通知
            - update: 任务更新提醒

    Returns:
        dict: 通知发送结果
            - success: 是否发送成功
            - message: 结果描述
            - notification_id: 通知记录ID

    示例响应:
    ```json
    {
        "success": true,
        "message": "通知发送成功",
        "notification_id": "notify_001"
    }
    ```
    """
    result = await notification.send_task_notification(
        db, task_id, notification_type
    )
    if not result:
        raise HTTPException(status_code=404, detail='Task not found')
    return result


@router.get(
    '/tasks/overdue',
    response_model=List[schemas.TaskNotification],
    summary='获取逾期任务',
    description='获取所有已经逾期的任务列表',
)
async def get_overdue_tasks(db: AsyncSession = Depends(get_db_dependency)):
    """
    查询所有已逾期的任务

    Returns:
        List[TaskNotification]: 逾期任务列表
            - task_id: 任务ID
            - title: 任务标题
            - due_date: 截止日期
            - overdue_days: 逾期天数
            - assignee: 负责人

    示例响应:
    ```json
    [
        {
            "task_id": "task_001",
            "title": "系统测试",
            "due_date": "2024-01-01T00:00:00Z",
            "overdue_days": 5,
            "assignee": "张三"
        }
    ]
    ```
    """
    return await notification.get_overdue_tasks(db)


@router.get(
    '/tasks/due-soon',
    response_model=List[schemas.TaskNotification],
    summary='获取即将到期任务',
    description='获取指定时间内即将到期的任务列表',
)
async def get_tasks_due_soon(
    hours: int = 1, db: AsyncSession = Depends(get_db_dependency)
):
    """
    查询即将到期的任务

    Parameters:
        hours (int): 未来多少小时内到期，默认为1小时

    Returns:
        List[TaskNotification]: 即将到期的任务列表
            - task_id: 任务ID
            - title: 任务标题
            - due_date: 截止日期
            - remaining_hours: 剩余小时数
            - assignee: 负责人

    示例响应:
    ```json
    [
        {
            "task_id": "task_002",
            "title": "代码审查",
            "due_date": "2024-01-10T15:00:00Z",
            "remaining_hours": 0.5,
            "assignee": "李四"
        }
    ]
    ```
    """
    return await notification.get_tasks_due_soon(db, hours)


@router.post(
    '/task/{task_id}/send-wechat',
    summary='发送企业微信通知',
    description='向指定任务的相关人员发送企业微信通知',
    response_model=dict,
)
async def send_wechat_notification(
    task_id: str, message: str, db: AsyncSession = Depends(get_db_dependency)
):
    """
    手动发送企业微信通知

    Parameters:
        task_id (str): 任务ID
        message (str): 通知内容

    Returns:
        dict: 通知发送结果
            - status: 发送状态
            - message: 结果描述

    示例请求:
    ```json
    {
        "message": "请尽快完成任务评审"
    }
    ```

    示例响应:
    ```json
    {
        "status": "success",
        "message": "Notification sent successfully"
    }
    ```
    """
    success = await notification.send_wechat_notification(db, task_id, message)
    if not success:
        raise HTTPException(
            status_code=404, detail='Task not found or notification failed'
        )
    return {'status': 'success', 'message': 'Notification sent successfully'}


@router.post(
    '/tasks/notify-overdue',
    summary='通知所有逾期任务',
    description='向所有逾期任务的负责人发送通知提醒',
    response_model=dict,
)
async def notify_overdue_tasks(db: AsyncSession = Depends(get_db_dependency)):
    """
    批量通知所有逾期任务

    Returns:
        dict: 通知发送结果
            - status: 发送状态
            - message: 结果描述，包含发送数量

    示例响应:
    ```json
    {
        "status": "success",
        "message": "Sent notifications for 5 overdue tasks"
    }
    ```
    """
    count = await notification.notify_overdue_tasks(db)
    return {
        'status': 'success',
        'message': f'Sent notifications for {count} overdue tasks',
    }


@router.post(
    '/tasks/notify-due-soon',
    summary='通知即将到期任务',
    description='向即将到期任务的负责人发送预警通知',
    response_model=dict,
)
async def notify_tasks_due_soon(
    hours: int = 1, db: AsyncSession = Depends(get_db_dependency)
):
    """
    批量通知即将到期的任务

    Parameters:
        hours (int): 未来多少小时内到期，默认为1小时

    Returns:
        dict: 通知发送结果
            - status: 发送状态
            - message: 结果描述，包含发送数量

    示例响应:
    ```json
    {
        "status": "success",
        "message": "Sent notifications for 3 tasks due soon"
    }
    ```
    """
    count = await notification.notify_tasks_due_soon(db, hours)
    return {
        'status': 'success',
        'message': f'Sent notifications for {count} tasks due soon',
    }
