from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from database.base import get_db_dependency
import schemas
from schemas.base import DeviceType
from database.crud import workflow_management
from typing import List, Optional

router = APIRouter(
    prefix='/workflows',
    tags=['工作流管理'],
    responses={404: {'description': '资源未找到'}, 500: {'description': '服务器内部错误'}},
)


# @router.post(
#     '/workflow-instance/{instance_id}/copy-assignments',
#     response_model=dict,
#     summary='复制工作流节点分配',
#     description='从源工作流实例复制所有节点的用户分配到目标工作流实例',
# )
# async def copy_workflow_assignments(
#     instance_id: int,
#     source_instance_id: int,
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     从源工作流实例复制节点分配到目标工作流实例

#     Parameters:
#         instance_id (int): 目标工作流实例ID
#         source_instance_id (int): 源工作流实例ID

#     Returns:
#         dict: 复制结果
#             - success: 是否成功
#             - assignments: 复制后的分配信息
#             - message: 结果描述信息

#     Raises:
#         HTTPException:
#             - 404: 源工作流实例或目标工作流实例未找到
#             - 500: 复制过程中发生错误

#     示例请求:
#     ```
#     POST /workflows/workflow-instance/2/copy-assignments?source_instance_id=1
#     ```

#     示例响应:
#     ```json
#     {
#         "success": true,
#         "assignments": {
#             "需求评审": {
#                 "assigned_to": "user1",
#                 "subprocess_assignments": {
#                     "子流程节点1": {
#                         "assigned_to": "user2"
#                     }
#                 }
#             }
#         },
#         "message": "Successfully copied assignments from instance 1 to instance 2"
#     }
#     ```
#     """
#     try:
#         # 验证源实例和目标实例是否存在
#         source_instance = (
#             await workflow_management.workflow_instance.get_with_details(
#                 db, source_instance_id
#             )
#         )
#         if not source_instance:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f'Source workflow instance {source_instance_id} not found',
#             )

#         target_instance = (
#             await workflow_management.workflow_instance.get_with_details(
#                 db, instance_id
#             )
#         )
#         if not target_instance:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f'Target workflow instance {instance_id} not found',
#             )

#         # 初始化服务
#         service = workflow_management.WorkflowInitializationService(db)

#         # 执行复制操作
#         success = await service.copy_assignments_from_instance(
#             source_instance_id, instance_id
#         )

#         if not success:
#             raise HTTPException(
#                 status_code=500,
#                 detail='Failed to copy assignments between instances',
#             )

#         # 获取复制后的分配信息
#         new_assignments = await service.get_node_assignments_recursive(
#             instance_id
#         )

#         await db.commit()

#         return {
#             'success': True,
#             'assignments': new_assignments,
#             'message': f'Successfully copied assignments from instance {source_instance_id} to instance {instance_id}',
#         }

#     except HTTPException:
#         await db.rollback()
#         raise
#     except Exception as e:
#         await db.rollback()
#         raise HTTPException(
#             status_code=500,
#             detail=f'Error copying assignments: {str(e)}',
#         )


# @router.post(
#     '/workflow-definition',
#     response_model=schemas.WorkflowDefinition,
#     summary='创建工作流定义',
#     description='创建新的工作流定义，包括节点、流转规则等',
# )
# async def create_workflow_definition(
#     workflow: schemas.WorkflowDefinitionCreate,
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     创建新的工作流定义

#     Parameters:
#         workflow (WorkflowDefinitionCreate): 工作流定义信息
#             - name: 工作流名称
#             - description: 工作流描述
#             - nodes: 节点列表
#                 - name: 节点名称
#                 - type: 节点类型
#                 - expected_duration: 预期耗时
#                 - need_approval: 是否需要审批
#             - transitions: 流转规则

#     Returns:
#         WorkflowDefinition: 创建的工作流定义详情

#     示例请求:
#     ```json
#     {
#         "name": "产品发布流程",
#         "description": "产品从设计到发布的标准流程",
#         "nodes": [
#             {
#                 "name": "需求评审",
#                 "type": "APPROVAL",
#                 "expected_duration": 24,
#                 "need_approval": true
#             }
#         ],
#         "transitions": [
#             {
#                 "from_node": "需求评审",
#                 "to_node": "开发任务",
#                 "condition": "APPROVED"
#             }
#         ]
#     }
#     ```
#     """
#     return await workflow_management.workflow_definition.create_workflow_definition(
#         db, workflow
#     )


# @router.get(
#     '/workflow-definition/{workflow_id}',
#     response_model=schemas.WorkflowDefinition,
#     summary='获取工作流定义',
#     description='获取指定工作流定义的详细信息',
# )
# async def get_workflow_definition(
#     workflow_id: int, db: AsyncSession = Depends(get_db_dependency)
# ):
#     """
#     获取工作流定义详情

#     Parameters:
#         workflow_id (int): 工作流定义ID

#     Returns:
#         WorkflowDefinition: 工作流定义详情

#     Raises:
#         HTTPException:
#             - 404: 工作流定义未找到
#     """
#     workflow = await workflow_management.workflow_definition.get_workflow(
#         db, workflow_id
#     )
#     if not workflow:
#         raise HTTPException(
#             status_code=404, detail='Workflow definition not found'
#         )
#     return workflow


# @router.get(
#     '/workflow-definitions',
#     response_model=list[schemas.WorkflowDefinitionInfo],
#     summary='获取所有工作流定义',
#     description='获取系统中所有可用的工作流定义列表',
# )
# async def get_workflow_definitions(
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     获取所有工作流定义列表

#     Returns:
#         List[WorkflowDefinition]: 工作流定义列表
#     """
#     return await workflow_management.workflow_definition.get_workflows(db)


@router.post(
    '/workflow-instance',
    response_model=schemas.WorkflowInstance,
    summary='创建工作流实例',
    description='基于工作流定义创建新的工作流实例',
)
async def create_workflow_instance(
    instance: schemas.WorkflowInstanceCreate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    创建工作流实例

    Parameters:
        instance (WorkflowInstanceCreate): 工作流实例创建信息
            - definition_id: 工作流定义ID
            - project_id: 项目ID
            - name: 实例名称（可选）
            - description: 实例描述（可选）
            - variables: 初始变量

    Returns:
        WorkflowInstance: 创建的工作流实例详情

    示例请求:
    ```json
    {
        "definition_id": 1,
        "project_id": "proj_001",
        "name": "卷绕数据管理工作流主流程",
        "description": "Q1季度主要功能发布流程",
        "variables": {
            "priority": "high",
            "expected_release_date": "2024-03-31"
        }
    }
    ```
    """

    try:
        result = await workflow_management.workflow_instance.initialize_complete_workflow(
            db,
            '卷绕数据管理工作流主流程',
            project_id=instance.project_id,
            device_type=instance.device_type,
            creator=instance.created_by,
        )
        return result
    except Exception as e:
        print(f'ERROR - Failed to create workflow instance: {e}')
        raise HTTPException(
            status_code=500,
            detail=f'Failed to create workflow instance: {str(e)}',
        )


@router.get(
    '/workflow-instances',
    response_model=list[schemas.WorkflowInstanceInfo],
    summary='获取工作流实例列表',
    description='获取系统中所有的工作流实例, workflow_definition_id默认传1',
)
async def get_workflow_instances(
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取所有工作流实例列表

    Returns:
        List[WorkflowInstance]: 工作流实例列表
    """
    return await workflow_management.workflow_instance.get_workflow_instances(
        db
    )


@router.get(
    '/workflow-instance/{instance_id}',
    response_model=schemas.WorkflowInstance,
    summary='获取工作流实例详情',
    description='获取指定工作流实例的详细信息，包括节点状态',
)
async def get_workflow_instance(
    instance_id: int, db: AsyncSession = Depends(get_db_dependency)
):
    """
    获取工作流实例详情

    Parameters:
        instance_id (int): 工作流实例ID

    Returns:
        WorkflowInstanceDetail: 工作流实例详细信息
            - id: 实例ID
            - name: 实例名称
            - status: 当前状态
            - start_time: 开始时间
            - end_time: 结束时间
            - nodes: 节点列表
                - id: 节点ID
                - name: 节点名称
                - status: 节点状态
                - assigned_to: 处理人
                - start_time: 开始时间
                - end_time: 结束时间

    Raises:
        HTTPException:
            - 404: 工作流实例未找到
    """
    instance = await workflow_management.workflow_instance.get_with_details(
        db, instance_id
    )
    if not instance:
        raise HTTPException(
            status_code=404, detail='Workflow instance not found'
        )
    return instance


# @router.put(
#     '/workflow-instance/{instance_id}/status',
#     response_model=dict,
#     summary='更新工作流状态',
#     description='更新指定工作流实例的状态',
# )
# async def update_workflow_instance_status(
#     instance_id: int,
#     status: schemas.WorkflowStatus,
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     更新工作流实例状态

#     Parameters:
#         instance_id (int): 工作流实例ID
#         status (WorkflowStatus): 新状态值
#             - PENDING: 待处理
#             - RUNNING: 进行中
#             - COMPLETED: 已完成
#             - FAILED: 失败
#             - CANCELLED: 已取消

#     Returns:
#         dict: 更新结果
#             - message: 更新结果描述

#     Raises:
#         HTTPException:
#             - 404: 工作流实例未找到

#     示例请求:
#     ```json
#     {
#         "status": "RUNNING"
#     }
#     ```
#     """
#     updated = await workflow_management.workflow_instance.update_workflow_instance_status(
#         db, instance_id, status
#     )
#     if not updated:
#         raise HTTPException(
#             status_code=404, detail='Workflow instance not found'
#         )
#     return {'message': 'Workflow instance status updated successfully'}


@router.get(
    '/projects',
    response_model=list,
    summary='获取所有项目',
    description='获取系统中所有项目的基本信息',
)
async def get_all_projects(db: AsyncSession = Depends(get_db_dependency)):
    """
    获取所有项目列表

    Returns:
        List[dict]: 项目列表
            - project_id: 项目ID
            - name: 项目名称
            - workflow_count: 工作流数量

    示例响应:
    ```json
    [
        {
            "project_id": "proj_001",
            "name": "产品升级项目",
            "workflow_count": 5
        }
    ]
    ```
    """
    return await workflow_management.workflow_instance.get_all_projects(db)


# @router.get(
#     '/project/{project_id}/summary',
#     response_model=dict,
#     summary='获取项目概要',
#     description='获取指定项目的统计概要信息',
# )
# async def get_project_summary(
#     project_id: str, db: AsyncSession = Depends(get_db_dependency)
# ):
#     """
#     获取项目概要信息

#     Parameters:
#         project_id (str): 项目ID

#     Returns:
#         dict: 项目概要信息
#             - total_workflows: 工作流总数
#             - active_workflows: 活跃工作流数
#             - completed_workflows: 已完成工作流数
#             - completion_rate: 完成率
#             - avg_duration: 平均完成时间

#     Raises:
#         HTTPException:
#             - 404: 项目未找到

#     示例响应:
#     ```json
#     {
#         "total_workflows": 10,
#         "active_workflows": 3,
#         "completed_workflows": 7,
#         "completion_rate": 70.0,
#         "avg_duration": 72.5
#     }
#     ```
#     """
#     summary = await workflow_management.workflow_instance.get_project_summary(
#         db, project_id
#     )
#     if not summary:
#         raise HTTPException(status_code=404, detail='Project not found')
#     return summary


@router.get(
    '/project/{project_id}/workflows',
    response_model=list[schemas.WorkflowInstanceInfo],
    summary='获取项目工作流',
    description='获取指定项目下的所有工作流实例',
)
async def get_project_workflows(
    project_id: str, db: AsyncSession = Depends(get_db_dependency)
):
    """
    获取项目下的工作流列表

    Parameters:
        project_id (str): 项目ID

    Returns:
        List[WorkflowInstance]: 工作流实例列表
            - id: 实例ID
            - name: 实例名称
            - status: 状态
            - start_time: 开始时间
            - end_time: 结束时间
            - progress: 完成进度

    示例响应:
    ```json
    [
        {
            "id": 1,
            "name": "Q1产品发布",
            "status": "RUNNING",
            "start_time": "2024-01-01T00:00:00Z",
            "end_time": null,
            "progress": 60
        }
    ]
    ```
    """
    return await workflow_management.workflow_instance.get_project_workflows(
        db, project_id
    )


@router.get(
    '/project/{project_id}/workflow-instance',
    response_model=schemas.WorkflowInstance,
    summary='获取项目工作流',
    description='获取指定项目下的主流工作流实例',
)
async def get_project_workflow_instance(
    project_id: str, db: AsyncSession = Depends(get_db_dependency)
):
    """
    获取项目下的工作流列表

    Parameters:
        project_id (str): 项目ID

    Returns:
        List[WorkflowInstance]: 工作流实例列表
            - id: 实例ID
            - name: 实例名称
            - status: 状态
            - start_time: 开始时间
            - end_time: 结束时间
            - progress: 完成进度

    示例响应:
    ```json
    [
        {
            "id": 1,
            "name": "Q1产品发布",
            "status": "RUNNING",
            "start_time": "2024-01-01T00:00:00Z",
            "end_time": null,
            "progress": 60
        }
    ]
    ```
    """
    project_workflows = (
        await workflow_management.workflow_instance.get_project_workflows(
            db, project_id
        )
    )
    instance_id = None
    for project_workflow in project_workflows:
        if not project_workflow.definition.is_subprocess:
            instance_id = project_workflow.id
            break
    instance = await workflow_management.workflow_instance.get_with_details(
        db, instance_id
    )
    if not instance:
        raise HTTPException(
            status_code=404, detail='Workflow instance not found'
        )
    return instance


@router.get(
    '/project/workflow-instances',
    response_model=list[schemas.WorkflowInstance],
    summary='获取所有项目主工作流',
    description='获取所有项目下的主工作流实例',
)
async def get_project_workflowinstances(
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取项目下的工作流列表

    Parameters:
        project_id (str): 项目ID

    Returns:
        List[WorkflowInstance]: 工作流实例列表
            - id: 实例ID
            - name: 实例名称
            - status: 状态
            - start_time: 开始时间
            - end_time: 结束时间
            - progress: 完成进度

    示例响应:
    ```json
    [
        {
            "id": 1,
            "name": "Q1产品发布",
            "status": "RUNNING",
            "start_time": "2024-01-01T00:00:00Z",
            "end_time": null,
            "progress": 60
        }
    ]
    ```
    """
    projects = await workflow_management.workflow_instance.get_all_projects(db)
    project_workflow_instances = []
    for project in projects:
        if len(project) != 5:
            continue
        project_workflows = (
            await workflow_management.workflow_instance.get_project_workflows(
                db, project
            )
        )
        instance_id = None
        for project_workflow in project_workflows:
            if not project_workflow.definition.is_subprocess:
                instance_id = project_workflow.id
                break
        instance = (
            await workflow_management.workflow_instance.get_with_details(
                db, instance_id
            )
        )
        project_workflow_instances.append(instance)
    return project_workflow_instances


@router.get(
    '/project/{project_id}/workflow-instances',
    response_model=List[schemas.WorkflowInstance],
    summary='获取项目工作流',
    description='获取指定项目下所有工作流实例',
)
async def get_project_workflow_instances(
    project_id: str,
    device_type: Optional[DeviceType] = None,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取项目下的工作流列表

    Parameters:
        project_id (str): 项目ID

    Returns:
        List[WorkflowInstance]: 工作流实例列表
            - id: 实例ID
            - name: 实例名称
            - status: 状态
            - start_time: 开始时间
            - end_time: 结束时间
            - progress: 完成进度

    示例响应:
    ```json
    [
        {
            "id": 1,
            "name": "Q1产品发布",
            "status": "RUNNING",
            "start_time": "2024-01-01T00:00:00Z",
            "end_time": null,
            "progress": 60
        }
    ]
    ```
    """
    project_workflows = (
        await workflow_management.workflow_instance.get_project_workflows(
            db, project_id, device_type
        )
    )
    instances = []
    for project_workflow in project_workflows:
        instance = (
            await workflow_management.workflow_instance.get_with_details(
                db, project_workflow.id
            )
        )
        instances.append(instance)
    if not instances:
        raise HTTPException(
            status_code=404, detail='Workflow instance not found'
        )
    return instances
