from fastapi import APIRouter, Depends, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from database.base import get_db_dependency
import schemas
from database.crud import change_log_management

router = APIRouter(
    prefix='/changelog',
    tags=['变更日志'],
    responses={404: {'description': '未找到相关资源'}},
)


@router.post(
    '/change-log',
    response_model=schemas.ChangeLog,
    status_code=status.HTTP_201_CREATED,
    summary='创建变更日志',
    description='记录工作流实例的变更信息，包括状态变更、配置修改等',
)
async def create_change_log(
    change_log: schemas.ChangeLogCreate,
    db: AsyncSession = Depends(get_db_dependency),
) -> schemas.ChangeLog:
    """
    创建新的变更日志记录，用于追踪工作流实例的修改历史

    参数:
        change_log: 变更日志信息
            - **workflow_instance_id**: 工作流实例ID
            - **change_type**: 变更类型
            - **description**: 变更描述
            - **operator_id**: 操作人ID
            - **before_value**: 变更前的值（可选）
            - **after_value**: 变更后的值（可选）

    返回值:
        schemas.ChangeLog: 创建成功的变更日志记录

    示例请求体:
    ```json
    {
        "workflow_instance_id": 1,
        "change_type": "STATUS_CHANGE",
        "description": "工作流状态从 PENDING 变更为 RUNNING",
        "operator_id": 100,
        "before_value": "PENDING",
        "after_value": "RUNNING"
    }
    ```
    """
    return await change_log_management.change_log.create_change_log(
        db, change_log
    )


@router.get(
    '/workflow-instance/{instance_id}/change-logs',
    response_model=list[schemas.ChangeLog],
    summary='获取工作流实例的变更历史',
    description='获取指定工作流实例的所有变更日志记录，按时间倒序排列',
)
async def get_workflow_change_logs(
    instance_id: int = Path(
        ..., description='工作流实例ID', gt=0, example=1, title='工作流实例标识'
    ),
    db: AsyncSession = Depends(get_db_dependency),
) -> list[schemas.ChangeLog]:
    """
    获取指定工作流实例的所有变更历史记录

    参数:
        instance_id (int): 工作流实例的唯一标识ID，必须大于0

    返回值:
        List[schemas.ChangeLog]: 该工作流实例的所有变更日志列表，按创建时间倒序排序

    示例响应:
    ```json
    [
        {
            "id": 1,
            "workflow_instance_id": 1,
            "change_type": "STATUS_CHANGE",
            "description": "工作流状态从 PENDING 变更为 RUNNING",
            "operator_id": 100,
            "before_value": "PENDING",
            "after_value": "RUNNING",
            "created_at": "2024-01-01T12:00:00"
        },
        {
            "id": 2,
            "workflow_instance_id": 1,
            "change_type": "CONFIG_UPDATE",
            "description": "更新了工作流配置参数",
            "operator_id": 101,
            "before_value": "{\"max_retries\": 3}",
            "after_value": "{\"max_retries\": 5}",
            "created_at": "2024-01-01T12:30:00"
        }
    ]
    ```
    """
    change_logs = await change_log_management.change_log.get_workflow_changes(
        db, instance_id
    )
    return change_logs
