from fastapi import APIRouter, Depends, Path, Query
from sqlalchemy.ext.asyncio import AsyncSession
from database.base import get_db_dependency
import schemas
from database.crud import machine_duration_management
from typing import List
from database.models import DeviceType


HOURS_PER_DAY = 10
MINUTES_PER_HOUR = 60


def days_to_minutes(days: float) -> int:
    """Convert days to minutes (1 day = 10 hours)"""
    return int(days * HOURS_PER_DAY * MINUTES_PER_HOUR)


def minutes_to_days(minutes: int) -> float:
    """Convert minutes to days (1 day = 10 hours)"""
    return round(minutes / (HOURS_PER_DAY * MINUTES_PER_HOUR), 2)


router = APIRouter(
    prefix='/machine-duration',
    tags=['机型工时配置'],
    responses={404: {'description': '未找到相关资源'}},
)


@router.get(
    '/{machine_type}',
    response_model=List[schemas.MachineTypeDuration],
    summary='获取机型工时配置',
    description='获取指定机型的所有节点工时配置，如果某节点未配置则自动创建默认配置',
)
async def get_machine_durations(
    user_id: str = Query(..., description='用户ID', example='1202490'),
    machine_type: str = Path(..., description='机型', example='EV普通卷绕机'),
    device_type: DeviceType = Query(
        DeviceType.main_machine,
        description='设备类型',
    ),
    db: AsyncSession = Depends(get_db_dependency),
) -> List[schemas.MachineTypeDuration]:
    """
    获取指定机型的所有节点工时配置

    参数:
        machine_type (str): 机型标识
        device_type (DeviceType): 设备类型

    返回值:
        List[schemas.MachineTypeDuration]: 该机型的所有节点工时配置列表，
        未配置的节点会自动创建并使用节点默认工时

    示例响应:
    ```json
    [
        {
            "id": 1,
            "node_definition_id": 1,
            "machine_type": "TYPE-A",
            "device_type": "main_machine",
            "expected_duration": 120,
            "created_by": "system",
            "created_at": "2024-01-01T12:00:00",
            "updated_at": "2024-01-01T12:00:00"
        }
    ]
    ```
    """
    db_results = await machine_duration_management.machine_duration.get_multiple_by_machine_type(
        db, machine_type, device_type, user_id
    )
    # Convert minutes to days in response
    return [
        schemas.MachineTypeDuration(
            id=duration.id,
            node_name=duration.node_definition.name,
            node_definition_id=duration.node_definition_id,
            machine_type=duration.machine_type,
            device_type=duration.device_type,
            expected_duration=minutes_to_days(duration.expected_duration)
            if duration.expected_duration
            else None,
            created_by=duration.created_by,
            created_at=duration.created_at,
            updated_at=duration.updated_at,
        )
        for duration in db_results
    ]


@router.put(
    '/{machine_type}/batch',
    summary='批量更新机型工时配置',
    description='批量更新指定机型的节点工时配置',
)
async def update_machine_durations(
    durations: List[schemas.MachineTypeDurationUpdate],
    user_id: str = Query(..., description='用户ID', example='1202490'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    批量更新指定机型的工时配置

    参数:
        machine_type (str): 机型标识
        durations: 工时配置列表
            - **id**: 配置ID
            - **expected_duration**: 预期工时（分钟）
            - **device_type**: 设备类型

    返回值:
        List[schemas.MachineTypeDuration]: 更新后的机型工时配置列表

    示例请求体:
    ```json
    [
        {
            "id": 1,
            "expected_duration": 180,
        },
        {
            "id": 2,
            "expected_duration": 240,
        }
    ]
    ```
    """
    results = []
    for duration in durations:
        # Convert days to minutes before updating
        duration_in_minutes = schemas.MachineTypeDurationUpdate(
            id=duration.id,
            expected_duration=days_to_minutes(duration.expected_duration),
        )

        updated = (
            await machine_duration_management.machine_duration.update_duration(
                db, duration.id, duration_in_minutes, user_id
            )
        )
        if updated:
            results.append(updated)

    return {'success': True}
