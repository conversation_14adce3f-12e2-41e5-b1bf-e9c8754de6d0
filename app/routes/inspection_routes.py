from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from database.base import get_db_dependency
from database.crud.inspection_management import (
    inspection_template,
    project_inspection,
)
from database.crud.info_management import info
from schemas.inspection import (
    InspectionTemplate,
    InspectionTemplateCreate,
    InspectionTemplateUpdate,
    InspectionTemplateListResponse,
    ProjectInspection,
    ProjectInspectionCreate,
    ProjectInspectionUpdate,
    ProjectInspectionListResponse,
    InspectionTypeResponse,
    ProjectInspectionSummary,
    ProjectInspectionSummaryListResponse,
    BatchCreateFromTemplateRequest,
    SyncFromTemplateRequest,
    BatchSyncResponse,
    SyncResult,
)
from routes import logger


router = APIRouter(
    prefix='/inspection',
    tags=['点检表管理'],
    responses={404: {'description': '资源未找到'}, 500: {'description': '服务器内部错误'}},
)


# ==================== 点检表模板管理 ====================


@router.post(
    '/templates',
    response_model=InspectionTemplate,
    summary='创建点检表模板',
    description='创建新的点检表模板',
)
async def create_inspection_template(
    template_in: InspectionTemplateCreate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """创建点检表模板"""
    try:
        # 检查是否已存在相同类型和序号的模板
        existing = await inspection_template.get_by_type_and_sequence(
            db,
            template_type=template_in.type,
            sequence_number=template_in.sequence_number,
        )
        if existing:
            raise HTTPException(
                status_code=400,
                detail=f'类型 {template_in.type} 序号 {template_in.sequence_number} 的模板已存在',
            )

        template = await inspection_template.create(db, obj_in=template_in)
        return template
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'创建点检表模板失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'创建点检表模板失败: {str(e)}')


@router.get(
    '/templates',
    response_model=InspectionTemplateListResponse,
    summary='获取点检表模板列表',
    description='获取点检表模板列表，支持按类型筛选',
)
async def get_inspection_templates(
    template_type: Optional[str] = Query(None, description='模板类型筛选'),
    skip: int = Query(0, ge=0, description='跳过记录数'),
    limit: int = Query(100, ge=1, le=1000, description='返回记录数'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """获取点检表模板列表"""
    try:
        if template_type:
            templates = await inspection_template.get_by_type(
                db, template_type=template_type, skip=skip, limit=limit
            )
            total = await inspection_template.count_by_type(db, template_type)
        else:
            templates = await inspection_template.get_active_templates(
                db, skip=skip, limit=limit
            )
            total = len(templates)  # 简化计算，实际应该用count查询

        return InspectionTemplateListResponse(total=total, templates=templates)
    except Exception as e:
        logger.error(f'获取点检表模板列表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取点检表模板列表失败: {str(e)}')


@router.get(
    '/templates/types',
    response_model=InspectionTypeResponse,
    summary='获取所有模板类型',
    description='获取所有可用的点检表模板类型',
)
async def get_template_types(
    db: AsyncSession = Depends(get_db_dependency),
):
    """获取所有模板类型"""
    try:
        types = await inspection_template.get_all_types(db)
        return InspectionTypeResponse(types=types)
    except Exception as e:
        logger.error(f'获取模板类型失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取模板类型失败: {str(e)}')


@router.get(
    '/templates/{template_id}',
    response_model=InspectionTemplate,
    summary='获取点检表模板详情',
    description='根据ID获取点检表模板详情',
)
async def get_inspection_template(
    template_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """获取点检表模板详情"""
    try:
        template = await inspection_template.get(db, id=template_id)
        if not template:
            raise HTTPException(status_code=404, detail='模板不存在')
        return template
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'获取点检表模板详情失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取点检表模板详情失败: {str(e)}')


@router.put(
    '/templates/{template_id}',
    response_model=InspectionTemplate,
    summary='更新点检表模板',
    description='更新点检表模板信息',
)
async def update_inspection_template(
    template_id: int,
    template_in: InspectionTemplateUpdate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """更新点检表模板"""
    try:
        template = await inspection_template.get(db, id=template_id)
        if not template:
            raise HTTPException(status_code=404, detail='模板不存在')

        # 如果更新类型和序号，检查是否冲突
        if template_in.type and template_in.sequence_number:
            existing = await inspection_template.get_by_type_and_sequence(
                db,
                template_type=template_in.type,
                sequence_number=template_in.sequence_number,
            )
            if existing and existing.id != template_id:
                raise HTTPException(
                    status_code=400,
                    detail=f'类型 {template_in.type} 序号 {template_in.sequence_number} 的模板已存在',
                )

        updated_template = await inspection_template.update(
            db, db_obj=template, obj_in=template_in
        )
        return updated_template
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'更新点检表模板失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'更新点检表模板失败: {str(e)}')


@router.delete(
    '/templates/{template_id}', summary='删除点检表模板', description='删除点检表模板（软删除）'
)
async def delete_inspection_template(
    template_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """删除点检表模板"""
    try:
        template = await inspection_template.get(db, id=template_id)
        if not template:
            raise HTTPException(status_code=404, detail='模板不存在')

        await inspection_template.deactivate(db, id=template_id)
        return {'message': '模板已删除'}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'删除点检表模板失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'删除点检表模板失败: {str(e)}')


# ==================== 项目点检表管理 ====================


@router.post(
    '/projects/{project_id}/inspections',
    response_model=ProjectInspection,
    summary='创建项目点检表',
    description='为项目创建单个点检表',
)
async def create_project_inspection(
    project_id: str,
    inspection_in: ProjectInspectionCreate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """创建项目点检表"""
    try:
        inspection_in.project_id = project_id
        inspection = await project_inspection.create(db, obj_in=inspection_in)
        return inspection
    except Exception as e:
        logger.error(f'创建项目点检表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'创建项目点检表失败: {str(e)}')


@router.get(
    '/projects/{project_id}/inspections',
    response_model=ProjectInspectionListResponse,
    summary='获取项目点检表列表',
    description='获取项目的所有点检表，支持按类型筛选',
)
async def get_project_inspections(
    project_id: str,
    inspection_type: Optional[str] = Query(None, description='点检表类型筛选'),
    skip: int = Query(0, ge=0, description='跳过记录数'),
    limit: int = Query(1000, ge=1, le=1000, description='返回记录数'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """获取项目点检表列表"""
    try:
        if inspection_type:
            inspections = await project_inspection.get_by_project_and_type(
                db, project_id=project_id, inspection_type=inspection_type
            )
        else:
            inspections = await project_inspection.get_by_project(
                db, project_id=project_id, skip=skip, limit=limit
            )

        total = len(inspections)
        return ProjectInspectionListResponse(
            total=total, inspections=inspections
        )
    except Exception as e:
        logger.error(f'获取项目点检表列表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取项目点检表列表失败: {str(e)}')


@router.get(
    '/projects/{project_id}/inspections/summary',
    response_model=ProjectInspectionSummary,
    summary='获取项目点检表汇总',
    description='获取项目点检表的汇总统计信息',
)
async def get_project_inspection_summary(
    project_id: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """获取项目点检表汇总"""
    try:
        summary = await project_inspection.get_project_summary(db, project_id)
        return ProjectInspectionSummary(**summary)
    except Exception as e:
        logger.error(f'获取项目点检表汇总失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取项目点检表汇总失败: {str(e)}')


@router.put(
    '/projects/{project_id}/inspections/{inspection_id}',
    response_model=ProjectInspection,
    summary='更新项目点检表',
    description='更新项目点检表信息',
)
async def update_project_inspection(
    project_id: str,
    inspection_id: int,
    inspection_in: ProjectInspectionUpdate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """更新项目点检表"""
    try:
        inspection = await project_inspection.get(db, id=inspection_id)
        if not inspection or inspection.project_id != project_id:
            raise HTTPException(status_code=404, detail='点检表不存在')

        updated_inspection = await project_inspection.update(
            db, db_obj=inspection, obj_in=inspection_in
        )
        return updated_inspection
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'更新项目点检表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'更新项目点检表失败: {str(e)}')


@router.delete(
    '/projects/{project_id}/inspections/{inspection_id}',
    summary='删除项目点检表',
    description='删除项目点检表',
)
async def delete_project_inspection(
    project_id: str,
    inspection_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """删除项目点检表"""
    try:
        inspection = await project_inspection.get(db, id=inspection_id)
        if not inspection or inspection.project_id != project_id:
            raise HTTPException(status_code=404, detail='点检表不存在')

        await project_inspection.remove(db, id=inspection_id)
        return {'message': '点检表已删除'}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'删除项目点检表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'删除项目点检表失败: {str(e)}')


# ==================== 批量操作和同步功能 ====================


@router.post(
    '/batch/create-from-template',
    response_model=BatchSyncResponse,
    summary='基于模板批量创建项目点检表',
    description='为多个项目基于模板批量创建点检表',
)
async def batch_create_from_template(
    request: BatchCreateFromTemplateRequest,
    db: AsyncSession = Depends(get_db_dependency),
):
    """基于模板批量创建项目点检表"""
    try:
        results = []
        success_count = 0
        failed_count = 0

        # 获取模板
        if request.template_type:
            templates = await inspection_template.get_by_type(
                db, template_type=request.template_type
            )
        else:
            templates = await inspection_template.get_active_templates(db)

        if not templates:
            raise HTTPException(status_code=404, detail='未找到可用的模板')

        for project_id in request.project_ids:
            try:
                created_inspections = (
                    await project_inspection.batch_create_from_templates(
                        db,
                        project_id=project_id,
                        templates=templates,
                        created_by=request.created_by,
                    )
                )

                results.append(
                    SyncResult(
                        project_id=project_id,
                        success=True,
                        message=f'成功创建 {len(created_inspections)} 个点检表',
                        created_count=len(created_inspections),
                    )
                )
                success_count += 1

            except Exception as e:
                results.append(
                    SyncResult(
                        project_id=project_id,
                        success=False,
                        message=f'创建失败: {str(e)}',
                    )
                )
                failed_count += 1
                logger.error(f'为项目 {project_id} 创建点检表失败: {str(e)}')

        return BatchSyncResponse(
            total_projects=len(request.project_ids),
            success_count=success_count,
            failed_count=failed_count,
            results=results,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'批量创建点检表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'批量创建点检表失败: {str(e)}')


@router.post(
    '/sync/from-template',
    response_model=BatchSyncResponse,
    summary='基于模板同步项目点检表',
    description='基于最新模板同步项目点检表条目，支持增量和全量同步',
)
async def sync_from_template(
    request: SyncFromTemplateRequest,
    db: AsyncSession = Depends(get_db_dependency),
):
    """基于模板同步项目点检表"""
    try:
        results = []
        success_count = 0
        failed_count = 0

        # 获取要同步的项目列表
        if request.project_ids:
            project_ids = request.project_ids
        else:
            # 获取所有有工作流的项目
            erp_info = await info.get_erp_info(db)
            project_ids = [item['erp'] for item in erp_info]

        # 获取模板
        if request.template_type:
            templates = await inspection_template.get_by_type(
                db, template_type=request.template_type
            )
        else:
            templates = await inspection_template.get_active_templates(db)

        if not templates:
            raise HTTPException(status_code=404, detail='未找到可用的模板')

        for project_id in project_ids:
            try:
                created_count = 0
                updated_count = 0
                deleted_count = 0

                if request.sync_mode == 'full':
                    # 全量同步：先删除现有的，再重新创建
                    if request.template_type:
                        deleted_count = await project_inspection.delete_by_project_and_type(
                            db,
                            project_id=project_id,
                            inspection_type=request.template_type,
                        )
                    else:
                        deleted_count = (
                            await project_inspection.delete_by_project(
                                db, project_id=project_id
                            )
                        )

                    # 重新创建
                    created_inspections = (
                        await project_inspection.batch_create_from_templates(
                            db, project_id=project_id, templates=templates
                        )
                    )
                    created_count = len(created_inspections)

                else:
                    # 增量同步：只创建不存在的
                    created_inspections = (
                        await project_inspection.batch_create_from_templates(
                            db, project_id=project_id, templates=templates
                        )
                    )
                    created_count = len(created_inspections)

                message = f'同步完成 - 创建: {created_count}'
                if deleted_count > 0:
                    message += f', 删除: {deleted_count}'

                results.append(
                    SyncResult(
                        project_id=project_id,
                        success=True,
                        message=message,
                        created_count=created_count,
                        updated_count=updated_count,
                        deleted_count=deleted_count,
                    )
                )
                success_count += 1

            except Exception as e:
                results.append(
                    SyncResult(
                        project_id=project_id,
                        success=False,
                        message=f'同步失败: {str(e)}',
                    )
                )
                failed_count += 1
                logger.error(f'同步项目 {project_id} 点检表失败: {str(e)}')

        return BatchSyncResponse(
            total_projects=len(project_ids),
            success_count=success_count,
            failed_count=failed_count,
            results=results,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'同步点检表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'同步点检表失败: {str(e)}')


@router.get(
    '/projects/summaries',
    response_model=ProjectInspectionSummaryListResponse,
    summary='获取所有项目点检表汇总',
    description='获取所有项目的点检表汇总统计信息',
)
async def get_all_project_summaries(
    skip: int = Query(0, ge=0, description='跳过记录数'),
    limit: int = Query(100, ge=1, le=1000, description='返回记录数'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """获取所有项目点检表汇总"""
    try:
        # 获取所有有点检表的项目
        erp_info = await info.get_erp_info(db)
        project_ids = [item['erp'] for item in erp_info]

        summaries = []
        for project_id in project_ids[skip : skip + limit]:
            try:
                summary_data = await project_inspection.get_project_summary(
                    db, project_id
                )
                if summary_data['total_count'] > 0:  # 只返回有点检表的项目
                    summaries.append(ProjectInspectionSummary(**summary_data))
            except Exception as e:
                logger.error(f'获取项目 {project_id} 汇总失败: {str(e)}')
                continue

        return ProjectInspectionSummaryListResponse(
            total=len(summaries), summaries=summaries
        )

    except Exception as e:
        logger.error(f'获取项目汇总列表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取项目汇总列表失败: {str(e)}')
