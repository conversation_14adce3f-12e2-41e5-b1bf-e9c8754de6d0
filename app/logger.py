import logging
import sys
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path


class Logger:
    def __init__(
        self, name: str, log_level: str = 'INFO', log_to_file: bool = True
    ):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(log_level.upper())

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        if log_to_file:
            # 确保日志目录存在
            log_dir = Path('logs')
            log_dir.mkdir(exist_ok=True)

            # 文件处理器（每天轮转）
            file_handler = TimedRotatingFileHandler(
                filename=log_dir / f'{name}.log',
                when='midnight',
                interval=1,
                backupCount=30,  # 保留30天的日志
                encoding='utf-8',
            )
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)

    def debug(self, message):
        self.logger.debug(message)

    def info(self, message):
        self.logger.info(message)

    def warning(self, message):
        self.logger.warning(message)

    def error(self, message):
        self.logger.error(message)

    def critical(self, message):
        self.logger.critical(message)
