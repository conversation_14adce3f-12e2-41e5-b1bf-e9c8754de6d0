import os
import sys

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)

# 获取当前脚本的上层目录
parent_dir = os.path.dirname(os.path.dirname(current_script_path))

# 将上层目录添加到 Python 路径中
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

import math
import asyncio
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, time, timedelta
from typing import Dict, List, Set, Tuple
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
)
from schemas import NodeStatus, WorkflowStatus
from database.base import engine
from database.crud.info_management import info
from utils.tools import (
    calculate_column_width,
    get_node_color,
    get_full_node_name,
)
from schemas.base import DeviceType


def normalize_work_time(dt: datetime) -> datetime:
    """
    规范化时间到工作时间范围（8:30-20:30）
    如果时间在非工作时间，则调整到最近的工作时间
    """
    work_start = time(8, 30)
    work_end = time(20, 30)

    # 如果是在工作开始时间之前，调整到当天的工作开始时间
    if dt.time() < work_start:
        return dt.replace(hour=8, minute=30, second=0, microsecond=0)

    # 如果是在工作结束时间之后，调整到当天的工作结束时间
    if dt.time() > work_end:
        return dt.replace(hour=20, minute=30, second=0, microsecond=0)

    return dt


def calculate_work_hours(start: datetime, end: datetime) -> int:
    """
    计算工作时间内的持续小时数
    只计算 8:30-20:30 之间的时间
    """
    if start > end:
        return 0

    # 规范化开始和结束时间到工作时间范围
    start = normalize_work_time(start)
    end = normalize_work_time(end)

    # 如果在同一天
    if start.date() == end.date():
        duration = (end - start).total_seconds() / 3600  # 转换为小时
        return int(duration)

    # 如果跨天，计算每天的工作时间
    total_hours = 0
    current_date = start.date()
    end_date = end.date()

    while current_date <= end_date:
        if current_date == start.date():
            # 第一天：从开始时间到工作结束
            day_end = datetime.combine(current_date, time(20, 30))
            hours = (day_end - start).total_seconds() / 3600
            total_hours += max(0, hours)
        elif current_date == end_date:
            # 最后一天：从工作开始到结束时间
            day_start = datetime.combine(current_date, time(8, 30))
            hours = (end - day_start).total_seconds() / 3600
            total_hours += max(0, hours)
        else:
            # 中间的完整工作日：12小时（8:30-20:30）
            total_hours += 12

        current_date += timedelta(days=1)

    return int(total_hours)


def calculate_cell_position(
    project_start: datetime, target_time: datetime
) -> Tuple[int, int]:
    """
    计算时间点对应的单元格位置
    返回: (天数偏移, 当天小时偏移)
    """
    target_time = normalize_work_time(target_time)

    # 计算日期差
    days_diff = (target_time.date() - project_start.date()).days

    # 计算小时偏移
    target_hours = target_time.hour + target_time.minute / 60
    base_hours = 8.5  # 8:30 作为基准时间
    hour_offset = int(target_hours - base_hours)

    # 确保小时偏移在有效范围内
    hour_offset = max(0, min(11, hour_offset))  # 限制在0-11之间

    return days_diff, hour_offset


def adjust_chain_timing_v2(
    nodes: Dict[int, NodeInstance],
    graph: Dict,
    all_paths: List[List[str]],
    project_start: datetime,
) -> Dict[int, Tuple[datetime, datetime]]:
    """
    调整链路上节点的时间，处理时间重叠的情况
    基于小时精度，并考虑工作时间范围
    """
    adjusted_times = {}
    skipped_nodes = set()

    def_id_to_instance = {
        node.node_definition_id: node.id for node in nodes.values()
    }

    def check_and_adjust_time(
        node_id: int,
        start_time: datetime,
        end_time: datetime,
        later_nodes: List[int],
    ) -> Tuple[datetime, datetime, bool]:
        """检查并调整节点时间，处理与后续节点的时间重叠"""
        # 规范化时间到工作时间范围
        start_time = normalize_work_time(start_time)
        end_time = normalize_work_time(end_time)

        for later_node_id in later_nodes:
            if later_node_id not in adjusted_times:
                continue

            later_start, later_end = adjusted_times[later_node_id]
            later_start = normalize_work_time(later_start)
            later_end = normalize_work_time(later_end)

            # 获取两个任务的具体小时位置
            start_days, start_hour = calculate_cell_position(
                project_start, start_time
            )
            end_days, end_hour = calculate_cell_position(
                project_start, end_time
            )
            later_start_days, later_start_hour = calculate_cell_position(
                project_start, later_start
            )
            later_end_days, later_end_hour = calculate_cell_position(
                project_start, later_end
            )

            # 转换为总小时数以便比较
            start_total = start_days * 12 + start_hour
            end_total = end_days * 12 + end_hour
            later_start_total = later_start_days * 12 + later_start_hour
            later_end_total = later_end_days * 12 + later_end_hour

            # 检查是否完全包含
            if (
                start_total >= later_start_total
                and end_total <= later_end_total
            ):
                return start_time, end_time, True

            # 检查是否有小时级别的时间交集
            if (
                end_total >= later_start_total
                and start_total <= later_end_total
            ):
                # 计算新的结束时间（回退到上一个小时）
                if later_start_hour == 0:
                    # 如果后续任务从当天开始，则调整到前一天最后一小时
                    new_end_days = later_start_days - 1
                    new_end_hour = 11
                else:
                    new_end_days = later_start_days
                    new_end_hour = later_start_hour - 1

                # 转换回实际时间
                base_time = project_start + timedelta(days=new_end_days)
                adjusted_end = base_time.replace(
                    hour=new_end_hour + 8,  # 8:30 为基准，所以加 8
                    minute=30 if new_end_hour == 0 else 0,  # 如果是第一个小时，设为30分
                )

                # 检查调整后的时间是否有效
                if calculate_work_hours(start_time, adjusted_end) <= 0:
                    return start_time, end_time, True

                return start_time, adjusted_end, False

        return start_time, end_time, False

    def adjust_path_timing(path: List[str]):
        path_node_ids = []
        for def_id in reversed(path):
            if def_id in def_id_to_instance:
                node_id = def_id_to_instance[def_id]
                if node_id in nodes and nodes[node_id].start_time:
                    path_node_ids.append(node_id)

        for i, node_id in enumerate(path_node_ids):
            node = nodes[node_id]
            if not node.start_time:
                continue

            start_time = node.start_time
            end_time = node.end_time if node.end_time else datetime.now()

            later_nodes = path_node_ids[:i]
            adjusted_start, adjusted_end, should_skip = check_and_adjust_time(
                node_id, start_time, end_time, later_nodes
            )

            if should_skip:
                skipped_nodes.add(node_id)
                continue

            adjusted_times[node_id] = (adjusted_start, adjusted_end)

    for i, path in enumerate(all_paths):
        adjust_path_timing(path)

    return adjusted_times


def calculate_project_timeline(
    nodes: List[NodeInstance],
) -> Tuple[datetime, int]:
    """
    计算项目的开始时间和总持续天数
    返回: (项目开始时间, 总持续天数)
    """
    start_times = [n.start_time for n in nodes if n.start_time]
    if not start_times:
        return datetime.now(), 0

    project_start = min(start_times)
    current_date = datetime.now()
    duration = (current_date - project_start).days + 1
    return project_start, duration


def remove_duplicates_from_paths(
    paths: List[List[int]], nodes_dict: Dict[int, NodeInstance]
) -> List[List[int]]:
    """
    从路径中删除重复节点，优先在较长路径中保留节点

    Args:
        paths: 按长度降序排序的路径列表
        nodes_dict: 节点字典

    Returns:
        List[List[int]]: 处理后的路径列表
    """

    # 记录每个节点出现的所有路径
    node_paths = {}
    for i, path in enumerate(paths):
        for node in path:
            if node not in node_paths:
                node_paths[node] = []
            node_paths[node].append(i)

    # 找出重复的节点
    duplicate_nodes = {
        node: indices
        for node, indices in node_paths.items()
        if len(indices) > 1
    }

    if not duplicate_nodes:
        return paths

    # 处理后的路径列表（深拷贝）
    processed_paths = [path[:] for path in paths]

    # 为每个重复节点，从较短的路径中删除
    for node, path_indices in duplicate_nodes.items():
        # 保留在最长路径（索引最小）中的节点
        paths_to_remove_from = path_indices[1:]

        for path_idx in paths_to_remove_from:
            if node in processed_paths[path_idx]:
                processed_paths[path_idx].remove(node)

    # 删除空路径
    processed_paths = [path for path in processed_paths if path]

    return processed_paths


def find_minimal_paths(
    graph: Dict, nodes_dict: Dict[int, NodeInstance]
) -> List[List[int]]:
    """
    找出覆盖所有节点的路径集合，并处理重复节点
    """

    def find_path_from_node(
        start_node: int, visited: Set[int] = None
    ) -> List[int]:
        """从给定节点寻找一条路径"""
        if visited is None:
            visited = set()

        path = []
        current = start_node

        while current is not None and current not in visited:
            visited.add(current)
            path.append(current)

            # 获取所有未访问的后继节点
            next_nodes = [
                n for n in graph['successors'][current] if n not in visited
            ]

            if not next_nodes:
                break

            # 选择入度最小的后继节点
            current = min(
                next_nodes, key=lambda n: len(graph['predecessors'][n])
            )

        return path

    def find_all_start_nodes() -> List[int]:
        """找出所有可能的起始节点"""
        # 首先使用图的起始节点
        start_nodes = list(graph['start_nodes'])

        # 添加所有入度为1的节点作为潜在的起始点
        for node_id in nodes_dict:
            if (
                len(graph['predecessors'][node_id]) == 1
                and node_id not in start_nodes
            ):
                start_nodes.append(node_id)

        return start_nodes

    # 从每个起始节点找出可能的路径
    all_paths = []
    start_nodes = find_all_start_nodes()

    for start in start_nodes:
        path = find_path_from_node(start)
        if path:
            all_paths.append(path)

    # 按路径长度降序排序
    all_paths.sort(key=len, reverse=True)

    # 检查是否覆盖了所有节点
    covered_nodes = {node for path in all_paths for node in path}
    uncovered = set(nodes_dict.keys()) - covered_nodes

    # 如果有未覆盖的节点，为它们创建单独的路径
    for node in uncovered:
        path = find_path_from_node(node)
        if path:
            all_paths.append(path)

    # 处理重复节点
    final_paths = remove_duplicates_from_paths(all_paths, nodes_dict)

    return final_paths


def build_node_graph(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Dict:
    """
    构建节点关系图，包括前驱和后继节点
    返回: {
        'predecessors': {node_id: [predecessor_ids]},
        'successors': {node_id: [successor_ids]},
        'start_nodes': [node_ids],
    }
    """
    predecessors = {node.id: [] for node in nodes}
    successors = {node.id: [] for node in nodes}

    # 构建前驱和后继关系
    for edge in edges:
        predecessors[edge.to_node_instance_id].append(
            edge.from_node_instance_id
        )
        successors[edge.from_node_instance_id].append(edge.to_node_instance_id)

    # 找出起始节点（没有前驱的节点）
    start_nodes = [node.id for node in nodes if not predecessors[node.id]]

    return {
        'predecessors': predecessors,
        'successors': successors,
        'start_nodes': start_nodes,
    }


def find_workflow_paths(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Tuple[Dict, List[List[str]], Dict[str, int]]:
    """
    分析工作流模板结构，返回基准路径信息
    返回: (图结构, 路径列表, 节点行号映射)
    注意: 路径和行号映射使用 node_definition_id 而不是实例 id
    """
    print('\n=== 分析工作流模板结构 ===')

    # 构建节点关系图，使用定义ID作为键
    instance_id_to_def_id = {
        node.id: node.node_definition_id for node in nodes
    }

    # 构建基于实例ID的图
    graph = build_node_graph(nodes, edges)
    nodes_dict = {node.id: node for node in nodes}

    # 找出路径集合并处理重复节点
    instance_paths = find_minimal_paths(graph, nodes_dict)

    # 将实例ID路径转换为定义ID路径
    definition_paths = [
        [instance_id_to_def_id[instance_id] for instance_id in path]
        for path in instance_paths
    ]

    # 为每条路径分配唯一的行号，使用定义ID
    node_rows = {}
    for row, path in enumerate(definition_paths, 1):
        for def_id in path:
            node_rows[def_id] = row

    return graph, definition_paths, node_rows


async def create_gantt_chart(session: AsyncSession, output_file: str):
    """创建基于小时精度的甘特图Excel文件"""
    wb = Workbook()
    ws = wb.active
    ws.title = '甘特图（小时）'

    print('\n=== 开始生成甘特图 ===')

    # 获取主流程工作流
    workflows = (
        (
            await session.execute(
                select(WorkflowInstance)
                .join(WorkflowDefinition)
                .filter(WorkflowDefinition.is_subprocess == False)
            )
        )
        .scalars()
        .all()
    )

    if not workflows:
        print('未找到工作流')
        return

    erp_info = await info.get_erp_info_dict(session)

    # 获取模板结构
    template_workflow = workflows[0]
    template_nodes = (
        (
            await session.execute(
                select(NodeInstance).filter(
                    NodeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    template_edges = (
        (
            await session.execute(
                select(EdgeInstance).filter(
                    EdgeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    template_graph, template_paths, template_node_rows = find_workflow_paths(
        template_nodes, template_edges
    )

    # 准备数据
    all_node_names = set()
    node_defs_map = {}
    max_duration_hours = 0
    workflow_timelines = {}

    # 处理每个工作流
    for workflow in workflows:
        if workflow.status in [
            WorkflowStatus.completed,
            WorkflowStatus.terminated,
        ]:
            continue

        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        # 检查节点状态和时间信息
        if all(node.status == NodeStatus.completed for node in nodes):
            continue

        if not any(node.start_time or node.end_time for node in nodes):
            continue

        # 收集节点信息
        for node in nodes:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            all_node_names.add(node_def.name)
            node_defs_map[node.node_definition_id] = node_def

        # 计算时间线
        nodes_with_time = [n for n in nodes if n.start_time]
        if not nodes_with_time:
            continue

        project_start = min(n.start_time for n in nodes_with_time)
        project_start = normalize_work_time(project_start)

        current_time = normalize_work_time(datetime.now())
        duration_hours = calculate_work_hours(project_start, current_time)

        workflow_timelines[workflow.id] = {
            'start': project_start,
            'duration': duration_hours,
            'nodes': nodes,
        }
        max_duration_hours = max(max_duration_hours, duration_hours)

    # 设置表格样式
    hour_cell_width = 0.5
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)

    # 设置表头
    ws['A1'] = '项目名称'
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font
    ws.row_dimensions[1].height = 28

    # 写入天数表头（每天12个小时格子）
    days = math.ceil(max_duration_hours / 12)
    for day in range(1, days + 2):
        # 每天12个小时格子（8:30-20:30）
        start_col = (day - 1) * 12 + 2  # B列开始
        end_col = start_col + 11

        # 设置小时格子宽度
        for col in range(start_col, end_col + 1):
            col_letter = get_column_letter(col)
            ws.column_dimensions[col_letter].width = hour_cell_width

        # 合并天数表头
        start_letter = get_column_letter(start_col)
        end_letter = get_column_letter(end_col)
        header_range = f'{start_letter}1:{end_letter}1'
        ws.merge_cells(header_range)

        # 设置天数表头样式
        cell = ws[f'{start_letter}1']
        cell.value = f'D{day}'
        cell.alignment = Alignment(horizontal='center', vertical='bottom')
        cell.fill = header_fill
        cell.font = header_font

    current_row = 2
    column_widths = []

    # 为每个工作流生成甘特图
    for workflow in workflows:
        if workflow.id not in workflow_timelines:
            continue

        timeline = workflow_timelines[workflow.id]
        nodes = timeline['nodes']
        project_start = timeline['start']

        # 调整节点时间
        nodes_dict = {node.id: node for node in nodes}
        adjusted_times = adjust_chain_timing_v2(
            nodes_dict,
            template_graph,
            template_paths,
            project_start,
        )

        max_row_num = (
            max(template_node_rows.values()) if template_node_rows else 1
        )

        # 写入项目标题
        erp_row = erp_info[workflow.project_id]
        device_type = (
            '' if workflow.device_type == DeviceType.main_machine else '(出料机)'
        )
        project_title = f'{workflow.project_id} {erp_row["customer"]} {erp_row["model"]}{device_type}'

        ws[f'A{current_row}'] = project_title
        ws[f'A{current_row}'].font = Font(bold=True)
        ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 设置列宽
        column_width = calculate_column_width(project_title)
        column_widths.append(column_width)
        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 绘制每个节点的甘特图条
        for node in nodes:
            if node.start_time and node.id not in adjusted_times:  # 跳过时间冲突的节点
                continue

            if node.start_time:
                node_def = node_defs_map[node.node_definition_id]
                # 直接使用节点定义ID获取行号
                row = (
                    current_row
                    + template_node_rows[node.node_definition_id]
                    - 1
                )

                # 使用调整后的开始时间和结束时间
                start_time, end_time = adjusted_times[node.id]

                # 计算开始和结束的具体位置（天数和小时偏移）
                start_days, start_hour = calculate_cell_position(
                    project_start, start_time
                )
                end_days, end_hour = calculate_cell_position(
                    project_start, end_time
                )

                # 计算在Excel中的具体列位置
                # 每天12列（小时），从B列开始（索引2）
                start_col = (
                    (start_days * 12) + start_hour + 2
                )  # +2 是因为A列是项目名称，从B列开始
                end_col = (end_days * 12) + end_hour + 2

                # 确保至少有一个格子被涂色
                end_col = max(start_col, end_col)

                start_letter = get_column_letter(start_col)
                end_letter = get_column_letter(end_col)

                # 为整个范围的单元格设置填充色
                for col in range(start_col, end_col + 1):
                    col_letter = get_column_letter(col)
                    cell = ws[f'{col_letter}{row}']
                    color = get_node_color(node_def.name)
                    cell.fill = PatternFill(
                        start_color=color, end_color=color, fill_type='solid'
                    )

                # 合并单元格
                cell_range = f'{start_letter}{row}:{end_letter}{row}'
                ws.merge_cells(cell_range)

                # 设置任务名称和对齐方式
                ws[f'{start_letter}{row}'] = get_full_node_name(node_def.name)
                ws[f'{start_letter}{row}'].alignment = Alignment(
                    horizontal='left', vertical='center'
                )

        current_row += max_row_num + 1

    ws.column_dimensions['A'].width = max(column_widths)  # 项目名称列
    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    for row in ws.iter_rows(min_row=1, max_row=current_row - 1):
        for cell in row:
            cell.border = thin_border

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_gantt_chart(session, 'tests/project_gantt_chart.xlsx')
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    asyncio.run(generate_project_gantt_chart())
