from datetime import datetime, timedelta, timezone


class WorkTimeCalculator:
    # 定义北京时区（UTC+8）
    BEIJING_TZ = timezone(timedelta(hours=8))

    # 工作时间配置 (北京时间)
    WORK_START_TIME = timedelta(hours=8, minutes=30)  # 8:30
    WORK_END_TIME = timedelta(hours=17, minutes=30)  # 17:30
    LUNCH_START = timedelta(hours=11, minutes=30)  # 11:30
    LUNCH_END = timedelta(hours=12, minutes=30)  # 12:30
    DINNER_START = timedelta(hours=17, minutes=30)  # 17:30
    DINNER_END = timedelta(hours=18, minutes=30)  # 18:30

    @classmethod
    def to_beijing_time(cls, dt: datetime) -> datetime:
        """将任意时间转换为北京时间"""
        if dt.tzinfo is None:
            # 如果是naive时间，假定为UTC时间
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(cls.BEIJING_TZ)

    @classmethod
    def is_working_time(cls, dt: datetime) -> bool:
        """判断是否在工作时间内"""
        # 转换为北京时间
        beijing_time = cls.to_beijing_time(dt)
        time = timedelta(hours=beijing_time.hour, minutes=beijing_time.minute)

        # 检查是否在工作日时间范围内
        if time < cls.WORK_START_TIME or time > cls.WORK_END_TIME:
            return False

        # 检查是否在午休时间
        if cls.LUNCH_START <= time <= cls.LUNCH_END:
            return False

        return True

    @classmethod
    def get_next_working_time(cls, dt: datetime) -> datetime:
        """获取给定时间后的下一个工作时间点"""
        # 转换为北京时间
        beijing_time = cls.to_beijing_time(dt)
        current_time = timedelta(
            hours=beijing_time.hour, minutes=beijing_time.minute
        )

        result_time = beijing_time

        # 如果在上班之前，直接返回当天的上班时间
        if current_time < cls.WORK_START_TIME:
            result_time = beijing_time.replace(
                hour=cls.WORK_START_TIME.seconds // 3600,
                minute=(cls.WORK_START_TIME.seconds % 3600) // 60,
            )

        # 如果在午休时间，返回午休结束时间
        elif cls.LUNCH_START <= current_time <= cls.LUNCH_END:
            result_time = beijing_time.replace(
                hour=cls.LUNCH_END.seconds // 3600,
                minute=(cls.LUNCH_END.seconds % 3600) // 60,
            )

        # 如果在下班之后，返回第二天的上班时间
        elif current_time >= cls.WORK_END_TIME:
            next_day = beijing_time + timedelta(days=1)
            result_time = next_day.replace(
                hour=cls.WORK_START_TIME.seconds // 3600,
                minute=(cls.WORK_START_TIME.seconds % 3600) // 60,
            )

        return result_time

    @classmethod
    def calculate_working_duration(
        cls, start_time: datetime, duration_minutes: int
    ) -> datetime:
        """
        计算从给定开始时间加上工作时长后的实际结束时间

        Args:
            start_time: 开始时间 (UTC或北京时间)
            duration_minutes: 工作时长（分钟）

        Returns:
            预期结束时间 (带时区信息的北京时间)
        """
        # 确保使用北京时间进行计算
        current_time = cls.to_beijing_time(start_time)
        remaining_minutes = duration_minutes

        while remaining_minutes > 0:
            # 确保当前时间是工作时间
            current_time = cls.get_next_working_time(current_time)

            # 计算当前工作时段可用的分钟数
            available_minutes = cls._get_available_minutes(current_time)

            if available_minutes >= remaining_minutes:
                # 如果当前时段足够完成剩余工作
                current_time += timedelta(minutes=remaining_minutes)
                break
            else:
                # 使用当前时段的可用时间，并移动到下一个工作时段
                current_time += timedelta(minutes=available_minutes)
                remaining_minutes -= available_minutes

        return current_time

    @classmethod
    def _get_available_minutes(cls, dt: datetime) -> int:
        """计算从给定时间点到当前工作时段结束还有多少分钟"""
        # 确保使用北京时间
        beijing_time = cls.to_beijing_time(dt)
        current_time = timedelta(
            hours=beijing_time.hour, minutes=beijing_time.minute
        )

        # 如果在上午工作时段
        if cls.WORK_START_TIME <= current_time < cls.LUNCH_START:
            return int((cls.LUNCH_START - current_time).total_seconds() / 60)

        # 如果在下午工作时段
        if cls.LUNCH_END <= current_time < cls.WORK_END_TIME:
            return int((cls.WORK_END_TIME - current_time).total_seconds() / 60)

        return 0
