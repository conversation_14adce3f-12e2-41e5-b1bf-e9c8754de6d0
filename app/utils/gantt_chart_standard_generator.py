import os
import sys

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)

# 获取当前脚本的上层目录
parent_dir = os.path.dirname(os.path.dirname(current_script_path))

# 将上层目录添加到 Python 路径中
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

import math
import asyncio
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload
from datetime import datetime, timedelta
from typing import Dict, List, Set, Tuple
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
    MachineTypeDuration,
)
from database.base import engine
from utils.tools import (
    calculate_column_width,
    get_node_color,
    get_full_node_name,
)
from schemas.base import DeviceType


def calculate_node_start_times(
    graph: Dict,
    duration_map: Dict[str, int],
    nodes_dict: Dict[int, NodeInstance],
) -> Dict[int, int]:
    """
    计算每个节点的开始时间（以天为单位）

    Args:
        graph: 节点关系图，包含 predecessors 和 successors
        duration_map: 节点定义ID到持续时间的映射
        nodes_dict: 节点实例字典

    Returns:
        Dict[int, int]: 节点实例ID到开始时间（天数）的映射
    """
    start_times = {}  # 存储每个节点的开始时间
    processed = set()  # 已处理的节点

    def get_node_duration(node_id: int) -> int:
        """获取节点的持续时间"""
        node = nodes_dict[node_id]
        return duration_map.get(node.node_definition_id, 0)

    def calculate_node_time(node_id: int) -> int:
        """递归计算节点的开始时间"""
        if node_id in start_times:
            return start_times[node_id]

        # 获取所有前驱节点
        predecessors = graph['predecessors'][node_id]

        if not predecessors:
            # 没有前驱节点，从0开始
            start_time = 0
        else:
            # 计算所有前驱节点的结束时间，取最大值
            pred_end_times = []
            for pred_id in predecessors:
                if pred_id not in processed:
                    calculate_node_time(pred_id)
                pred_start = start_times[pred_id]
                pred_duration = get_node_duration(pred_id)
                pred_end_times.append(pred_start + pred_duration)

            # 取最晚的结束时间作为当前节点的开始时间
            start_time = max(pred_end_times)

        start_times[node_id] = start_time
        processed.add(node_id)
        return start_time

    # 从所有终止节点开始反向计算
    end_nodes = [
        node_id
        for node_id in nodes_dict.keys()
        if not graph['successors'][node_id]
    ]

    # 确保所有路径都被处理
    for node_id in nodes_dict.keys():
        if node_id not in processed:
            calculate_node_time(node_id)

    return start_times


def adjust_chain_timing_v2(
    nodes: Dict[int, NodeInstance],
    graph: Dict,
    all_paths: List[List[str]],  # 使用定义ID的路径
    project_start: datetime,
) -> Dict[int, Tuple[datetime, datetime]]:
    """
    调整链路上节点的时间，处理时间重叠的情况
    使用定义ID路径，但返回实例ID的时间映射
    当出现时间重叠时：
    1. 如果前面节点完全包含在后面节点时间内，忽略前面节点
    2. 如果前面节点与后面节点有交集，将前面节点的结束时间调整为后面节点的开始时间
    """
    adjusted_times = {}
    skipped_nodes = set()

    # 创建定义ID到实例ID的映射
    def_id_to_instance = {
        node.node_definition_id: node.id for node in nodes.values()
    }

    def check_and_adjust_time(
        node_id: int,
        start_time: datetime,
        end_time: datetime,
        later_nodes: List[int],
    ) -> Tuple[datetime, datetime, bool]:
        """
        检查并调整节点时间，处理与后续节点的时间重叠
        返回: (调整后的开始时间, 调整后的结束时间, 是否需要忽略该节点)
        """
        for later_node_id in later_nodes:
            if later_node_id not in adjusted_times:
                continue

            later_start, later_end = adjusted_times[later_node_id]

            # 检查当前节点是否完全包含在后续节点的时间范围内
            if (
                start_time.date() >= later_start.date()
                and end_time.date() <= later_end.date()
            ):
                return start_time, end_time, True

            # 检查是否有时间交集
            if (
                end_time.date() > later_start.date()
                and start_time.date() < later_end.date()
            ):
                # 将当前节点的结束时间调整为后续节点的开始时间
                adjusted_end = later_start - timedelta(days=1)
                if adjusted_end.date() < start_time.date():
                    return start_time, end_time, True

                return start_time, adjusted_end, False

        return start_time, end_time, False

    def adjust_path_timing(path: List[str]):
        # 获取路径中实际存在的节点ID列表（从后往前）
        path_node_ids = []
        for def_id in reversed(path):
            if def_id in def_id_to_instance:
                node_id = def_id_to_instance[def_id]
                if node_id in nodes and nodes[node_id].start_time:
                    path_node_ids.append(node_id)

        # 从后往前处理节点
        for i, node_id in enumerate(path_node_ids):
            node = nodes[node_id]
            if not node.start_time:
                continue

            start_time = node.start_time
            end_time = node.end_time if node.end_time else datetime.now()

            # 检查与后续节点的时间重叠
            later_nodes = path_node_ids[:i]  # 获取当前节点之后的所有节点
            adjusted_start, adjusted_end, should_skip = check_and_adjust_time(
                node_id, start_time, end_time, later_nodes
            )

            if should_skip:
                skipped_nodes.add(node_id)
                continue

            adjusted_times[node_id] = (adjusted_start, adjusted_end)

    # 按路径调整时间
    for i, path in enumerate(all_paths):
        adjust_path_timing(path)

    return adjusted_times


def calculate_project_timeline(
    nodes: List[NodeInstance],
) -> Tuple[datetime, int]:
    """
    计算项目的开始时间和总持续天数
    返回: (项目开始时间, 总持续天数)
    """
    start_times = [n.start_time for n in nodes if n.start_time]
    if not start_times:
        return datetime.now(), 0

    project_start = min(start_times)
    current_date = datetime.now()
    duration = (current_date - project_start).days + 1
    return project_start, duration


def remove_duplicates_from_paths(
    paths: List[List[int]], nodes_dict: Dict[int, NodeInstance]
) -> List[List[int]]:
    """
    从路径中删除重复节点，优先在较长路径中保留节点

    Args:
        paths: 按长度降序排序的路径列表
        nodes_dict: 节点字典

    Returns:
        List[List[int]]: 处理后的路径列表
    """

    # 记录每个节点出现的所有路径
    node_paths = {}
    for i, path in enumerate(paths):
        for node in path:
            if node not in node_paths:
                node_paths[node] = []
            node_paths[node].append(i)

    # 找出重复的节点
    duplicate_nodes = {
        node: indices
        for node, indices in node_paths.items()
        if len(indices) > 1
    }

    if not duplicate_nodes:
        return paths

    # 处理后的路径列表（深拷贝）
    processed_paths = [path[:] for path in paths]

    # 为每个重复节点，从较短的路径中删除
    for node, path_indices in duplicate_nodes.items():
        # 保留在最长路径（索引最小）中的节点
        paths_to_remove_from = path_indices[1:]

        for path_idx in paths_to_remove_from:
            if node in processed_paths[path_idx]:
                processed_paths[path_idx].remove(node)

    # 删除空路径
    processed_paths = [path for path in processed_paths if path]

    return processed_paths


def find_minimal_paths(
    graph: Dict, nodes_dict: Dict[int, NodeInstance]
) -> List[List[int]]:
    """
    找出覆盖所有节点的路径集合，并处理重复节点
    """

    def find_path_from_node(
        start_node: int, visited: Set[int] = None
    ) -> List[int]:
        """从给定节点寻找一条路径"""
        if visited is None:
            visited = set()

        path = []
        current = start_node

        while current is not None and current not in visited:
            visited.add(current)
            path.append(current)

            # 获取所有未访问的后继节点
            next_nodes = [
                n for n in graph['successors'][current] if n not in visited
            ]

            if not next_nodes:
                break

            # 选择入度最小的后继节点
            current = min(
                next_nodes, key=lambda n: len(graph['predecessors'][n])
            )

        return path

    def find_all_start_nodes() -> List[int]:
        """找出所有可能的起始节点"""
        # 首先使用图的起始节点
        start_nodes = list(graph['start_nodes'])

        # 添加所有入度为1的节点作为潜在的起始点
        for node_id in nodes_dict:
            if (
                len(graph['predecessors'][node_id]) == 1
                and node_id not in start_nodes
            ):
                start_nodes.append(node_id)

        return start_nodes

    # 从每个起始节点找出可能的路径
    all_paths = []
    start_nodes = find_all_start_nodes()

    for start in start_nodes:
        path = find_path_from_node(start)
        if path:
            all_paths.append(path)
    # 按路径长度降序排序
    all_paths.sort(key=len, reverse=True)

    # 检查是否覆盖了所有节点
    covered_nodes = {node for path in all_paths for node in path}
    uncovered = set(nodes_dict.keys()) - covered_nodes

    # 如果有未覆盖的节点，为它们创建单独的路径
    for node in uncovered:
        path = find_path_from_node(node)
        if path:
            all_paths.append(path)

    # 处理重复节点
    final_paths = remove_duplicates_from_paths(all_paths, nodes_dict)

    return final_paths


def build_node_graph(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Dict:
    """
    构建节点关系图，包括前驱和后继节点
    返回: {
        'predecessors': {node_id: [predecessor_ids]},
        'successors': {node_id: [successor_ids]},
        'start_nodes': [node_ids],
    }
    """
    predecessors = {node.id: [] for node in nodes}
    successors = {node.id: [] for node in nodes}

    # 构建前驱和后继关系
    for edge in edges:
        predecessors[edge.to_node_instance_id].append(
            edge.from_node_instance_id
        )
        successors[edge.from_node_instance_id].append(edge.to_node_instance_id)

    # 找出起始节点（没有前驱的节点）
    start_nodes = [node.id for node in nodes if not predecessors[node.id]]

    return {
        'predecessors': predecessors,
        'successors': successors,
        'start_nodes': start_nodes,
    }


def find_workflow_paths(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Tuple[Dict, List[List[str]], Dict[str, int]]:
    """
    分析工作流模板结构，返回基准路径信息
    返回: (图结构, 路径列表, 节点行号映射)
    注意: 路径和行号映射使用 node_definition_id 而不是实例 id
    """

    # 构建节点关系图，使用定义ID作为键
    instance_id_to_def_id = {
        node.id: node.node_definition_id for node in nodes
    }

    # 构建基于实例ID的图
    graph = build_node_graph(nodes, edges)
    nodes_dict = {node.id: node for node in nodes}

    # 找出路径集合并处理重复节点
    instance_paths = find_minimal_paths(graph, nodes_dict)

    # 将实例ID路径转换为定义ID路径
    definition_paths = [
        [instance_id_to_def_id[instance_id] for instance_id in path]
        for path in instance_paths
    ]

    # 为每条路径分配唯一的行号，使用定义ID
    node_rows = {}
    for row, path in enumerate(definition_paths, 1):
        for def_id in path:
            node_rows[def_id] = row

    return graph, definition_paths, node_rows


async def create_standard_time_gantt(session: AsyncSession, output_file: str):
    """创建标准时间甘特图，按小时显示每个节点的预期执行时长"""
    try:
        wb = load_workbook(output_file)
    except FileNotFoundError:
        wb = Workbook()
        ws = wb.active

    sheet_name = '标准周期表'
    base_name = sheet_name
    counter = 1
    while sheet_name in wb.sheetnames:
        sheet_name = f'{base_name}_{counter}'
        counter += 1

    ws.title = sheet_name

    # 获取模板工作流和相关数据
    template_workflow = (
        await session.execute(
            select(WorkflowInstance)
            .join(WorkflowDefinition)
            .filter(WorkflowDefinition.is_subprocess == False)
            .limit(1)
        )
    ).scalar_one_or_none()

    if not template_workflow:
        print('未找到模板工作流')
        return

    # 获取节点和边
    template_nodes = (
        (
            await session.execute(
                select(NodeInstance).filter(
                    NodeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    template_edges = (
        (
            await session.execute(
                select(EdgeInstance).filter(
                    EdgeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    # 获取路径信息
    template_graph, template_paths, template_node_rows = find_workflow_paths(
        template_nodes, template_edges
    )

    # 获取节点持续时间
    node_durations = (
        (
            await session.execute(
                select(MachineTypeDuration)
                .join(NodeDefinition)
                .filter(NodeDefinition.workflow_definition_id == 1)
                .options(joinedload(MachineTypeDuration.node_definition))
            )
        )
        .scalars()
        .all()
    )

    # 按机型和设备类型处理持续时间，将分钟转换为小时（向上取整）
    machine_types = set(d.machine_type for d in node_durations)
    device_types = set(d.device_type for d in node_durations)
    duration_maps = {}  # {machine_type: {node_def_id: duration_in_hours}}
    max_duration_hours = 0

    for machine_type in machine_types:
        for device_type in device_types:
            durations = {}
            for duration in node_durations:
                if (
                    duration.machine_type == machine_type
                    and duration.device_type == device_type
                ):
                    # 转换分钟为小时并向上取整
                    hours = math.ceil((duration.expected_duration or 0) / 60)
                    durations[duration.node_definition_id] = hours

            device_type_name = (
                '' if device_type == DeviceType.main_machine else '(出料机)'
            )
            duration_maps[f'{machine_type}{device_type_name}'] = durations

            # 计算该机型的最长结束时间（小时）
            start_times = calculate_node_start_times(
                template_graph,
                durations,
                {node.id: node for node in template_nodes},
            )

            for node_def_id, duration in durations.items():
                if duration > 0:
                    node_instance = next(
                        node
                        for node in template_nodes
                        if node.node_definition_id == node_def_id
                    )
                    node_end_time = start_times[node_instance.id] + duration
                    max_duration_hours = max(max_duration_hours, node_end_time)

    # 添加缓冲时间并转换为天数
    max_duration_hours += 10  # 添加10小时缓冲
    max_duration_days = math.ceil(max_duration_hours / 10)

    # 设置基本样式
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    # 设置表头
    ws['A1'] = '机型/工序'
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font

    # 设置小时格子的宽度（更窄）
    hour_cell_width = 0.5

    ws.row_dimensions[1].height = 28

    # 写入天数表头并合并对应的小时格子
    for day in range(1, max_duration_days + 1):
        start_col = (day - 1) * 10 + 2  # B列开始
        end_col = start_col + 9

        # 设置每个小时格子的宽度
        for col in range(start_col, end_col + 1):
            col_letter = get_column_letter(col)
            ws.column_dimensions[col_letter].width = hour_cell_width

        # 合并天数表头
        start_letter = get_column_letter(start_col)
        end_letter = get_column_letter(end_col)
        header_range = f'{start_letter}1:{end_letter}1'
        ws.merge_cells(header_range)

        # 设置天数表头样式
        cell = ws[f'{start_letter}1']
        cell.value = f'D{day}'
        cell.alignment = Alignment(textRotation=90)
        cell.fill = header_fill
        cell.font = header_font

    current_row = 2
    column_widths = []

    # 处理每种机型
    for machine_type, durations in duration_maps.items():
        if not any(durations.values()):
            continue

        # 计算该机型下所有节点的开始时间（小时）
        start_times = calculate_node_start_times(
            template_graph,
            durations,
            {node.id: node for node in template_nodes},
        )

        max_row_num = (
            max(template_node_rows.values()) if template_node_rows else 1
        )

        # 写入机型名称
        ws[f'A{current_row}'] = machine_type
        ws[f'A{current_row}'].font = Font(bold=True)
        ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        column_width = calculate_column_width(machine_type)
        column_widths.append(column_width)

        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 绘制甘特图
        for node_def_id, duration in durations.items():
            if duration > 0:
                row = current_row + template_node_rows[node_def_id] - 1

                # 获取节点实例
                node_instance = next(
                    node
                    for node in template_nodes
                    if node.node_definition_id == node_def_id
                )

                # 计算开始和结束列（基于小时）
                start_col = 2 + start_times[node_instance.id]  # B列开始
                end_col = start_col + duration - 1

                start_letter = get_column_letter(start_col)
                end_letter = get_column_letter(end_col)

                # 获取节点定义和设置样式
                node_def = await session.get(NodeDefinition, node_def_id)
                color = get_node_color(node_def.name)

                # 合并单元格
                cell_range = f'{start_letter}{row}:{end_letter}{row}'
                ws.merge_cells(cell_range)

                # 设置单元格样式和内容
                cell = ws[f'{start_letter}{row}']
                cell.value = (
                    f'{get_full_node_name(node_def.name)} ({duration / 10}d)'
                )
                cell.fill = PatternFill(
                    start_color=color, end_color=color, fill_type='solid'
                )
                cell.alignment = Alignment(
                    horizontal='left', vertical='center'
                )

        current_row += max_row_num + 1  # 添加空行分隔不同机型

    # 设置最终的列宽
    ws.column_dimensions['A'].width = max(column_widths)

    # 添加边框
    for row in ws.iter_rows(min_row=1, max_row=current_row - 1):
        for cell in row:
            cell.border = thin_border

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_standard_time_gantt(
                session, 'tests/project_gantt_chart.xlsx'
            )
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    asyncio.run(generate_project_gantt_chart())
