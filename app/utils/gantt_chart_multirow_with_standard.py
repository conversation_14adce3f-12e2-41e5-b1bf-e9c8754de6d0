import os
import sys

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)

# 获取当前脚本的上层目录
parent_dir = os.path.dirname(os.path.dirname(current_script_path))

# 将上层目录添加到 Python 路径中
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

import math
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
    MachineTypeDuration,
)
from database.base import engine
from schemas import WorkflowStatus, NodeStatus
from utils.tools import get_full_node_name, get_node_color
from database.crud.info_management import info
from utils.tools import calculate_column_width
from schemas.base import DeviceType
from utils.gantt_chart_standard_generator import (
    calculate_node_start_times,
    find_workflow_paths,
)


def get_node_order_number(node_def_name: str) -> int:
    """
    从节点名称中提取排序数字
    例如: "1.节点名称" -> 1
    """
    full_name = get_full_node_name(node_def_name)
    try:
        # 分割字符串，取 '.' 前面的部分
        parts = full_name.split('.')
        if len(parts) >= 2:
            return int(parts[0])
        else:
            # 如果没有 '.'，返回一个很大的数字让它排在最后
            return 9999
    except (ValueError, IndexError):
        # 如果无法解析数字，返回一个很大的数字让它排在最后
        return 9999


def assign_rows_by_node_order(
    nodes: List[NodeInstance], node_defs_map: Dict[int, NodeDefinition]
) -> Dict[int, int]:
    """
    根据节点名称中的数字顺序分配行号
    """
    node_rows = {}

    # 为每个节点获取排序数字
    node_order_pairs = []
    for node in nodes:
        if node.start_time:  # 只处理有开始时间的节点
            node_def = node_defs_map[node.node_definition_id]
            order_num = get_node_order_number(node_def.name)
            node_order_pairs.append((node, order_num))

    # 按排序数字排序
    node_order_pairs.sort(key=lambda x: x[1])

    # 分配行号
    for i, (node, order_num) in enumerate(node_order_pairs):
        node_rows[node.id] = i + 1

    return node_rows


def assign_template_rows_by_node_order(
    template_nodes: List[NodeInstance],
    node_defs_map: Dict[int, NodeDefinition],
) -> Dict[int, int]:
    """
    为模板节点根据节点名称中的数字顺序分配行号
    """
    template_node_rows = {}

    # 为每个模板节点获取排序数字
    node_order_pairs = []
    for node in template_nodes:
        node_def = node_defs_map[node.node_definition_id]
        order_num = get_node_order_number(node_def.name)
        node_order_pairs.append((node, order_num))

    # 按排序数字排序
    node_order_pairs.sort(key=lambda x: x[1])

    # 分配行号，使用 node_definition_id 作为键
    for i, (node, order_num) in enumerate(node_order_pairs):
        template_node_rows[node.node_definition_id] = i + 1

    return template_node_rows


def calculate_display_time_range(
    start_time: datetime, end_time: Optional[datetime], min_slots: int = 5
) -> Tuple[datetime, datetime]:
    """
    计算显示用的时间范围，确保每天都满足最小格子数要求
    """
    if not end_time:
        end_time = datetime.now()

    # 处理结束时间小于等于开始时间的情况
    if end_time <= start_time:
        end_time = start_time

    def get_day_bounds(time: datetime) -> Tuple[datetime, datetime]:
        """获取给定时间所在天的工作时间范围"""
        day_start = time.replace(hour=8, minute=30, second=0, microsecond=0)
        day_end = time.replace(hour=20, minute=30, second=0, microsecond=0)

        if time < day_start:
            return day_start - timedelta(days=1), day_end - timedelta(days=1)
        return day_start, day_end

    def adjust_to_work_hours(time: datetime) -> datetime:
        """调整时间到工作时间范围内"""
        day_start, day_end = get_day_bounds(time)

        if time < day_start:
            return day_start
        elif time > day_end:
            next_day = time.date() + timedelta(days=1)
            return datetime.combine(
                next_day, datetime.min.time().replace(hour=8, minute=30)
            )
        return time

    def get_slots_in_day(time: datetime) -> int:
        """计算从给定时间到当天20:30的剩余格子数"""
        _, day_end = get_day_bounds(time)
        start_slot = get_hour_slot(time)
        end_slot = get_hour_slot(day_end)
        return end_slot - start_slot + 1

    def extend_time_in_day(time: datetime, needed_slots: int) -> datetime:
        """在当天内扩展时间以获得所需格子数"""
        _, day_end = get_day_bounds(time)
        current_slots = get_slots_in_day(time)

        if current_slots >= needed_slots:
            # 当前时间加上所需小时数
            return time + timedelta(hours=needed_slots - 1)
        else:
            # 如果当天剩余格子不够，返回20:30，由外层处理跳天
            return day_end

    # 调整时间到工作时间范围内
    start_time = adjust_to_work_hours(start_time)
    end_time = adjust_to_work_hours(end_time)

    # 确保每天都有足够的格子数
    while True:
        # 检查当前天的剩余格子数
        slots_in_current_day = get_slots_in_day(end_time)

        if slots_in_current_day < min_slots:
            # 如果当天剩余格子不够，跳到下一天8:30
            next_day = end_time.date() + timedelta(days=1)
            end_time = datetime.combine(
                next_day, datetime.min.time().replace(hour=8, minute=30)
            )

            # 在新的一天确保有足够的格子
            end_time = extend_time_in_day(end_time, min_slots)
        else:
            # 当天格子够用，扩展到需要的格子数
            if end_time.hour < 20 or (
                end_time.hour == 20 and end_time.minute < 30
            ):
                end_time = extend_time_in_day(end_time, min_slots)
            break

    return start_time, end_time


def get_grid_range(
    start_time: datetime, end_time: datetime, chart_start: datetime
) -> Tuple[int, int]:
    """
    获取时间范围对应的格子范围，用于检查重叠
    """
    start_col = get_column_for_time(start_time, chart_start)
    end_col = get_column_for_time(end_time, chart_start)
    return start_col, end_col


def is_grid_overlap(range1: Tuple[int, int], range2: Tuple[int, int]) -> bool:
    """
    检查两个格子范围是否重叠
    """
    start1, end1 = range1
    start2, end2 = range2
    return start1 <= end2 and start2 <= end1


class DisplayTimeInfo:
    """存储节点显示时间信息的类"""

    def __init__(self, node_id: int, start_time: datetime, end_time: datetime):
        self.node_id = node_id
        self.start_time = start_time
        self.end_time = end_time


def prepare_display_times(
    nodes: List[NodeInstance], min_slots: int = 5
) -> Dict[int, DisplayTimeInfo]:
    """
    为所有节点准备显示时间信息
    """
    display_times = {}
    for node in nodes:
        if node.start_time:
            start_time, end_time = calculate_display_time_range(
                node.start_time, node.end_time, min_slots
            )
            display_times[node.id] = DisplayTimeInfo(
                node.id, start_time, end_time
            )
    return display_times


def is_time_overlap_with_display(
    start1: datetime,
    end1: datetime,
    start2: datetime,
    end2: datetime,
    display_times: Dict[int, DisplayTimeInfo],
    node1_id: int,
    node2_id: int,
) -> bool:
    """使用显示时间判断两个时间段是否重叠"""
    # 使用显示时间而不是原始时间
    display_time1 = display_times.get(node1_id)
    display_time2 = display_times.get(node2_id)

    if display_time1 and display_time2:
        return (
            display_time1.start_time < display_time2.end_time
            and display_time2.start_time < display_time1.end_time
        )

    return start1 < end2 and start2 < end1


def find_parallel_paths(
    node_id: int, edges: List[EdgeInstance], nodes: Dict[int, NodeInstance]
) -> List[List[int]]:
    """查找从给定节点开始的所有并行路径"""

    def dfs(
        current_id: int, current_path: List[int], all_paths: List[List[int]]
    ):
        current_path.append(current_id)
        next_nodes = [
            e.to_node_instance_id
            for e in edges
            if e.from_node_instance_id == current_id
        ]

        if not next_nodes:
            all_paths.append(current_path[:])
        else:
            for next_node in next_nodes:
                dfs(next_node, current_path[:], all_paths)

    all_paths = []
    dfs(node_id, [], all_paths)
    return all_paths


def get_hour_slot(time: datetime) -> int:
    """
    将时间转换为对应的小时格子索引（0-11）
    8:30-20:30 分为12个格子
    """
    base_time = time.replace(hour=8, minute=30, second=0, microsecond=0)

    # 如果时间早于8:30，使用第一个格子
    if time < base_time:
        return 0

    # 如果时间晚于20:30，使用最后一个格子
    end_time = base_time.replace(hour=20, minute=30)
    if time > end_time:
        return 11

    # 计算时间差（小时）
    time_diff = (time - base_time).total_seconds() / 3600

    # 将时间差转换为格子索引（12小时分12格）
    slot = int(time_diff)
    return min(slot, 11)


def get_column_for_time(time: datetime, start_date: datetime) -> int:
    """
    计算给定时间对应的列号
    """
    if time < start_date:
        return 3  # C列开始（A列是项目名称，B列是文字信息）
    days_diff = (time.date() - start_date.date()).days
    hour_slot = get_hour_slot(time)
    return days_diff * 12 + hour_slot + 3  # +3 因为A列是项目名称，B列是文字信息


def is_time_overlap(
    start1: datetime, end1: datetime, start2: datetime, end2: datetime
) -> bool:
    """判断两个时间段是否重叠"""
    current_time = datetime.now()
    end1 = end1 or current_time
    end2 = end2 or current_time

    # 处理时间倒置的情况
    if start1 > end1:
        end1 = start1
    if start2 > end2:
        end2 = start2

    return start1 < end2 and start2 < end1


async def create_gantt_chart(
    session: AsyncSession, output_file: str, project_ids: List[str] = None
):
    """创建甘特图Excel文件"""
    try:
        wb = load_workbook(output_file)
        # 检查是否已存在同名sheet，如果存在则添加数字后缀
        sheet_name = '项目周期表'
        base_name = sheet_name
        counter = 1
        if sheet_name in wb.sheetnames:
            while sheet_name in wb.sheetnames:
                sheet_name = f'{base_name}_{counter}'
                counter += 1
            ws = wb.create_sheet(sheet_name)
        else:
            ws = wb.create_sheet(sheet_name)

    except FileNotFoundError:
        # 如果文件不存在，创建新的
        wb = Workbook()
        ws = wb.active
        ws.title = '项目周期表'

    # 获取模板工作流和相关数据
    template_workflow = (
        await session.execute(
            select(WorkflowInstance)
            .join(WorkflowDefinition)
            .filter(WorkflowDefinition.is_subprocess == False)
            .limit(1)
        )
    ).scalar_one_or_none()

    if not template_workflow:
        print('未找到模板工作流')
        return

    # 获取节点和边
    template_nodes = (
        (
            await session.execute(
                select(NodeInstance).filter(
                    NodeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    template_edges = (
        (
            await session.execute(
                select(EdgeInstance).filter(
                    EdgeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    # 获取路径信息
    template_graph, template_paths, _ = find_workflow_paths(
        template_nodes, template_edges
    )

    # 获取节点持续时间
    node_durations = (
        (
            await session.execute(
                select(MachineTypeDuration)
                .join(NodeDefinition)
                .filter(NodeDefinition.workflow_definition_id == 1)
                .options(joinedload(MachineTypeDuration.node_definition))
            )
        )
        .scalars()
        .all()
    )

    # 按机型和设备类型处理持续时间，将分钟转换为小时（向上取整）
    machine_types = set(d.machine_type for d in node_durations)
    device_types = set(d.device_type for d in node_durations)
    duration_maps = {}  # {machine_type: {node_def_id: duration_in_hours}}
    max_duration_hours = 0

    for machine_type in machine_types:
        for device_type in device_types:
            durations = {}
            for duration in node_durations:
                if (
                    duration.machine_type == machine_type
                    and duration.device_type == device_type
                ):
                    # 转换分钟为小时并向上取整
                    hours = math.ceil((duration.expected_duration or 0) / 60)
                    durations[duration.node_definition_id] = hours

            device_type_name = (
                '' if device_type == DeviceType.main_machine else '(出料机)'
            )
            duration_maps[f'{machine_type}{device_type_name}'] = durations

            # 计算该机型的最长结束时间（小时）
            start_times = calculate_node_start_times(
                template_graph,
                durations,
                {node.id: node for node in template_nodes},
            )

            for node_def_id, duration in durations.items():
                if duration > 0:
                    node_instance = next(
                        node
                        for node in template_nodes
                        if node.node_definition_id == node_def_id
                    )
                    node_end_time = start_times[node_instance.id] + duration
                    max_duration_hours = max(max_duration_hours, node_end_time)

    # 添加缓冲时间并转换为天数
    max_duration_hours += 10  # 添加10小时缓冲
    max_duration_days = math.ceil(max_duration_hours / 10)

    # 获取主流程工作流
    query = (
        select(WorkflowInstance)
        .join(WorkflowDefinition)
        .filter(WorkflowDefinition.is_subprocess == False)
        .order_by(
            WorkflowInstance.project_id.desc(),
            WorkflowInstance.device_type.asc(),
        )
    )

    # 如果指定了项目ID列表，添加筛选条件
    if project_ids:
        query = query.filter(WorkflowInstance.project_id.in_(project_ids))

    # 获取主流程工作流
    workflows = (await session.execute(query)).scalars().all()

    # 获取所有唯一的节点名称和定义
    all_node_names = set()
    node_defs_map = {}  # 存储节点定义的映射
    for workflow in workflows:
        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )
        for node in nodes:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            all_node_names.add(node_def.name)
            node_defs_map[node.node_definition_id] = node_def

    # 为模板节点也添加节点定义映射
    for node in template_nodes:
        if node.node_definition_id not in node_defs_map:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            node_defs_map[node.node_definition_id] = node_def

    # 确定整体的时间范围
    all_start_times = []
    current_date = datetime.now().replace(
        hour=0, minute=0, second=0, microsecond=0
    )

    for workflow in workflows:
        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        start_times = [
            datetime.combine(n.start_time.date(), datetime.min.time())
            for n in nodes
            if n.start_time
        ]
        if start_times:
            all_start_times.extend(start_times)

    chart_start = (
        datetime.combine(
            min(t.date() for t in all_start_times), datetime.min.time()
        )
        if all_start_times
        else current_date
    )
    chart_end = datetime.combine(current_date.date(), datetime.min.time())

    max_standard_start = (
        datetime.combine(
            max(t.date() for t in all_start_times), datetime.min.time()
        )
        if all_start_times
        else current_date
    )

    max_standard_end = max_standard_start + timedelta(days=max_duration_days)

    chart_end = max(chart_end, max_standard_end)

    # 生成日期列表
    dates = []
    temp_date = chart_start
    while temp_date <= chart_end:
        dates.append(temp_date)
        temp_date += timedelta(days=1)

    # 设置基本样式
    cell_width = 0.5
    ws.column_dimensions['A'].width = 15
    ws.column_dimensions['B'].width = 20  # 新增的文字信息列
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)

    # 写入表头
    ws['A1'] = '项目名称'
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font

    ws['B1'] = '任务名称'  # 新增的文字信息列表头
    ws['B1'].fill = header_fill
    ws['B1'].font = header_font

    # 写入日期表头，每天合并12列，从C列开始
    for day_idx, date in enumerate(dates):
        # 计算该天的起始和结束列，从C列开始
        start_col = day_idx * 12 + 3  # +3 是因为A列是项目名称，B列是文字信息
        end_col = start_col + 11

        # 设置所有列的宽度
        for col in range(start_col, end_col + 1):
            col_letter = get_column_letter(col)
            ws.column_dimensions[col_letter].width = cell_width

        # 合并当天的所有列并设置日期
        start_letter = get_column_letter(start_col)
        end_letter = get_column_letter(end_col)
        ws.merge_cells(f'{start_letter}1:{end_letter}1')

        # 设置日期
        cell = ws[f'{start_letter}1']
        cell.value = date.strftime('%m-%d')
        cell.alignment = Alignment(horizontal='center')
        cell.fill = header_fill
        cell.font = header_font

    current_row = 2
    change_records = await info.get_mech_info_change_records(session)
    erp_info = await info.get_erp_info_dict(session)
    column_widths = []

    # 为每个工作流实例创建甘特图
    for workflow in workflows:
        if workflow.status.value in [
            WorkflowStatus.completed.value,
            WorkflowStatus.terminated.value,
        ]:
            continue
        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        # 检查是否所有节点都是completed状态
        all_completed = all(
            node.status.value == NodeStatus.completed.value for node in nodes
        )
        if all_completed:
            continue

        # 检查工作流是否包含时间信息
        has_times = any(node.start_time or node.end_time for node in nodes)
        if not has_times:
            continue

        edges = (
            (
                await session.execute(
                    select(EdgeInstance).filter(
                        EdgeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        start_cols = []
        # 绘制每个节点的甘特图条
        for node in nodes:
            if node.start_time:
                start_col = get_column_for_time(node.start_time, chart_start)
                start_cols.append(start_col)

        offset = min(start_cols)

        # 使用新的行分配方法：根据节点名称中的数字排序
        node_rows = assign_rows_by_node_order(nodes, node_defs_map)

        # 计算最大行数
        max_row_num = max(node_rows.values()) if node_rows else 1

        device_type = (
            '' if workflow.device_type == DeviceType.main_machine else '(出料机)'
        )

        erp_row = erp_info[workflow.project_id]
        # 写入工作流标题
        project_title = f'{workflow.project_id} \n{erp_row["customer"]} \n{erp_row["model"]}{device_type}'
        ws[f'A{current_row}'] = project_title
        ws[f'A{current_row}'].font = Font(bold=True)
        ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 计算并设置列宽
        column_width = calculate_column_width(erp_row['customer'])
        column_widths.append(column_width)
        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 先计算所有节点的显示时间
        display_times = prepare_display_times(nodes, min_slots=5)

        # 绘制每个节点的甘特图条
        for node in nodes:
            if node.start_time and node.id in node_rows:
                node_def = node_defs_map[node.node_definition_id]
                row = current_row + node_rows[node.id] - 1

                # 在B列设置节点名称
                ws[f'B{row}'] = get_full_node_name(node_def.name)
                ws[f'B{row}'].alignment = Alignment(
                    horizontal='left', vertical='center'
                )
                ws[f'B{row}'].fill = PatternFill(
                    start_color=get_node_color(node_def.name),
                    end_color=get_node_color(node_def.name),
                    fill_type='solid',
                )

                # 使用显示时间计算列位置
                display_time = display_times[node.id]
                start_col = get_column_for_time(
                    display_time.start_time, chart_start
                )
                end_col = get_column_for_time(
                    display_time.end_time, chart_start
                )
                # 确保至少有一个格子被涂色
                end_col = max(start_col, end_col)
                # 设置格式和填充颜色
                start_letter = get_column_letter(start_col)
                end_letter = get_column_letter(end_col)
                cell_range = f'{start_letter}{row}:{end_letter}{row}'

                # 合并单元格并设置填充色，但不设置文字
                ws.merge_cells(cell_range)
                for col in range(start_col, end_col + 1):
                    col_letter = get_column_letter(col)
                    cell = ws[f'{col_letter}{row}']
                    color = get_node_color(node_def.name)
                    cell.fill = PatternFill(
                        start_color=color, end_color=color, fill_type='solid'
                    )

        # Add change records row
        changes_row = current_row + max_row_num
        ws[f'A{changes_row}'] = '变更记录'
        ws[f'A{changes_row}'].font = Font(bold=True)
        ws[f'A{changes_row}'].alignment = Alignment(vertical='center')
        ws.row_dimensions[changes_row].height = 15  # 标准单行高度

        # Process change records for this project
        change_records_for_project = [
            record
            for record in change_records
            if record['erp'] == workflow.project_id
        ]
        filtered_records = []
        for record in change_records_for_project:
            part_num = record['partNumber']
            # Try to convert part number to integer, if fails (contains letters) it will be None
            try:
                part_num_int = int(part_num)
                is_numeric = True
            except (ValueError, TypeError):
                part_num_int = None
                is_numeric = False
            if workflow.device_type == DeviceType.main_machine:
                # For main machine, only include part numbers < 100
                if is_numeric and part_num_int < 100:
                    filtered_records.append(record)
            else:
                # For feeder machine, include part numbers >= 100 or containing letters
                if not is_numeric or part_num_int >= 100:
                    filtered_records.append(record)

        # Group changes by time slot and part number
        changes_by_slot = {}
        for record in filtered_records:
            slot_col = get_column_for_time(record['created_at'], chart_start)
            if slot_col not in changes_by_slot:
                changes_by_slot[slot_col] = {}

            part_num = record['partNumber']
            if part_num not in changes_by_slot[slot_col]:
                changes_by_slot[slot_col][part_num] = set()

            # Add the submission count to the set
            changes_by_slot[slot_col][part_num].add(record['submitCount'])

        # Add change records to the chart
        for slot_col, changes in changes_by_slot.items():
            col_letter = get_column_letter(slot_col)
            cell = ws[f'{col_letter}{changes_row}']
            # Format changes: focus on part number and change count
            formatted_changes = []
            for part_num, versions in changes.items():
                versions_list = sorted(
                    versions,
                    key=lambda x: int(x) if x.isdigit() else float('inf'),
                )
                if len(versions_list) == 1:
                    formatted_changes.append(f'部件{part_num}变更1次')
                else:
                    formatted_changes.append(
                        f'部件{part_num}变更{len(versions_list)}次'
                    )

            cell.value = '; '.join(formatted_changes)
            cell.alignment = Alignment(
                horizontal='left', vertical='center', wrap_text=True
            )
            cell.fill = PatternFill(
                start_color=get_node_color('部件完整信息录入'),
                end_color=get_node_color('部件完整信息录入'),
                fill_type='solid',
            )

        current_row += max_row_num + 2  # Add extra row for change records

        device_type = (
            '' if workflow.device_type == DeviceType.main_machine else '(出料机)'
        )
        machine_type = f'{workflow.machine_type}{device_type}'
        if machine_type in duration_maps:
            durations = duration_maps[machine_type]
            if any(durations.values()):
                # 计算该机型下所有节点的开始时间（小时）
                start_times = calculate_node_start_times(
                    template_graph,
                    durations,
                    {node.id: node for node in template_nodes},
                )
                for i, j in start_times.items():
                    start_times[i] = j + offset - 3  # 调整offset，因为现在从C列开始

                # 使用新的行分配方法：根据节点名称中的数字排序
                template_node_rows = assign_template_rows_by_node_order(
                    template_nodes, node_defs_map
                )

                max_row_num = (
                    max(template_node_rows.values())
                    if template_node_rows
                    else 1
                )

                # 写入机型名称
                ws[f'A{current_row}'] = machine_type
                ws[f'A{current_row}'].font = Font(bold=True)
                ws[f'A{current_row}'].alignment = Alignment(vertical='center')

                column_width = calculate_column_width(machine_type)
                column_widths.append(column_width)

                if max_row_num > 1:
                    ws.merge_cells(
                        f'A{current_row}:A{current_row + max_row_num - 1}'
                    )
                    ws[f'A{current_row}'].alignment = Alignment(
                        vertical='center'
                    )

                # 绘制甘特图
                for node_def_id, duration in durations.items():
                    row = current_row + template_node_rows[node_def_id] - 1

                    # 获取节点实例和节点定义
                    node_instance = next(
                        node
                        for node in template_nodes
                        if node.node_definition_id == node_def_id
                    )
                    node_def = await session.get(NodeDefinition, node_def_id)

                    # 在B列设置节点名称和持续时间（无论duration是否为0）
                    ws[
                        f'B{row}'
                    ] = f'{get_full_node_name(node_def.name)} ({duration / 10}d)'
                    ws[f'B{row}'].alignment = Alignment(
                        horizontal='left', vertical='center'
                    )
                    ws[f'B{row}'].fill = PatternFill(
                        start_color=get_node_color(node_def.name),
                        end_color=get_node_color(node_def.name),
                        fill_type='solid',
                    )

                    # 只有当duration > 0时才绘制涂色块
                    if duration > 0:
                        # 计算开始和结束列（基于小时）
                        start_col = 3 + start_times[node_instance.id]  # C列开始
                        end_col = start_col + duration - 1

                        start_letter = get_column_letter(start_col)
                        end_letter = get_column_letter(end_col)

                        # 设置颜色
                        color = get_node_color(node_def.name)

                        # 合并单元格并只设置填充色，不设置文字
                        cell_range = f'{start_letter}{row}:{end_letter}{row}'
                        ws.merge_cells(cell_range)

                        for col in range(start_col, end_col + 1):
                            col_letter = get_column_letter(col)
                            cell = ws[f'{col_letter}{row}']
                            cell.fill = PatternFill(
                                start_color=color,
                                end_color=color,
                                fill_type='solid',
                            )

                current_row += max_row_num + 1  # 添加空行分隔不同机型

    ws.column_dimensions['A'].width = 12  # 项目名称列
    ws.column_dimensions['B'].width = 18  # 项目名称列

    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    for row in ws.iter_rows(min_row=1, max_row=current_row - 1):
        for cell in row:
            cell.border = thin_border

    ws.freeze_panes = 'C1'

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_gantt_chart(
                session, 'tests/project_gantt_chart.xlsx', ['37956']
            )
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    import asyncio

    asyncio.run(generate_project_gantt_chart())
