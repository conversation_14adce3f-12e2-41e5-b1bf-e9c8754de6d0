from openpyxl import Workbook
from openpyxl.styles import Pat<PERSON><PERSON><PERSON>, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, timedelta
import random
from typing import Dict, List, Set, Tuple
import colorsys
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
)
from database.base import engine


def calculate_project_timeline(
    nodes: List[NodeInstance],
) -> <PERSON><PERSON>[datetime, int]:
    """
    计算项目的开始时间和总持续天数
    返回: (项目开始时间, 总持续天数)
    """
    start_times = [n.start_time for n in nodes if n.start_time]
    if not start_times:
        return datetime.now(), 0

    project_start = min(start_times)
    current_date = datetime.now()
    duration = (current_date - project_start).days + 1
    return project_start, duration


def calculate_node_relative_days(
    node: NodeInstance, project_start: datetime
) -> <PERSON><PERSON>[int, int]:
    """
    计算节点相对于项目开始的天数
    返回: (开始天数, 结束天数)
    """
    if not node.start_time:
        return 0, 0

    start_day = (node.start_time - project_start).days + 1

    if node.end_time:
        end_day = (node.end_time - project_start).days + 1
    else:
        end_day = (datetime.now() - project_start).days + 1

    return start_day, end_day


def generate_distinct_colors(n: int) -> List[str]:
    """生成n个视觉上区分度高的颜色"""
    colors = []
    for i in range(n):
        hue = (i * 0.618033988749895) % 1
        saturation = 0.4 + random.random() * 0.2
        value = 0.8 + random.random() * 0.1

        rgb = colorsys.hsv_to_rgb(hue, saturation, value)
        hex_color = '%02x%02x%02x' % tuple(int(x * 255) for x in rgb)
        colors.append(hex_color.upper())

    return colors


def is_time_overlap(
    start1: datetime, end1: datetime, start2: datetime, end2: datetime
) -> bool:
    """判断两个时间段是否重叠"""
    current_time = datetime.now()
    end1 = end1 or current_time
    end2 = end2 or current_time
    return start1 < end2 and start2 < end1


def find_parallel_paths(
    node_id: int, edges: List[EdgeInstance], nodes: Dict[int, NodeInstance]
) -> List[List[int]]:
    """查找从给定节点开始的所有并行路径"""

    def dfs(
        current_id: int, current_path: List[int], all_paths: List[List[int]]
    ):
        current_path.append(current_id)
        next_nodes = [
            e.to_node_instance_id
            for e in edges
            if e.from_node_instance_id == current_id
        ]

        if not next_nodes:
            all_paths.append(current_path[:])
        else:
            for next_node in next_nodes:
                dfs(next_node, current_path[:], all_paths)

    all_paths = []
    dfs(node_id, [], all_paths)
    return all_paths


def assign_rows(
    nodes: List[NodeInstance], edges: List[EdgeInstance]
) -> Dict[int, int]:
    """为节点分配行号，确保重叠的任务不会在同一行"""
    node_map = {node.id: node for node in nodes}
    node_rows = {}

    # 找到所有起始节点
    start_nodes = [
        n
        for n in nodes
        if not any(e.to_node_instance_id == n.id for e in edges)
    ]

    # 获取所有执行路径
    all_paths = []
    for start_node in start_nodes:
        paths = find_parallel_paths(start_node.id, edges, node_map)
        all_paths.extend(paths)

    # 创建节点到路径的映射
    node_to_paths = {node.id: [] for node in nodes}
    for path in all_paths:
        for node_id in path:
            node_to_paths[node_id].append(path)

    # 按开始时间排序节点
    sorted_nodes = sorted(
        [n for n in nodes if n.start_time], key=lambda x: x.start_time.date()
    )

    def get_path_nodes(node_id: int) -> Set[int]:
        """获取与当前节点在同一路径上的所有节点ID"""
        path_nodes = set()
        for path in node_to_paths[node_id]:
            path_nodes.update(path)
        return path_nodes

    # 为每个节点分配行号
    for node in sorted_nodes:
        # 获取与当前节点在同一路径上的所有节点
        path_nodes = get_path_nodes(node.id)

        # 查找可用的行号
        row = 0
        while True:
            row += 1
            can_use_row = True

            # 检查已分配行号的节点
            for other_node_id, assigned_row in node_rows.items():
                other_node = node_map[other_node_id]

                # 如果是同一路径上的节点，或者时间有重叠，就不能用相同的行
                if assigned_row == row and (
                    other_node_id in path_nodes
                    or (
                        other_node.start_time
                        and is_time_overlap(  # 确保节点有开始时间
                            node.start_time,
                            node.end_time,
                            other_node.start_time,
                            other_node.end_time,
                        )
                    )
                ):
                    can_use_row = False
                    break

            if can_use_row:
                break

        node_rows[node.id] = row

    return node_rows


def adjust_times_by_rows(
    nodes: List[NodeInstance],
    edges: List[EdgeInstance],
    node_rows: Dict[int, int],
) -> Dict[int, datetime]:
    """根据行号调整节点时间"""
    adjusted_start_times = {}

    # 按行号和开始时间排序节点
    sorted_nodes = sorted(
        [(n, node_rows.get(n.id, 0)) for n in nodes if n.start_time],
        key=lambda x: (x[1], x[0].start_time.date()),
    )

    # 记录每行最后的结束时间
    row_end_times = {}

    # 调整每个节点的时间
    for node, row in sorted_nodes:
        # 获取当前行的最后结束时间
        last_end_date = row_end_times.get(row)

        # 确定开始日期
        start_date = node.start_time.date()
        if last_end_date and start_date <= last_end_date:
            # 如果开始时间早于或等于该行最后结束时间，调整到下一天
            start_date = last_end_date + timedelta(days=1)

        # 检查调整后的开始时间是否可行
        if not node.end_time or start_date <= node.end_time.date():
            adjusted_start = datetime.combine(start_date, datetime.min.time())
            adjusted_start_times[node.id] = adjusted_start

            # 更新该行的最后结束时间
            if node.end_time:
                row_end_times[row] = node.end_time.date()
            else:
                row_end_times[row] = datetime.now().date()

    return adjusted_start_times


async def create_gantt_chart(session: AsyncSession, output_file: str):
    """创建甘特图Excel文件"""
    wb = Workbook()
    ws = wb.active
    ws.title = 'Project Gantt Chart'

    # 获取主流程工作流
    workflows = (
        (
            await session.execute(
                select(WorkflowInstance)
                .join(WorkflowDefinition)
                .filter(WorkflowDefinition.is_subprocess == False)
            )
        )
        .scalars()
        .all()
    )

    # 获取所有唯一的节点名称和定义
    all_node_names = set()
    node_defs_map = {}

    # 计算所有项目中最长的持续天数
    max_duration = 0
    workflow_timelines = {}  # 存储每个工作流的时间信息

    for workflow in workflows:
        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        for node in nodes:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            all_node_names.add(node_def.name)
            node_defs_map[node.node_definition_id] = node_def

        # 计算每个工作流的时间线
        project_start, duration = calculate_project_timeline(nodes)
        workflow_timelines[workflow.id] = {
            'start': project_start,
            'duration': duration,
            'nodes': nodes,
        }
        max_duration = max(max_duration, duration)

    # 为每个节点名称生成唯一的颜色
    node_colors = dict(
        zip(
            sorted(all_node_names),
            generate_distinct_colors(len(all_node_names)),
        )
    )

    # 设置表头样式
    cell_width = 3
    ws.column_dimensions['A'].width = 15  # 项目名称列
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)

    # 写入表头
    ws['A1'] = '项目名称'
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font

    # 写入天数表头
    for day in range(1, max_duration + 1):
        col_letter = get_column_letter(day + 1)
        ws.column_dimensions[col_letter].width = cell_width
        cell = ws[f'{col_letter}1']
        cell.value = str(day)
        cell.alignment = Alignment(textRotation=90)
        cell.fill = header_fill
        cell.font = header_font

    current_row = 2

    # 为每个工作流实例创建甘特图
    for workflow in workflows:
        timeline = workflow_timelines[workflow.id]
        nodes = timeline['nodes']
        project_start = timeline['start']

        edges = (
            (
                await session.execute(
                    select(EdgeInstance).filter(
                        EdgeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        # 为任务分配行号
        node_rows = assign_rows(nodes, edges)
        max_row_num = max(node_rows.values()) if node_rows else 1

        # 写入工作流标题
        project_title = (
            f'项目 {workflow.project_id}'
            if workflow.project_id
            else f'工作流 {workflow.id}'
        )
        ws[f'A{current_row}'] = project_title
        ws[f'A{current_row}'].font = Font(bold=True)
        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 绘制每个节点的甘特图条
        for node in nodes:
            if node.start_time:
                node_def = node_defs_map[node.node_definition_id]
                row = current_row + node_rows[node.id] - 1

                # 计算相对天数
                start_day, end_day = calculate_node_relative_days(
                    node, project_start
                )

                # 设置单元格颜色和合并
                start_col = start_day + 1  # 加1是因为A列是项目名称
                end_col = end_day + 1

                # 确保至少有一个格子被涂色
                end_col = max(start_col, end_col)

                start_letter = get_column_letter(start_col)
                end_letter = get_column_letter(end_col)

                # 为整个范围的单元格设置填充色
                for col in range(start_col, end_col + 1):
                    col_letter = get_column_letter(col)
                    cell = ws[f'{col_letter}{row}']
                    color = node_colors[node_def.name]
                    cell.fill = PatternFill(
                        start_color=color, end_color=color, fill_type='solid'
                    )

                # 合并单元格
                cell_range = f'{start_letter}{row}:{end_letter}{row}'
                ws.merge_cells(cell_range)

                # 设置任务名称和对齐方式
                ws[f'{start_letter}{row}'] = node_def.name
                ws[f'{start_letter}{row}'].alignment = Alignment(
                    horizontal='center', vertical='center'
                )

        current_row += max_row_num + 1

    # 添加图例
    legend_row = current_row + 2
    ws[f'A{legend_row}'] = '图例：'
    ws[f'A{legend_row}'].font = Font(bold=True)
    legend_row += 1

    # 为每个任务类型创建图例
    for node_name, color in sorted(node_colors.items()):
        ws[f'A{legend_row}'] = node_name
        cell = ws[f'B{legend_row}']
        cell.fill = PatternFill(
            start_color=color, end_color=color, fill_type='solid'
        )
        legend_row += 1

    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    for row in ws.iter_rows(min_row=1, max_row=legend_row - 1):
        for cell in row:
            cell.border = thin_border

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_gantt_chart(session, 'tests/project_gantt_chart.xlsx')
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    import asyncio

    asyncio.run(generate_project_gantt_chart())
