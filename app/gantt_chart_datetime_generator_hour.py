import asyncio
from openpyxl import Workbook, load_workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
from database.models import (
    WorkflowInstance,
    NodeInstance,
    EdgeInstance,
    NodeDefinition,
    WorkflowDefinition,
)
from database.base import engine
from schemas import WorkflowStatus, NodeStatus
from database.crud.info_management import info
from utils.tools import (
    calculate_column_width,
    get_node_color,
    get_full_node_name,
)
from utils.gantt_chart_generator import find_workflow_paths
from schemas.base import DeviceType


def adjust_node_times_by_template(
    nodes: List[NodeInstance],
    template_paths: List[List[str]],
    template_node_rows: Dict[str, int],
) -> Dict[int, Tuple[datetime, datetime]]:
    """
    根据模板路径和行号调整节点时间
    处理原则：
    1. 如果是进行中的节点（有开始时间，无结束时间），保留该节点时间不变
    2. 如果两个节点时间相邻或重叠，前面节点的结束时间需要比后面节点的开始时间早1小时
    3. 时间调整精确到小时
    """
    adjusted_times = {}  # 存储调整后的时间 {node_id: (start_time, end_time)}
    current_time = datetime.now()

    def round_time_to_work_hours(dt: datetime) -> datetime:
        """将时间调整到工作时间范围内(8:30-20:30)"""
        hour = dt.hour + (dt.minute / 60.0)
        if hour < 8.5:
            # 如果早于8:30，调整到8:30
            return dt.replace(hour=8, minute=30, second=0, microsecond=0)
        elif hour > 20.5:
            # 如果晚于20:30，调整到20:30
            return dt.replace(hour=20, minute=30, second=0, microsecond=0)
        return dt.replace(second=0, microsecond=0)

    # 获取所有有时间信息的节点
    timed_nodes = []
    for node in nodes:
        if node.start_time:
            row = template_node_rows.get(node.node_definition_id, 0)
            # 记录节点是否正在进行中
            is_active = False
            # 对开始时间进行舍入
            start_time = round_time_to_work_hours(node.start_time)
            # 结束时间处理
            if node.end_time:
                end_time = round_time_to_work_hours(node.end_time)
            else:
                end_time = round_time_to_work_hours(current_time)

            timed_nodes.append((node, row, is_active, start_time, end_time))

    # 按行号分组
    row_nodes = {}
    for node, row, is_active, start_time, end_time in timed_nodes:
        if row not in row_nodes:
            row_nodes[row] = []
        row_nodes[row].append((node, is_active, start_time, end_time))

    # 处理每一行的节点
    for row, nodes_in_row in row_nodes.items():
        # 按开始时间排序，活动节点放在最后
        nodes_in_row.sort(
            key=lambda x: (
                not x[1],  # 非活动节点优先
                x[2],  # 按开始时间排序（精确到小时）
            )
        )

        # 从后往前调整时间
        for i in range(len(nodes_in_row) - 1, -1, -1):
            current_node, is_active, current_start, current_end = nodes_in_row[
                i
            ]

            # 活动节点保持原样
            if is_active:
                adjusted_times[current_node.id] = (current_start, current_end)
                continue

            # 检查与后续节点的时间重叠或相邻
            next_conflicting_start = None
            for j in range(i + 1, len(nodes_in_row)):
                next_node, next_is_active, next_start, next_end = nodes_in_row[
                    j
                ]

                # 检查时间重叠或相邻（精确到小时）
                time_diff = (
                    next_start - current_end
                ).total_seconds() / 3600  # 转换为小时
                if time_diff <= 0:  # 如果时间重叠或相邻
                    # 如果后续节点是活动的或者还没有找到冲突时间
                    if next_is_active or next_conflicting_start is None:
                        next_conflicting_start = next_start

            # 如果存在重叠或相邻，调整当前节点的结束时间
            if next_conflicting_start:
                # 将结束时间调整为后续节点开始时间前1小时
                adjusted_end = next_conflicting_start - timedelta(hours=1)
                adjusted_end = round_time_to_work_hours(adjusted_end)

                # 只有当调整后的结束时间仍然在开始时间之后才进行调整
                time_span = (
                    adjusted_end - current_start
                ).total_seconds() / 3600
                if time_span >= 1:  # 确保至少有1小时的时间跨度
                    current_end = adjusted_end

            adjusted_times[current_node.id] = (current_start, current_end)

    return adjusted_times


def get_time_column(
    time: datetime,
    chart_start: datetime,
    hours_per_day: int = 12,
    start_hour: float = 8.5,
    end_hour: float = 20.5,
) -> int:
    """
    计算给定时间对应的列号
    考虑工作时间范围(8:30-20:30)和每天12个时间槽
    """
    # 计算日期偏移
    days_offset = (time.date() - chart_start.date()).days
    base_col = days_offset * hours_per_day + 2  # 2是因为第一列是项目名称

    # 获取小时偏移
    hour = time.hour + time.minute / 60
    if hour < start_hour:
        hour_offset = 0
    elif hour > end_hour:
        hour_offset = hours_per_day - 1
    else:
        hour_offset = int(
            (hour - start_hour) / ((end_hour - start_hour) / hours_per_day)
        )
        hour_offset = min(max(0, hour_offset), hours_per_day - 1)

    return base_col + hour_offset


def get_hour_column(
    time: datetime, start_hour: float = 8.5, end_hour: float = 20.5
) -> int:
    """
    Convert time to column index based on hour slots
    Returns -1 if before start_hour, num_slots+1 if after end_hour
    """
    hour = time.hour + time.minute / 60
    if hour < start_hour:
        return 0  # Before start time
    if hour > end_hour:
        return 12  # After end time

    # Calculate slot (0-11 for the 12 time slots)
    slot = int((hour - start_hour) / ((end_hour - start_hour) / 12))
    return min(max(0, slot), 11)  # Ensure within bounds


def format_hour_header(hour: float) -> str:
    """Convert hour float to formatted string (e.g., 8.5 -> '08:30')"""
    h = int(hour)
    m = int((hour % 1) * 60)
    return f'{h:02d}:{m:02d}'


async def create_gantt_chart(session: AsyncSession, output_file: str):
    """创建甘特图Excel文件"""
    try:
        wb = load_workbook(output_file)
    except FileNotFoundError:
        wb = Workbook()

    sheet_name = '甘特图（日期）'
    base_name = sheet_name
    counter = 1
    while sheet_name in wb.sheetnames:
        sheet_name = f'{base_name}_{counter}'
        counter += 1

    ws = wb.create_sheet(sheet_name)

    # 设置工作时间范围
    start_hour = 8.5  # 8:30
    end_hour = 20.5  # 20:30
    hours_per_day = 12  # Number of hour slots per day

    # 获取主流程工作流
    workflows = (
        (
            await session.execute(
                select(WorkflowInstance)
                .join(WorkflowDefinition)
                .filter(WorkflowDefinition.is_subprocess == False)
            )
        )
        .scalars()
        .all()
    )

    if not workflows:
        print('未找到工作流')
        return

    # 获取第一个工作流的结构作为模板
    template_workflow = workflows[0]
    template_nodes = (
        (
            await session.execute(
                select(NodeInstance).filter(
                    NodeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    template_edges = (
        (
            await session.execute(
                select(EdgeInstance).filter(
                    EdgeInstance.workflow_instance_id == template_workflow.id
                )
            )
        )
        .scalars()
        .all()
    )

    # 分析模板工作流结构（只执行一次）
    template_graph, template_paths, template_node_rows = find_workflow_paths(
        template_nodes, template_edges
    )

    # 获取所有唯一的节点名称和定义
    node_defs_map = {}
    all_start_times = []
    current_date = datetime.now().replace(
        hour=0, minute=0, second=0, microsecond=0
    )

    for workflow in workflows:
        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        for node in nodes:
            node_def = await session.get(
                NodeDefinition, node.node_definition_id
            )
            node_defs_map[node.node_definition_id] = node_def
            if node.start_time:
                all_start_times.append(node.start_time)

    # 设置时间范围
    chart_start = (
        min(all_start_times).replace(hour=0, minute=0, second=0, microsecond=0)
        if all_start_times
        else current_date
    )
    chart_end = current_date

    # 生成日期列表
    dates = []
    temp_date = chart_start
    while temp_date <= chart_end:
        dates.append(temp_date)
        temp_date += timedelta(days=1)

    # 设置表头
    ws['A1'] = '项目名称'
    header_fill = PatternFill(
        start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'
    )
    header_font = Font(bold=True)
    ws['A1'].fill = header_fill
    ws['A1'].font = header_font

    # 为每天创建12个时间槽
    current_col = 2
    for date in dates:
        # 创建日期表头
        date_col_letter = get_column_letter(current_col)
        ws[f'{date_col_letter}1'] = date.strftime('%m-%d')
        ws[f'{date_col_letter}1'].fill = header_fill
        ws[f'{date_col_letter}1'].font = header_font

        # 合并当天的所有小时格子作为日期表头
        end_col_letter = get_column_letter(current_col + hours_per_day - 1)
        ws.merge_cells(f'{date_col_letter}1:{end_col_letter}1')

        # 设置小时格子的宽度
        for i in range(hours_per_day):
            col = current_col + i
            ws.column_dimensions[get_column_letter(col)].width = 1.5

        current_col += hours_per_day

    current_row = 2
    erp_info = await info.get_erp_info_dict(session)
    column_widths = []

    # 处理每个工作流
    for workflow in workflows:
        if workflow.status in [
            WorkflowStatus.completed,
            WorkflowStatus.terminated,
        ]:
            continue

        nodes = (
            (
                await session.execute(
                    select(NodeInstance).filter(
                        NodeInstance.workflow_instance_id == workflow.id
                    )
                )
            )
            .scalars()
            .all()
        )

        # 跳过检查逻辑...
        if all(node.status == NodeStatus.completed for node in nodes):
            continue
        if not any(node.start_time for node in nodes):
            continue

        # 使用模板路径和行号调整节点时间
        adjusted_times = adjust_node_times_by_template(
            nodes, template_paths, template_node_rows
        )

        max_row_num = (
            max(template_node_rows.values()) if template_node_rows else 1
        )
        device_type = (
            '' if workflow.device_type == DeviceType.main_machine else '(出料机)'
        )
        # 写入工作流标题
        erp_row = erp_info[workflow.project_id]
        project_title = f'{workflow.project_id} {erp_row["customer"]} {erp_row["model"]}{device_type}'
        ws[f'A{current_row}'] = project_title
        ws[f'A{current_row}'].font = Font(bold=True)

        # 设置列宽和合并单元格
        column_width = calculate_column_width(project_title)
        column_widths.append(column_width)
        if max_row_num > 1:
            ws.merge_cells(f'A{current_row}:A{current_row + max_row_num - 1}')
            ws[f'A{current_row}'].alignment = Alignment(vertical='center')

        # 绘制甘特图条
        for node in nodes:
            if node.id not in adjusted_times:
                continue

            # 获取调整后的起止时间
            start_time = adjusted_times[node.id][0]  # 获取元组的第一个元素作为开始时间
            end_time = adjusted_times[node.id][1]  # 获取元组的第二个元素作为结束时间

            # 使用模板中的行号
            row = current_row + template_node_rows[node.node_definition_id] - 1

            # 计算起始和结束列
            start_date_col = (
                start_time.date() - chart_start.date()
            ).days * hours_per_day + 2
            end_date_col = (
                end_time.date() - chart_start.date()
            ).days * hours_per_day + 2

            # 添加小时偏移
            start_hour_offset = get_hour_column(start_time)
            end_hour_offset = get_hour_column(end_time)

            start_col = start_date_col + start_hour_offset
            end_col = end_date_col + end_hour_offset

            # 设置单元格样式和内容
            start_letter = get_column_letter(start_col)
            end_letter = get_column_letter(end_col)

            node_def = node_defs_map[node.node_definition_id]
            color = get_node_color(node_def.name)

            # 首先设置单元格的值
            first_cell = ws[f'{start_letter}{row}']
            first_cell.value = get_full_node_name(node_def.name)
            first_cell.alignment = Alignment(
                horizontal='left', vertical='center'
            )

            # 为整个范围设置填充色
            for col in range(start_col, end_col + 1):
                col_letter = get_column_letter(col)
                cell = ws[f'{col_letter}{row}']
                cell.fill = PatternFill(
                    start_color=color, end_color=color, fill_type='solid'
                )

            # 最后合并单元格
            cell_range = f'{start_letter}{row}:{end_letter}{row}'
            ws.merge_cells(cell_range)

        current_row += max_row_num + 1

    # 最终调整
    ws.column_dimensions['A'].width = max(column_widths)

    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin'),
    )

    for row in ws.iter_rows(min_row=1, max_row=current_row - 1):
        for cell in row:
            cell.border = thin_border

    # 保存文件
    wb.save(output_file)


async def generate_project_gantt_chart():
    """主函数：生成项目甘特图"""
    async with AsyncSession(engine) as session:
        try:
            await create_gantt_chart(session, 'tests/project_gantt_chart.xlsx')
            print('甘特图已成功生成！')
        except Exception as e:
            print(f'生成甘特图时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
        finally:
            await engine.dispose()


if __name__ == '__main__':
    asyncio.run(generate_project_gantt_chart())
