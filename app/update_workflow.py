from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, timedelta
import random
from typing import Dict, List, Set, Optional, Tuple
from database.models import (
    NodeDefinition,
    EdgeInstance,
    NodeInstance,
    InputLogicType,
    WorkflowInstance,
    NodeStatus,
    WorkflowStatus,
    WorkflowDefinition,
)
from database.base import engine


async def get_node_duration() -> int:
    """随机生成1-10天的任务持续时间"""
    return random.randint(1, 10)


async def load_workflow_data(
    session: AsyncSession, workflow_instance_id: int
) -> Tuple[List[NodeInstance], List[EdgeInstance], List[NodeDefinition]]:
    """加载工作流相关的所有数据"""
    nodes = (
        (
            await session.execute(
                select(NodeInstance)
                .filter(
                    NodeInstance.workflow_instance_id == workflow_instance_id
                )
                .order_by(NodeInstance.id)
            )
        )
        .scalars()
        .all()
    )

    edges = (
        (
            await session.execute(
                select(EdgeInstance).filter(
                    EdgeInstance.workflow_instance_id == workflow_instance_id
                )
            )
        )
        .scalars()
        .all()
    )

    node_definitions = (
        (
            await session.execute(
                select(NodeDefinition).filter(
                    NodeDefinition.id.in_(
                        [node.node_definition_id for node in nodes]
                    )
                )
            )
        )
        .scalars()
        .all()
    )

    return nodes, edges, node_definitions


class NodeTimeProcessor:
    def __init__(
        self,
        nodes: List[NodeInstance],
        edges: List[EdgeInstance],
        node_definitions: List[NodeDefinition],
        session: AsyncSession,
        workflow_instance_id: int,
    ):
        self.session = session
        self.node_map = {node.id: node for node in nodes}
        self.node_def_map = {
            node_def.id: node_def for node_def in node_definitions
        }
        self.processed_nodes: Set[int] = set()
        self.last_end_time: Optional[datetime] = None
        self.workflow_instance_id = workflow_instance_id

        # 创建边映射
        self.incoming_edges: Dict[int, List[EdgeInstance]] = {}
        self.outgoing_edges: Dict[int, List[EdgeInstance]] = {}

        for edge in edges:
            if edge.to_node_instance_id not in self.incoming_edges:
                self.incoming_edges[edge.to_node_instance_id] = []
            if edge.from_node_instance_id not in self.outgoing_edges:
                self.outgoing_edges[edge.from_node_instance_id] = []

            self.incoming_edges[edge.to_node_instance_id].append(edge)
            self.outgoing_edges[edge.from_node_instance_id].append(edge)

        # 找到起始节点
        self.start_nodes = [
            node
            for node in nodes
            if node.id not in self.incoming_edges
            or not self.incoming_edges[node.id]
        ]

    async def update_node_times(
        self, node: NodeInstance, current_start_time: datetime
    ) -> Optional[datetime]:
        """更新单个节点的时间"""
        if node.id in self.processed_nodes:
            return node.end_time

        node_id = node.id

        node_def = self.node_def_map[node.node_definition_id]
        node_end_time = None

        # 根据节点状态设置时间
        if node.status == NodeStatus.completed:
            node.start_time = current_start_time
            duration_days = await get_node_duration()
            node.actual_duration = duration_days * 24 * 60
            node_end_time = current_start_time + timedelta(days=duration_days)
            node.end_time = node_end_time

            # 如果有子流程，处理子流程
            if node.subprocess_instance_id:
                subprocess_instance = await self.session.get(
                    WorkflowInstance, node.subprocess_instance_id
                )
                if subprocess_instance:
                    subprocess_end_time = await process_workflow_nodes(
                        self.session,
                        subprocess_instance.id,
                        current_start_time,
                    )
                    node = await self.session.get(NodeInstance, node_id)
                    (
                        self.nodes,
                        self.edges,
                        self.node_definitions,
                    ) = await load_workflow_data(
                        self.session, self.workflow_instance_id
                    )
                    node.end_time = subprocess_end_time
                    node_end_time = subprocess_end_time

        elif node.status == NodeStatus.active:
            node.start_time = current_start_time
            node.end_time = None
            node.actual_duration = None

        elif node.status == NodeStatus.pending:
            node.start_time = None
            node.end_time = None
            node.actual_duration = None
        self.processed_nodes.add(node.id)

        # 处理后续节点
        if (
            node.status == NodeStatus.completed
            and node.id in self.outgoing_edges
        ):
            for edge in self.outgoing_edges[node.id]:
                next_node = self.node_map[edge.to_node_instance_id]

                if next_node.id in self.incoming_edges:
                    all_prev_processed = all(
                        e.from_node_instance_id in self.processed_nodes
                        for e in self.incoming_edges[next_node.id]
                    )

                    if all_prev_processed:
                        next_start_time = None
                        if node_def.input_logic == InputLogicType.AND:
                            prev_end_times = [
                                self.node_map[e.from_node_instance_id].end_time
                                for e in self.incoming_edges[next_node.id]
                                if self.node_map[
                                    e.from_node_instance_id
                                ].end_time
                                is not None
                            ]
                            if prev_end_times:
                                next_start_time = max(prev_end_times)
                        else:  # OR logic
                            next_start_time = (
                                node_end_time
                                if node_end_time
                                else current_start_time
                            )

                        if next_start_time:
                            next_end_time = await self.update_node_times(
                                next_node, next_start_time
                            )
                            if next_end_time and (
                                self.last_end_time is None
                                or next_end_time > self.last_end_time
                            ):
                                self.last_end_time = next_end_time

        return node_end_time


async def process_workflow_nodes(
    session: AsyncSession,
    workflow_instance_id: int,
    start_date: Optional[datetime],
) -> datetime:
    """处理单个工作流实例中的所有节点，包括子流程"""
    # 预先加载所有需要的数据
    nodes, edges, node_definitions = await load_workflow_data(
        session, workflow_instance_id
    )

    # 创建节点时间处理器
    processor = NodeTimeProcessor(
        nodes, edges, node_definitions, session, workflow_instance_id
    )

    # 处理所有起始节点
    for start_node in processor.start_nodes:
        node_end_time = await processor.update_node_times(
            start_node, start_date
        )
        if node_end_time and (
            processor.last_end_time is None
            or node_end_time > processor.last_end_time
        ):
            processor.last_end_time = node_end_time
    workflow_instance = await session.get(
        WorkflowInstance, workflow_instance_id
    )
    # 更新工作流实例时间
    workflow_instance.start_time = start_date
    if workflow_instance.status == WorkflowStatus.completed:
        workflow_instance.end_time = processor.last_end_time
    else:
        workflow_instance.end_time = None
    # 保存更改
    await session.commit()

    return processor.last_end_time or start_date


async def update_all_workflow_times():
    """更新所有主流程工作流实例的时间"""
    async with AsyncSession(engine) as session:
        try:
            # 获取所有主流程工作流实例
            main_workflows = (
                (
                    await session.execute(
                        select(WorkflowInstance)
                        .join(WorkflowDefinition)
                        .filter(WorkflowDefinition.is_subprocess == False)
                    )
                )
                .scalars()
                .all()
            )
            main_workflows_ids = [workflow.id for workflow in main_workflows]

            for i, workflow_id in enumerate(main_workflows_ids):
                start_date = datetime(2024, 1, 1) + timedelta(days=30 * i)
                await process_workflow_nodes(session, workflow_id, start_date)

            print('所有工作流时间更新完成')

        except Exception as e:
            await session.rollback()
            print(f'更新工作流时间时发生错误: {str(e)}')
            import traceback

            print(traceback.format_exc())
            raise

        finally:
            await engine.dispose()


async def main():
    try:
        await update_all_workflow_times()
    except Exception as e:
        print(f'运行更新工作流时间时发生错误: {str(e)}')


if __name__ == '__main__':
    import asyncio

    asyncio.run(main())
