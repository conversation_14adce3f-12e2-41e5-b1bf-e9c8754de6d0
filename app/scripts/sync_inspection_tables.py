#!/usr/bin/env python3
"""
点检表同步脚本
用于定时遍历ERP base info中的所有ERP，为未创建点检表的项目基于当前模板自动创建点检表

使用方法:
1. 为所有ERP项目创建点检表（增量模式）:
   python sync_inspection_tables.py

2. 为指定项目创建点检表:
   python sync_inspection_tables.py --project-ids ERP001,ERP002

3. 只创建指定类型的点检表:
   python sync_inspection_tables.py --template-type 电气点检表

4. 全量同步模式（删除现有的再重新创建）:
   python sync_inspection_tables.py --sync-mode full

5. 查看帮助:
   python sync_inspection_tables.py --help

Crontab 示例:
# 每天凌晨2点执行点检表同步
0 2 * * * cd /path/to/app && python scripts/sync_inspection_tables.py >> logs/inspection_sync.log 2>&1
0 2 * * * docker run --rm -i *************:5000/wind-workflow:v1 python -m scripts.sync_inspection_tables >> /root/inspection_logs/inspection_sync_$(date +%Y%m%d).log 2>&1
"""
import asyncio
import sys
import os
import argparse
from datetime import datetime
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.base import get_db_session
from database.crud.inspection_management import (
    inspection_template,
    project_inspection,
)
from database.crud.info_management import info as info_service


async def sync_inspection_tables(
    project_ids: Optional[List[str]] = None,
    template_type: Optional[str] = None,
    sync_mode: str = 'incremental',
    created_by: str = 'system',
):
    """
    同步点检表

    Args:
        project_ids: 指定的项目ID列表，为空则处理所有ERP项目
        template_type: 指定的模板类型，为空则处理所有类型
        sync_mode: 同步模式，incremental(增量) 或 full(全量)
        created_by: 创建人
    """
    async with get_db_session() as db:
        try:
            # 获取要同步的项目列表
            if project_ids:
                print(f"处理指定项目: {', '.join(project_ids)}")
            else:
                # 获取所有ERP基础信息
                erp_info = await info_service.get_erp_info(
                    db, where_condition="bom_time > '2025-06-01'"
                )
                project_ids = [item['erp'] for item in erp_info if item['erp']]
                print(f'从ERP基础信息获取到 {len(project_ids)} 个项目')

            # 获取模板并提取ID列表，避免后续ORM访问
            if template_type:
                templates = await inspection_template.get_by_type(
                    db, template_type=template_type
                )
                print(f"获取到 {len(templates)} 个 '{template_type}' 类型的模板")
            else:
                templates = await inspection_template.get_active_templates(db)
                print(f'获取到 {len(templates)} 个活跃模板')

            if not templates:
                print('未找到可用的模板，退出同步')
                return

            # 提前提取template_ids，避免后续访问ORM对象时的greenlet问题
            template_ids = []
            for template in templates:
                try:
                    template_ids.append(template.id)
                except Exception as e:
                    print(f'Warning: Failed to get template id: {str(e)}')
                    continue

            if not template_ids:
                print('无法获取模板ID，退出同步')
                return

            print(f'成功提取 {len(template_ids)} 个模板ID')

            # 统计信息
            total_created = 0
            total_updated = 0
            total_deleted = 0
            success_count = 0
            error_count = 0

            print(f'开始同步，模式: {sync_mode}')
            print('-' * 50)

            for i, project_id in enumerate(project_ids, 1):
                try:
                    print(f'[{i}/{len(project_ids)}] 处理项目: {project_id}')

                    created_count = 0
                    updated_count = 0
                    deleted_count = 0

                    if sync_mode == 'full':
                        # 全量同步：先删除现有的，再重新创建
                        try:
                            if template_type:
                                deleted_count = await project_inspection.delete_by_project_and_type(
                                    db,
                                    project_id=project_id,
                                    inspection_type=template_type,
                                )
                            else:
                                deleted_count = (
                                    await project_inspection.delete_by_project(
                                        db, project_id=project_id
                                    )
                                )

                            if deleted_count > 0:
                                print(f'  删除了 {deleted_count} 个现有点检表项目')
                        except Exception as delete_error:
                            print(f'  删除操作失败: {str(delete_error)}')
                            deleted_count = 0

                        # 重新创建
                        try:
                            created_count = await project_inspection.pure_sql_batch_create_from_template_ids(
                                db,
                                project_id=project_id,
                                template_ids=template_ids,
                                created_by=created_by,
                            )
                        except Exception as create_error:
                            print(f'  创建操作失败: {str(create_error)}')
                            created_count = 0

                    else:
                        # 增量同步：只创建不存在的
                        try:
                            created_count = await project_inspection.pure_sql_batch_create_from_template_ids(
                                db,
                                project_id=project_id,
                                template_ids=template_ids,
                                created_by=created_by,
                            )
                        except Exception as create_error:
                            print(f'  创建操作失败: {str(create_error)}')
                            created_count = 0

                    if created_count > 0:
                        print(f'  创建了 {created_count} 个点检表项目')
                    else:
                        print('  无需创建新的点检表项目')

                    # 累计统计
                    total_created += created_count
                    total_updated += updated_count
                    total_deleted += deleted_count
                    success_count += 1

                except Exception as e:
                    error_count += 1
                    print(f'  错误: {str(e)}')
                    continue

            print('-' * 50)
            print('同步完成！')
            print(f'处理项目数: {len(project_ids)}')
            print(f'成功: {success_count}, 失败: {error_count}')
            print(
                f'创建: {total_created}, 更新: {total_updated}, 删除: {total_deleted}'
            )

        except Exception as e:
            print(f'同步失败: {str(e)}')
            import traceback

            traceback.print_exc()
            raise


async def list_templates():
    """列出所有可用的模板类型"""
    async with get_db_session() as db:
        try:
            types = await inspection_template.get_all_types(db)
            templates = await inspection_template.get_active_templates(db)

            print('可用的点检表模板类型:')
            print('-' * 30)
            for template_type in types:
                type_templates = [
                    t for t in templates if t.type == template_type
                ]
                print(f'{template_type}: {len(type_templates)} 个模板')

            print(f'\n总计: {len(templates)} 个活跃模板')

        except Exception as e:
            print(f'获取模板列表失败: {str(e)}')


async def show_project_summary(project_id: str):
    """显示项目点检表汇总"""
    async with get_db_session() as db:
        try:
            summary = await project_inspection.get_project_summary(
                db, project_id
            )
            print(f'项目 {project_id} 点检表汇总:')
            print('-' * 30)
            print(f"总数: {summary['total_count']}")
            print(f"已完成: {summary['completed_count']}")
            print(f"完成率: {summary['completion_rate']}%")
            print(f"类型: {', '.join(summary['types'])}")

        except Exception as e:
            print(f'获取项目汇总失败: {str(e)}')


async def run_with_proper_cleanup():
    """运行主逻辑并确保正确清理资源"""
    import sys

    parser = argparse.ArgumentParser(
        description='点检表同步脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    parser.add_argument(
        '--project-ids', type=str, help='指定项目ID列表，用逗号分隔，如: ERP001,ERP002'
    )

    parser.add_argument('--template-type', type=str, help='指定模板类型，如: 电气点检表')

    parser.add_argument(
        '--sync-mode',
        choices=['incremental', 'full'],
        default='incremental',
        help='同步模式: incremental(增量，默认) 或 full(全量)',
    )

    parser.add_argument(
        '--created-by', type=str, default='system', help='创建人，默认为 system'
    )

    parser.add_argument(
        '--list-templates', action='store_true', help='列出所有可用的模板类型'
    )

    parser.add_argument('--project-summary', type=str, help='显示指定项目的点检表汇总')

    args = parser.parse_args()

    print(f"点检表同步脚本 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print('=' * 60)

    try:
        if args.list_templates:
            await list_templates()
        elif args.project_summary:
            await show_project_summary(args.project_summary)
        else:
            project_ids = None
            if args.project_ids:
                project_ids = [
                    pid.strip() for pid in args.project_ids.split(',')
                ]

            await sync_inspection_tables(
                project_ids=project_ids,
                template_type=args.template_type,
                sync_mode=args.sync_mode,
                created_by=args.created_by,
            )
    except KeyboardInterrupt:
        print('\n脚本被用户中断')
        sys.exit(1)
    except Exception as e:
        print(f'脚本执行失败: {str(e)}')
        import traceback

        traceback.print_exc()
        sys.exit(1)


def main():
    """主入口函数"""
    try:
        asyncio.run(run_with_proper_cleanup())
    except KeyboardInterrupt:
        print('\n脚本被用户中断')
    except Exception as e:
        print(f'脚本启动失败: {str(e)}')
        import traceback

        traceback.print_exc()


if __name__ == '__main__':
    main()
