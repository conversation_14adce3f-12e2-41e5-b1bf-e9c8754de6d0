#!/usr/bin/env python3
"""
点检表模板变更同步脚本

当点检表模板发生变更时，自动同步到所有相关项目中。
支持的操作：
- 创建模板：为所有现有项目创建对应的点检表项目
- 更新模板：更新所有项目中对应的点检表项目
- 删除模板：删除所有项目中对应的点检表项目

使用示例：
python sync_template_changes.py --template-id 123 --action create
python sync_template_changes.py --template-id 123 --action update
python sync_template_changes.py --template-id 123 --action delete
python sync_template_changes.py --sync-all-templates
"""

import asyncio
import argparse
import sys
import os
from datetime import datetime
from typing import Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from database.base import get_db_session
from database.crud.inspection_management import (
    inspection_template,
    project_inspection,
)


async def sync_single_template(
    template_id: int, action: str, created_by: str = 'system'
) -> dict:
    """同步单个模板的变更"""
    async with get_db_session() as db:
        try:
            if action == 'create':
                # 新建模板同步
                result = (
                    await inspection_template.sync_to_all_projects_on_create(
                        db, template_id=template_id, created_by=created_by
                    )
                )
            else:
                # 更新或删除模板同步
                result = (
                    await project_inspection.sync_template_to_all_projects(
                        db,
                        template_id=template_id,
                        action=action,
                        created_by=created_by,
                    )
                )

            return result

        except Exception as e:
            return {
                'action': action,
                'template_id': template_id,
                'success': False,
                'error': str(e),
            }


async def sync_all_templates(created_by: str = 'system') -> dict:
    """同步所有活跃模板到所有项目"""
    async with get_db_session() as db:
        try:
            # 获取所有活跃模板
            templates = await inspection_template.get_active_templates(db)

            if not templates:
                return {
                    'action': 'sync_all',
                    'success': True,
                    'message': 'No active templates found',
                    'total_templates': 0,
                    'results': [],
                }

            # 提取模板ID
            template_ids = []
            for template in templates:
                try:
                    template_ids.append(template.id)
                except Exception as e:
                    print(f'Warning: Failed to get template id: {str(e)}')
                    continue

            if not template_ids:
                return {
                    'action': 'sync_all',
                    'success': False,
                    'message': 'Failed to extract template IDs',
                    'total_templates': 0,
                    'results': [],
                }

            # 使用批量创建方法
            created_count = await project_inspection.pure_sql_batch_create_from_template_ids(
                db,
                project_id='ALL_PROJECTS',  # 特殊标记，表示所有项目
                template_ids=template_ids,
                created_by=created_by,
            )

            return {
                'action': 'sync_all',
                'success': True,
                'message': f'Successfully synced {len(template_ids)} templates',
                'total_templates': len(template_ids),
                'created_count': created_count,
                'results': [],
            }

        except Exception as e:
            return {
                'action': 'sync_all',
                'success': False,
                'error': str(e),
                'total_templates': 0,
                'results': [],
            }


async def get_template_info(template_id: int) -> Optional[dict]:
    """获取模板信息"""
    async with get_db_session() as db:
        try:
            template = await inspection_template.get(db, id=template_id)
            if template:
                return {
                    'id': template.id,
                    'type': template.type,
                    'sequence_number': template.sequence_number,
                    'inspection_item': template.inspection_item,
                    'is_active': template.is_active,
                }
            return None
        except Exception as e:
            print(f'Error getting template info: {str(e)}')
            return None


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='点检表模板变更同步脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    parser.add_argument('--template-id', type=int, help='模板ID')

    parser.add_argument(
        '--action',
        choices=['create', 'update', 'delete'],
        help='同步操作类型: create(创建), update(更新), delete(删除)',
    )

    parser.add_argument(
        '--sync-all-templates', action='store_true', help='同步所有活跃模板到所有项目'
    )

    parser.add_argument(
        '--created-by', type=str, default='system', help='创建人，默认为 system'
    )

    parser.add_argument('--show-template-info', type=int, help='显示指定模板的信息')

    args = parser.parse_args()

    print(f"点检表模板同步脚本 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print('=' * 60)

    try:
        if args.show_template_info:
            # 显示模板信息
            template_info = await get_template_info(args.show_template_info)
            if template_info:
                print('模板信息:')
                for key, value in template_info.items():
                    print(f'  {key}: {value}')
            else:
                print(f'模板 {args.show_template_info} 不存在')

        elif args.sync_all_templates:
            # 同步所有模板
            print('开始同步所有活跃模板到所有项目...')
            result = await sync_all_templates(args.created_by)

            print('同步结果:')
            print(f"  操作: {result['action']}")
            print(f"  成功: {result['success']}")
            print(f"  模板总数: {result['total_templates']}")

            if result['success']:
                print(f"  创建数量: {result.get('created_count', 0)}")
                print(f"  消息: {result['message']}")
            else:
                print(f"  错误: {result.get('error', 'Unknown error')}")

        elif args.template_id and args.action:
            # 同步单个模板
            print(f'开始同步模板 {args.template_id} (操作: {args.action})...')

            # 先显示模板信息
            template_info = await get_template_info(args.template_id)
            if template_info:
                print(
                    f"模板信息: {template_info['type']} - {template_info['inspection_item']}"
                )
            else:
                print(f'警告: 模板 {args.template_id} 不存在或无法访问')

            result = await sync_single_template(
                args.template_id, args.action, args.created_by
            )

            print('同步结果:')
            for key, value in result.items():
                print(f'  {key}: {value}')

        else:
            parser.print_help()

    except KeyboardInterrupt:
        print('\n脚本被用户中断')
        sys.exit(1)
    except Exception as e:
        print(f'脚本执行失败: {str(e)}')
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
