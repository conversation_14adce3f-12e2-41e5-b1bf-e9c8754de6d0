stages:
  - build
  - deploy

workflow:
  rules:
    - if: '$CI_COMMIT_TAG'  # 仅在存在 Tag 时触发

# Build Docker Image job
build_docker_image:
  stage: build

  variables:
    DOCKER_HOST:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  # before_script:
  #   - docker info
  script:
    - echo "Creating docker package..."
    - ls -ll -h
    - docker buildx create --name build-images --driver docker-container --config buildkitd.toml
    - docker buildx use build-images
    - MAJOR_TAG=$(echo $CI_COMMIT_TAG | awk -F. '{print $1}')
    - MINOR_TAG=$(echo $CI_COMMIT_TAG | awk -F. '{print $1"."$2}')
    - PATCH_TAG=$CI_COMMIT_TAG
    - >
      docker buildx build --platform linux/amd64
      -t *************:5000/wind-workflow:latest
      -t *************:5000/wind-workflow:$MAJOR_TAG
      -t *************:5000/wind-workflow:$MINOR_TAG
      -t *************:5000/wind-workflow:$PATCH_TAG
      --push .
    - docker buildx rm build-images
  tags:
    - docker-runner
  rules:
    - if: '$CI_COMMIT_TAG'  # 仅在存在 Tag 时触发

deploy_to_cloud:
  stage: deploy
  before_script:
    - apt-get update -qq && apt-get install -y -qq sshpass
  script:
    - echo "Deploying to Kubernetes..."
    - export SSHPASS=LEADchina123@@
    - PATCH_TAG=$CI_COMMIT_TAG
    - >
      sshpass -e ssh -o StrictHostKeyChecking=no root@************* "
      cd wind-workflow/ && docker pull *************:5000/wind-workflow:v1 && docker compose down &&
      docker compose up -d && docker image prune -f
      "
    - echo "Deployment completed successfully"
  tags:
    - ubuntu-runner-22.04  # 使用 shell-runner
  rules:
    - if: '$CI_COMMIT_TAG'  # 仅在存在 Tag 时触发
