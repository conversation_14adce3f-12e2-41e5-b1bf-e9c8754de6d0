#!/usr/bin/env python3
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import aiomysql
import enum

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)


class NodeStatus(enum.Enum):
    pending = 'pending'
    active = 'active'
    completed = 'completed'
    error = 'error'
    overdue = 'overdue'


class NodeStatusUpdater:
    """节点状态更新器"""

    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.pool = None

    async def init_pool(self):
        if not self.pool:
            self.pool = await aiomysql.create_pool(**self.db_config)
        return self.pool

    async def close(self):
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()

    async def get_workflow_instances(self, project_id: str) -> List[Dict]:
        """获取项目的工作流实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    """
                    SELECT id, workflow_definition_id, status
                    FROM workflow_instances
                    WHERE project_id = %s
                    AND status NOT IN ('completed', 'terminated')
                    """,
                    (project_id,),
                )
                return await cursor.fetchall()

    async def find_node_instance(
        self, instance_id: int, node_name: str
    ) -> Optional[Dict]:
        """根据节点名称查找节点实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    """
                    SELECT ni.id, ni.status, ni.workflow_instance_id,
                           nd.name as node_name
                    FROM workflow_node_instances ni
                    JOIN workflow_node_definitions nd ON ni.node_definition_id = nd.id
                    WHERE ni.workflow_instance_id = %s
                    AND nd.name = %s
                    """,
                    (instance_id, node_name),
                )
                return await cursor.fetchone()

    async def update_node_status(
        self,
        node_instance_id: int,
        new_status: str,
        set_end_time: bool = False,
    ) -> bool:
        """更新节点状态"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    now = datetime.now()
                    if set_end_time:
                        await cursor.execute(
                            """
                            UPDATE workflow_node_instances
                            SET status = %s, end_time = %s, updated_at = %s
                            WHERE id = %s
                            """,
                            (new_status, now, now, node_instance_id),
                        )
                    else:
                        await cursor.execute(
                            """
                            UPDATE workflow_node_instances
                            SET status = %s, updated_at = %s
                            WHERE id = %s
                            """,
                            (new_status, now, node_instance_id),
                        )

                    await conn.commit()
                    logging.info(
                        f'节点实例 {node_instance_id} 状态已更新为: {new_status}'
                    )
                    return True

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'更新节点状态失败: {str(e)}')
                    return False

    async def update_project_node_status(
        self, project_id: str, node_name: str, new_status: str
    ) -> Tuple[int, int]:
        """更新项目中指定节点的状态"""
        try:
            # 获取项目的工作流实例
            instances = await self.get_workflow_instances(project_id)
            if not instances:
                logging.info(f'项目 {project_id} 没有活动的工作流实例')
                return 0, 0

            total_count = 0
            success_count = 0

            for instance in instances:
                # 查找节点实例
                node_instance = await self.find_node_instance(
                    instance['id'], node_name
                )
                if node_instance:
                    total_count += 1

                    # 判断是否需要设置结束时间
                    set_end_time = new_status in ['completed', 'error']

                    if await self.update_node_status(
                        node_instance['id'], new_status, set_end_time
                    ):
                        success_count += 1
                        logging.info(
                            f"成功更新项目 {project_id} 工作流实例 {instance['id']} "
                            f"中的节点 '{node_name}' 状态为 {new_status}"
                        )
                    else:
                        logging.error(
                            f"更新项目 {project_id} 工作流实例 {instance['id']} "
                            f"中的节点 '{node_name}' 失败"
                        )

            return success_count, total_count

        except Exception as e:
            logging.error(f'更新项目节点状态时发生错误: {str(e)}')
            return 0, 0


async def main():
    DB_CONFIG = {
        'host': '*************',
        'user': 'root',
        'password': 'leadchina',
        'db': 'IO',
        'autocommit': False,
    }

    updater = NodeStatusUpdater(DB_CONFIG)
    await updater.init_pool()

    try:
        # 获取用户输入
        project_id = input('请输入项目编号: ')
        node_name = input('请输入节点名称: ')

        # 显示可用的状态选项
        print('\n可用的状态选项:')
        for status in NodeStatus:
            print(f'- {status.value}')

        new_status = input('\n请输入新状态: ')

        # 验证状态是否有效
        if new_status not in [status.value for status in NodeStatus]:
            print(f"错误：无效的状态值'{new_status}'")
            return

        logging.info(f"开始更新项目 {project_id} 中的节点 '{node_name}' 状态...")
        success_count, total_count = await updater.update_project_node_status(
            project_id, node_name, new_status
        )

        if total_count == 0:
            logging.info(f"未找到项目 {project_id} 中的节点 '{node_name}'")
        else:
            logging.info(
                f'更新完成: {success_count}/{total_count} 个节点成功更新为 {new_status}'
            )

    finally:
        await updater.close()


if __name__ == '__main__':
    asyncio.run(main())
