#!/usr/bin/env python3
import asyncio
import logging
from typing import List
import aiomysql

# 配置日志
logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)


class WorkflowInstanceDeleter:
    """工作流实例删除器"""

    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.pool = None

    async def init_pool(self):
        """初始化连接池"""
        if not self.pool:
            self.pool = await aiomysql.create_pool(**self.db_config)
        return self.pool

    async def close(self):
        """关闭连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()

    async def get_workflow_instances(self, project_id: str) -> List[int]:
        """获取指定项目的所有工作流实例ID"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    SELECT id
                    FROM workflow_instances
                    WHERE project_id = %s
                    """,  # AND device_type = 'output_machine'
                    (project_id,),
                )
                results = await cursor.fetchall()
                return [row[0] for row in results]

    async def delete_workflow_instance(self, instance_id: int) -> bool:
        """删除单个工作流实例及其相关数据"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    # 1. 删除实例变量
                    await cursor.execute(
                        """
                        DELETE FROM workflow_instance_variables
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 2. 删除审批记录
                    await cursor.execute(
                        """
                        DELETE ar FROM workflow_approval_records ar
                        INNER JOIN workflow_node_instances ni ON ar.node_instance_id = ni.id
                        WHERE ni.workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 3. 删除变更日志
                    await cursor.execute(
                        """
                        DELETE FROM workflow_change_logs
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 4. 删除边实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_instances
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 5. 删除节点实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_node_instances
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 6. 最后删除工作流实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_instances
                        WHERE id = %s
                        """,
                        (instance_id,),
                    )

                    await conn.commit()
                    logging.info(
                        f'Successfully deleted workflow instance {instance_id}'
                    )
                    return True

                except Exception as e:
                    await conn.rollback()
                    logging.error(
                        f'Failed to delete workflow instance {instance_id}: {str(e)}'
                    )
                    return False


async def delete_project_workflows(project_id: str, db_config: dict):
    """删除指定项目的所有工作流实例"""
    deleter = WorkflowInstanceDeleter(db_config)
    await deleter.init_pool()

    try:
        # 获取所有相关的工作流实例
        instance_ids = await deleter.get_workflow_instances(project_id)

        if not instance_ids:
            logging.info(
                f'No workflow instances found for project {project_id}'
            )
            return

        logging.info(
            f'Found {len(instance_ids)} workflow instances for project {project_id}'
        )

        # 删除每个工作流实例
        success_count = 0
        for instance_id in instance_ids:
            if await deleter.delete_workflow_instance(instance_id):
                success_count += 1

        logging.info(
            f'Successfully deleted {success_count}/{len(instance_ids)} workflow instances'
        )

    finally:
        await deleter.close()


async def main():
    # 数据库配置
    DB_CONFIG = {
        'host': '*************',
        'user': 'root',
        'password': 'leadchina',
        'db': 'IO',
        'autocommit': False,
    }

    # 要删除的项目ID
    project_id = input('请输入要删除的项目编号: ')

    await delete_project_workflows(project_id, DB_CONFIG)


if __name__ == '__main__':
    asyncio.run(main())
