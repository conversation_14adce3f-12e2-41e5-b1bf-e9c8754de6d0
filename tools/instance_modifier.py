#!/usr/bin/env python3
import asyncio
import logging
from datetime import datetime
from typing import List, Dict
import aiomysql

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)


class WorkflowSynchronizer:
    """工作流同步器 - 用于将指定工作流定义的实例同步到新定义"""

    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.pool = None

    async def init_pool(self):
        if not self.pool:
            self.pool = await aiomysql.create_pool(**self.db_config)
        return self.pool

    async def close(self):
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()

    async def get_workflow_instances_by_project(
        self, project_id: str
    ) -> List[Dict]:
        """获取指定项目的工作流实例信息"""
        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    """
                    SELECT id, workflow_definition_id, status
                    FROM workflow_instances
                    WHERE project_id = %s
                    AND workflow_definition_id IN (1, 2, 3)
                    """,  # AND device_type = 'output_machine'
                    (project_id,),
                )
                return await cursor.fetchall()

    async def sync_instance_nodes(
        self, instance_id: int, workflow_def_id: int, status: str
    ) -> bool:
        """同步工作流实例的节点"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    # 获取新定义中的所有节点
                    await cursor.execute(
                        """
                        SELECT id, name, type
                        FROM workflow_node_definitions
                        WHERE workflow_definition_id = %s
                        """,
                        (workflow_def_id,),
                    )
                    new_nodes = await cursor.fetchall()

                    now = datetime.now()
                    node_map = {}  # 存储新节点ID映射

                    # 处理每个新节点
                    for node_def_id, node_name, node_type in new_nodes:
                        # 检查节点是否已存在
                        await cursor.execute(
                            """
                            SELECT id FROM workflow_node_instances
                            WHERE workflow_instance_id = %s
                            AND node_definition_id = %s
                            """,
                            (instance_id, node_def_id),
                        )
                        existing = await cursor.fetchone()

                        if not existing:
                            # 创建新节点实例
                            if status == 'pending':
                                await cursor.execute(
                                    """
                                    INSERT INTO workflow_node_instances
                                    (workflow_instance_id, node_definition_id, status,
                                    created_at)
                                    VALUES (%s, %s, %s, %s)
                                    """,
                                    (instance_id, node_def_id, status, now),
                                )
                            elif status == 'active':
                                await cursor.execute(
                                    """
                                    INSERT INTO workflow_node_instances
                                    (workflow_instance_id, node_definition_id, status,
                                    created_at, start_time)
                                    VALUES (%s, %s, %s, %s, %s)
                                    """,
                                    (
                                        instance_id,
                                        node_def_id,
                                        status,
                                        now,
                                        now,
                                    ),
                                )
                            else:
                                await cursor.execute(
                                    """
                                    INSERT INTO workflow_node_instances
                                    (workflow_instance_id, node_definition_id, status,
                                    created_at, start_time, end_time)
                                    VALUES (%s, %s, %s, %s, %s, %s)
                                    """,
                                    (
                                        instance_id,
                                        node_def_id,
                                        status,
                                        now,
                                        now,
                                        now,
                                    ),
                                )
                            node_map[node_def_id] = cursor.lastrowid
                        else:
                            node_map[node_def_id] = existing[0]

                    # 删除旧的边实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_instances
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 创建新的边实例
                    await cursor.execute(
                        """
                        SELECT id, from_node_id, to_node_id
                        FROM workflow_edge_definitions
                        WHERE workflow_definition_id = %s
                        """,
                        (workflow_def_id,),
                    )
                    edges = await cursor.fetchall()

                    for edge_id, from_node_id, to_node_id in edges:
                        if from_node_id in node_map and to_node_id in node_map:
                            await cursor.execute(
                                """
                                INSERT INTO workflow_edge_instances
                                (workflow_instance_id, edge_definition_id,
                                 from_node_instance_id, to_node_instance_id,
                                 created_at)
                                VALUES (%s, %s, %s, %s, %s)
                                """,
                                (
                                    instance_id,
                                    edge_id,
                                    node_map[from_node_id],
                                    node_map[to_node_id],
                                    now,
                                ),
                            )

                    await conn.commit()
                    return True

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'同步实例 {instance_id} 失败: {str(e)}')
                    return False

    async def sync_project_instances(
        self, project_id: str, status: str
    ) -> bool:
        """同步指定项目的所有工作流实例"""
        try:
            instances = await self.get_workflow_instances_by_project(
                project_id
            )

            if not instances:
                logging.info(f'项目 {project_id} 没有需要同步的工作流实例')
                return True

            success_count = 0
            total_count = len(instances)

            for instance in instances:
                instance_id = instance['id']
                workflow_def_id = instance['workflow_definition_id']

                logging.info(
                    f'正在同步项目 {project_id} 的工作流实例 {instance_id} (定义ID: {workflow_def_id})'
                )

                if await self.sync_instance_nodes(
                    instance_id, workflow_def_id, status
                ):
                    success_count += 1
                    logging.info(f'实例 {instance_id} 同步成功')
                else:
                    logging.error(f'实例 {instance_id} 同步失败')

            logging.info(
                f'项目 {project_id} 同步完成: {success_count}/{total_count} 个实例成功'
            )
            return success_count == total_count

        except Exception as e:
            logging.error(f'同步项目 {project_id} 时发生错误: {str(e)}')
            return False


async def main():
    DB_CONFIG = {
        'host': '*************',
        'user': 'root',
        'password': 'leadchina',
        'db': 'IO',
        'autocommit': False,
    }

    synchronizer = WorkflowSynchronizer(DB_CONFIG)
    await synchronizer.init_pool()

    try:
        # 获取用户输入的项目编号
        project_id = input('请输入要同步的项目编号: ')
        status = input('请输入要同步的新节点状态(pending, completed, active): ')

        logging.info(f'开始同步项目 {project_id} 的工作流实例...')
        success = await synchronizer.sync_project_instances(project_id, status)

        if success:
            logging.info(f'项目 {project_id} 的所有工作流实例同步完成')
        else:
            logging.error(f'项目 {project_id} 的部分工作流实例同步失败')

    finally:
        await synchronizer.close()


if __name__ == '__main__':
    asyncio.run(main())
