#!/usr/bin/env python3
import asyncio
import aiomysql
import logging
from typing import Dict, Optional

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)


class WorkflowDefinitionModifier:
    """使用名称操作的工作流定义修改器"""

    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.pool = None

    async def init_pool(self):
        if not self.pool:
            self.pool = await aiomysql.create_pool(**self.db_config)
        return self.pool

    async def close(self):
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()

    async def get_node_id_by_name(
        self, workflow_def_id: int, node_name: str
    ) -> Optional[int]:
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    SELECT id FROM workflow_node_definitions
                    WHERE workflow_definition_id = %s AND name = %s
                    """,
                    (workflow_def_id, node_name),
                )
                result = await cursor.fetchone()
                return result[0] if result else None

    async def get_edge_id_by_nodes(
        self, workflow_def_id: int, from_node_id: int, to_node_id: int
    ) -> Optional[int]:
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    SELECT id FROM workflow_edge_definitions
                    WHERE workflow_definition_id = %s
                    AND from_node_id = %s
                    AND to_node_id = %s
                    """,
                    (workflow_def_id, from_node_id, to_node_id),
                )
                result = await cursor.fetchone()
                return result[0] if result else None

    async def delete_edge_instances(self, edge_def_id: int) -> bool:
        """删除边实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_instances
                        WHERE edge_definition_id = %s
                        """,
                        (edge_def_id,),
                    )
                    await conn.commit()
                    return True
                except Exception as e:
                    await conn.rollback()
                    logging.error(f'Failed to delete edge instances: {str(e)}')
                    return False

    async def add_node_definition_by_name(
        self, workflow_def_id: int, node_data: Dict
    ) -> Optional[int]:
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    existing_id = await self.get_node_id_by_name(
                        workflow_def_id, node_data['name']
                    )
                    if existing_id:
                        logging.warning(
                            f"Node with name '{node_data['name']}' already exists with ID: {existing_id}"
                        )
                        return existing_id

                    node_def_sql = """
                        INSERT INTO workflow_node_definitions
                        (workflow_definition_id, name, type, color, subprocess_id,
                         task_url, need_approval, input_logic, status_update_mode, status_query)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    await cursor.execute(
                        node_def_sql,
                        (
                            workflow_def_id,
                            node_data['name'],
                            node_data['type'],
                            node_data.get('color'),
                            node_data.get('subprocess_id'),
                            node_data.get('task_url'),
                            node_data.get('need_approval', False),
                            node_data.get('input_logic', 'AND'),
                            node_data.get('status_update_mode', 'manual'),
                            node_data.get('status_query'),
                        ),
                    )
                    node_def_id = cursor.lastrowid
                    await conn.commit()
                    logging.info(
                        f"Added node definition '{node_data['name']}' with ID: {node_def_id}"
                    )
                    return node_def_id

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'Failed to add node definition: {str(e)}')
                    return None

    async def delete_edge_by_node_names(
        self, workflow_def_id: int, from_node_name: str, to_node_name: str
    ) -> bool:
        try:
            from_node_id = await self.get_node_id_by_name(
                workflow_def_id, from_node_name
            )
            to_node_id = await self.get_node_id_by_name(
                workflow_def_id, to_node_name
            )

            if not from_node_id or not to_node_id:
                logging.error(
                    f'Could not find nodes: {from_node_name} -> {to_node_name}'
                )
                return False

            edge_id = await self.get_edge_id_by_nodes(
                workflow_def_id, from_node_id, to_node_id
            )
            if not edge_id:
                logging.error(
                    f'Could not find edge between nodes: {from_node_name} -> {to_node_name}'
                )
                return False

            # 先删除边实例
            if await self.delete_edge_instances(edge_id):
                # 再删除边定义
                return await self.delete_edge_definition(
                    workflow_def_id, edge_id
                )
            return False
        except Exception as e:
            logging.error(f'Failed to delete edge by node names: {str(e)}')
            return False

    async def add_edge_by_node_names(
        self,
        workflow_def_id: int,
        from_node_name: str,
        to_node_name: str,
        condition: str = 'default',
    ) -> Optional[int]:
        try:
            from_node_id = await self.get_node_id_by_name(
                workflow_def_id, from_node_name
            )
            to_node_id = await self.get_node_id_by_name(
                workflow_def_id, to_node_name
            )

            if not from_node_id or not to_node_id:
                logging.error(
                    f'Could not find nodes: {from_node_name} -> {to_node_name}'
                )
                return None

            edge_data = {
                'from_node_id': from_node_id,
                'to_node_id': to_node_id,
                'condition': condition,
            }

            return await self.add_edge_definition(workflow_def_id, edge_data)
        except Exception as e:
            logging.error(f'Failed to add edge by node names: {str(e)}')
            return None

    async def delete_edge_definition(
        self, workflow_def_id: int, edge_def_id: int
    ) -> bool:
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_definitions
                        WHERE id = %s AND workflow_definition_id = %s
                        """,
                        (edge_def_id, workflow_def_id),
                    )
                    await conn.commit()
                    logging.info(
                        f'Deleted edge definition with ID: {edge_def_id}'
                    )
                    return True
                except Exception as e:
                    await conn.rollback()
                    logging.error(
                        f'Failed to delete edge definition: {str(e)}'
                    )
                    return False

    async def add_edge_definition(
        self, workflow_def_id: int, edge_data: Dict
    ) -> Optional[int]:
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    await cursor.execute(
                        """
                        INSERT INTO workflow_edge_definitions
                        (workflow_definition_id, from_node_id, to_node_id, `condition`)
                        VALUES (%s, %s, %s, %s)
                        """,
                        (
                            workflow_def_id,
                            edge_data['from_node_id'],
                            edge_data['to_node_id'],
                            edge_data.get('condition', 'default'),
                        ),
                    )
                    edge_def_id = cursor.lastrowid
                    await conn.commit()
                    logging.info(
                        f'Added edge definition with ID: {edge_def_id}'
                    )
                    return edge_def_id
                except Exception as e:
                    await conn.rollback()
                    logging.error(f'Failed to add edge definition: {str(e)}')
                    return None

    async def delete_node_edge_instances(self, node_id: int) -> bool:
        """删除与节点相关的所有边实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    # 获取所有相关的边定义ID
                    await cursor.execute(
                        """
                        SELECT id FROM workflow_edge_definitions
                        WHERE from_node_id = %s OR to_node_id = %s
                        """,
                        (node_id, node_id),
                    )
                    edge_def_ids = [row[0] for row in await cursor.fetchall()]

                    if edge_def_ids:
                        # 删除这些边定义相关的所有边实例
                        await cursor.execute(
                            """
                            DELETE FROM workflow_edge_instances
                            WHERE edge_definition_id IN %s
                            """,
                            (tuple(edge_def_ids),),
                        )

                    await conn.commit()
                    return True
                except Exception as e:
                    await conn.rollback()
                    logging.error(
                        f'Failed to delete node edge instances: {str(e)}'
                    )
                    return False

    async def delete_node_edge_definitions(
        self, workflow_def_id: int, node_id: int
    ) -> bool:
        """删除与节点相关的所有边定义"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_definitions
                        WHERE workflow_definition_id = %s
                        AND (from_node_id = %s OR to_node_id = %s)
                        """,
                        (workflow_def_id, node_id, node_id),
                    )
                    await conn.commit()
                    return True
                except Exception as e:
                    await conn.rollback()
                    logging.error(
                        f'Failed to delete node edge definitions: {str(e)}'
                    )
                    return False

    async def delete_node_instances(self, node_id: int) -> bool:
        """删除节点的所有实例记录"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    # 先删除节点实例相关的变量
                    await cursor.execute(
                        """
                        DELETE v FROM workflow_instance_variables v
                        INNER JOIN workflow_node_instances n ON v.node_instance_id = n.id
                        WHERE n.node_definition_id = %s
                        """,
                        (node_id,),
                    )

                    # 删除审批记录
                    await cursor.execute(
                        """
                        DELETE a FROM workflow_approval_records a
                        INNER JOIN workflow_node_instances n ON a.node_instance_id = n.id
                        WHERE n.node_definition_id = %s
                        """,
                        (node_id,),
                    )

                    # 删除节点实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_node_instances
                        WHERE node_definition_id = %s
                        """,
                        (node_id,),
                    )

                    await conn.commit()
                    return True
                except Exception as e:
                    await conn.rollback()
                    logging.error(f'Failed to delete node instances: {str(e)}')
                    return False

    async def delete_node_machine_durations(self, node_id: int) -> bool:
        """删除节点的机器类型时长配置"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    await cursor.execute(
                        """
                        DELETE FROM workflow_machine_type_durations
                        WHERE node_definition_id = %s
                        """,
                        (node_id,),
                    )
                    await conn.commit()
                    return True
                except Exception as e:
                    await conn.rollback()
                    logging.error(
                        f'Failed to delete node machine durations: {str(e)}'
                    )
                    return False

    async def delete_node_definition(
        self, workflow_def_id: int, node_id: int
    ) -> bool:
        """删除节点定义"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    await cursor.execute(
                        """
                        DELETE FROM workflow_node_definitions
                        WHERE id = %s AND workflow_definition_id = %s
                        """,
                        (node_id, workflow_def_id),
                    )
                    await conn.commit()
                    return True
                except Exception as e:
                    await conn.rollback()
                    logging.error(
                        f'Failed to delete node definition: {str(e)}'
                    )
                    return False

    async def delete_node_by_name(
        self, workflow_def_id: int, node_name: str
    ) -> bool:
        """通过节点名称删除节点及其所有相关数据"""
        try:
            # 获取节点ID
            node_id = await self.get_node_id_by_name(
                workflow_def_id, node_name
            )
            if not node_id:
                logging.error(f'Node not found: {node_name}')
                return False

            # 按顺序删除所有相关数据
            if not await self.delete_node_edge_instances(node_id):
                return False

            if not await self.delete_node_edge_definitions(
                workflow_def_id, node_id
            ):
                return False

            if not await self.delete_node_instances(node_id):
                return False

            if not await self.delete_node_machine_durations(node_id):
                return False

            if not await self.delete_node_definition(workflow_def_id, node_id):
                return False

            logging.info(f'Successfully deleted node: {node_name}')
            return True

        except Exception as e:
            logging.error(f'Failed to delete node by name: {str(e)}')
            return False


async def main():
    DB_CONFIG = {
        'host': '*************',
        'user': 'root',
        'password': 'leadchina',
        'db': 'IO',
        'autocommit': False,
    }

    modifier = WorkflowDefinitionModifier(DB_CONFIG)
    await modifier.init_pool()

    try:

        # 删除旧节点
        # await modifier.delete_node_by_name(3, '11-8.是否批量机>5台')
        # await modifier.delete_node_by_name(3, "11-12.终版底板图")

        # 示例：添加新节点
        # nodes_to_add = [
        #     {
        #         'workflow_id': 1,
        #         'node_data': {'name': '等待装配', 'type': 'process'},
        #     },
        # ]

        # for node in nodes_to_add:
        #     node_id = await modifier.add_node_definition_by_name(
        #         node['workflow_id'], node['node_data']
        #     )
        #     if node_id:
        #         logging.info(
        #             f"Successfully added node: {node['node_data']['name']}"
        #         )

        # # 删除节点
        # await modifier.delete_node_by_name(2, '生成EtherCat伺服节点')

        # 删除旧边
        # await modifier.delete_edge_by_node_names(2, '8-4.底板图点检', '8-3.底板排版')
        # await modifier.delete_edge_by_node_names(2, '8-5.底板排版评审', '8-3.底板排版')
        # await modifier.delete_edge_by_node_names(2, '8-3.底板排版', '8-5.底板排版评审')
        # await modifier.delete_edge_by_node_names(1, "总装部件梳理", "电气底板钻孔图")
        # await modifier.delete_edge_by_node_names(1, "部件完整信息录入", "机电沟通表核查")
        # await modifier.delete_edge_by_node_names(1, "机电沟通表核查", "EM信息录入")
        # await modifier.delete_edge_by_node_names(1, "IO表入库+IO表核对", "技术开发负责人审核")
        # await modifier.delete_edge_by_node_names(1, "技术开发负责人审核", "硬件负责人审核")
        # await modifier.delete_edge_by_node_names(1, "程序检查，程序上传GIT", "仿真调试")
        # await modifier.delete_edge_by_node_names(1, "程序检查，程序上传GIT", "厂内调试")
        # await modifier.delete_edge_by_node_names(1, '总装部件梳理', '机型负责人录入版本信息')
        # await modifier.delete_edge_by_node_names(1, "Elpan图纸IO点位表校验", "仿真调试")
        # await modifier.delete_edge_by_node_names(1, "原理图纸自动生成投料和图纸下发", "硬件开发负责人审核")

        # 添加新边
        await modifier.add_edge_by_node_names(3, '11-7.原理图完成下发', '11-8.柜内布线')
        await modifier.add_edge_by_node_names(3, '11-7.原理图完成下发', '11-9.柜外布线')
        # await modifier.add_edge_by_node_names(2, '8-3.底板排版', '8-4.底板图点检')
        # await modifier.add_edge_by_node_names(2, '8-4.底板图点检', '8-5.底板排版评审')
        # await modifier.add_edge_by_node_names(1, '仿真调试', '等待装配')
        # await modifier.add_edge_by_node_names(1, '等待装配', '厂内调试')
        # await modifier.add_edge_by_node_names(1, '三维搭建', '仿真调试')
        # await modifier.add_edge_by_node_names(1, '部件伺服信息录入', '机电沟通表核查')
        # await modifier.add_edge_by_node_names(1, 'EM准备', 'PLC/HMI程序准备')
        # await modifier.add_edge_by_node_names(1, 'EM准备', 'IO规划')
        # await modifier.add_edge_by_node_names(1, '部件完整信息录入', 'IO规划')
        # await modifier.add_edge_by_node_names(1, '气路设计', 'IO规划')
        # await modifier.add_edge_by_node_names(1, '机电沟通表核查', 'IO规划')
        # await modifier.add_edge_by_node_names(1, 'IO规划', '电气原理图+投料前检查+投料')
        # await modifier.add_edge_by_node_names(1, "IO表入库+IO表核对", "硬件负责人审核")
        # await modifier.add_edge_by_node_names(1, "程序检查，程序上传GIT", "技术开发负责人审核")
        # await modifier.add_edge_by_node_names(1, "技术开发负责人审核", "仿真调试")
        # await modifier.add_edge_by_node_names(1, '总装部件梳理', '部件完整信息录入')
        # await modifier.add_edge_by_node_names(1, '总装部件梳理', '气路设计')
        # await modifier.add_edge_by_node_names(1, '总装部件梳理', '机型负责人录入版本信息')
        # await modifier.add_edge_by_node_names(1, '程序检查，程序上传GIT', '仿真调试')
        # await modifier.add_edge_by_node_names(1, '硬件负责人审核', '仿真调试')
        # await modifier.add_edge_by_node_names(1, '仿真调试', '厂内调试')
        # await modifier.add_edge_by_node_names(1, "部件完整信息录入", "机电沟通表入库")
        # await modifier.add_edge_by_node_names(1, '部件伺服信息录入', 'IO表入库')

        # await modifier.add_edge_by_node_names(3, "图纸完成下发", "IO表入库(原理图生成)")
        # await modifier.add_edge_by_node_names(3, "IO表入库(原理图生成)", "8Elpan图纸IO点位表校验")
        # await modifier.add_edge_by_node_names(3, "8Elpan图纸IO点位表校验", "图纸完成")

    finally:
        await modifier.close()


if __name__ == '__main__':
    asyncio.run(main())
