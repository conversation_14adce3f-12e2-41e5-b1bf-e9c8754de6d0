#!/usr/bin/env python3
import asyncio
import logging
from datetime import datetime
from typing import List, Dict
import aiomysql

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)


class CompletedProjectSynchronizer:
    """已完成项目工作流同步器 - 自动找到所有节点为completed状态的项目并进行适配"""

    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.pool = None

    async def init_pool(self):
        if not self.pool:
            self.pool = await aiomysql.create_pool(**self.db_config)
        return self.pool

    async def close(self):
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()

    async def get_all_completed_projects(self) -> List[str]:
        """获取所有节点都是completed状态的项目"""
        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                # 查询所有有工作流实例的项目
                await cursor.execute(
                    """
                    SELECT DISTINCT project_id
                    FROM workflow_instances
                    """
                )
                all_projects = await cursor.fetchall()

                completed_projects = []

                for project in all_projects:
                    project_id = project['project_id']

                    # 检查该项目是否所有节点都是completed状态
                    if await self.is_project_all_completed(project_id):
                        completed_projects.append(project_id)
                        logging.info(f'发现已完成项目: {project_id}')

                return completed_projects

    async def is_project_all_completed(self, project_id: str) -> bool:
        """检查项目的所有工作流实例是否都是completed状态"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 获取该项目的所有工作流实例状态
                await cursor.execute(
                    """
                    SELECT COUNT(*) as total_instances,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_instances
                    FROM workflow_instances
                    WHERE project_id = %s
                    """,
                    (project_id,),
                )
                result = await cursor.fetchone()
                total_instances, completed_instances = result

                # 如果没有实例或者不是所有实例都完成，返回False
                if (
                    total_instances == 0
                    or completed_instances != total_instances
                ):
                    return False

                return True

    async def get_workflow_instances_by_project(
        self, project_id: str
    ) -> List[Dict]:
        """获取指定项目的工作流实例信息"""
        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    """
                    SELECT id, workflow_definition_id, status
                    FROM workflow_instances
                    WHERE project_id = %s
                    """,
                    (project_id,),
                )
                return await cursor.fetchall()

    async def sync_instance_nodes(
        self, instance_id: int, workflow_def_id: int, status: str
    ) -> bool:
        """同步工作流实例的节点"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    # 获取新定义中的所有节点
                    await cursor.execute(
                        """
                        SELECT id, name, type
                        FROM workflow_node_definitions
                        WHERE workflow_definition_id = %s
                        """,
                        (workflow_def_id,),
                    )
                    new_nodes = await cursor.fetchall()

                    now = datetime.now()
                    node_map = {}  # 存储新节点ID映射

                    # 处理每个新节点
                    for node_def_id, node_name, node_type in new_nodes:
                        # 检查节点是否已存在
                        await cursor.execute(
                            """
                            SELECT id FROM workflow_node_instances
                            WHERE workflow_instance_id = %s
                            AND node_definition_id = %s
                            """,
                            (instance_id, node_def_id),
                        )
                        existing = await cursor.fetchone()

                        if not existing:
                            # 创建新节点实例，由于都是completed状态的项目，直接设为completed
                            await cursor.execute(
                                """
                                INSERT INTO workflow_node_instances
                                (workflow_instance_id, node_definition_id, status,
                                created_at, start_time, end_time)
                                VALUES (%s, %s, %s, %s, %s, %s)
                                """,
                                (
                                    instance_id,
                                    node_def_id,
                                    'completed',
                                    now,
                                    now,
                                    now,
                                ),
                            )
                            node_map[node_def_id] = cursor.lastrowid
                        else:
                            node_map[node_def_id] = existing[0]

                    # 删除旧的边实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_instances
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 创建新的边实例
                    await cursor.execute(
                        """
                        SELECT id, from_node_id, to_node_id
                        FROM workflow_edge_definitions
                        WHERE workflow_definition_id = %s
                        """,
                        (workflow_def_id,),
                    )
                    edges = await cursor.fetchall()

                    for edge_id, from_node_id, to_node_id in edges:
                        if from_node_id in node_map and to_node_id in node_map:
                            await cursor.execute(
                                """
                                INSERT INTO workflow_edge_instances
                                (workflow_instance_id, edge_definition_id,
                                 from_node_instance_id, to_node_instance_id,
                                 created_at)
                                VALUES (%s, %s, %s, %s, %s)
                                """,
                                (
                                    instance_id,
                                    edge_id,
                                    node_map[from_node_id],
                                    node_map[to_node_id],
                                    now,
                                ),
                            )

                    await conn.commit()
                    return True

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'同步实例 {instance_id} 失败: {str(e)}')
                    return False

    async def sync_project_instances(self, project_id: str) -> bool:
        """同步指定项目的所有工作流实例（固定为completed状态）"""
        try:
            instances = await self.get_workflow_instances_by_project(
                project_id
            )

            if not instances:
                logging.info(f'项目 {project_id} 没有需要同步的工作流实例')
                return True

            success_count = 0
            total_count = len(instances)

            for instance in instances:
                instance_id = instance['id']
                workflow_def_id = instance['workflow_definition_id']

                logging.info(
                    f'正在同步项目 {project_id} 的工作流实例 {instance_id} (定义ID: {workflow_def_id}) - 状态: completed'
                )

                if await self.sync_instance_nodes(
                    instance_id, workflow_def_id, 'completed'
                ):
                    success_count += 1
                else:
                    logging.error(f'实例 {instance_id} 同步失败')

            logging.info(
                f'项目 {project_id} 同步完成: {success_count}/{total_count} 个实例成功'
            )
            return success_count == total_count

        except Exception as e:
            logging.error(f'同步项目 {project_id} 时发生错误: {str(e)}')
            return False

    async def sync_all_completed_projects(self) -> Dict[str, str]:
        """同步所有已完成的项目"""
        completed_projects = await self.get_all_completed_projects()

        if not completed_projects:
            logging.info('没有发现所有节点都是completed状态的项目')
            return {}

        results = {}

        logging.info(f'发现 {len(completed_projects)} 个已完成项目，开始批量同步...')
        print('\n=== 发现的已完成项目列表 ===')
        for i, project_id in enumerate(completed_projects, 1):
            print(f'{i}. {project_id}')
        print('=' * 40)

        for project_id in completed_projects:
            logging.info(f'\n开始同步项目: {project_id}')
            success = await self.sync_project_instances(project_id)
            results[project_id] = 'success' if success else 'failed'

        return results


async def main():
    DB_CONFIG = {
        'host': '*************',
        'user': 'root',
        'password': 'leadchina',
        'db': 'IO',
        'autocommit': False,
    }

    synchronizer = CompletedProjectSynchronizer(DB_CONFIG)
    await synchronizer.init_pool()

    try:
        logging.info('开始检索所有节点都是completed状态的项目...')
        results = await synchronizer.sync_all_completed_projects()

        # 打印最终结果
        print('\n=== 同步结果汇总 ===')
        if results:
            success_projects = [
                pid for pid, status in results.items() if status == 'success'
            ]
            failed_projects = [
                pid for pid, status in results.items() if status == 'failed'
            ]

            print(f'总计处理项目: {len(results)} 个')
            print(f'成功同步: {len(success_projects)} 个')
            print(f'同步失败: {len(failed_projects)} 个')

            if success_projects:
                print('\n成功同步的项目:')
                for i, project_id in enumerate(success_projects, 1):
                    print(f'  {i}. {project_id}')

            if failed_projects:
                print('\n同步失败的项目:')
                for i, project_id in enumerate(failed_projects, 1):
                    print(f'  {i}. {project_id}')
        else:
            print('没有发现需要同步的已完成项目')

        print('=' * 40)
        logging.info('批量同步任务完成')

    except Exception as e:
        logging.error(f'执行过程中发生错误: {str(e)}')
    finally:
        await synchronizer.close()


if __name__ == '__main__':
    asyncio.run(main())
