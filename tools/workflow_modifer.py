import aiomysql
import logging
from typing import Dict, Optional
from datetime import datetime
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)

# 数据库配置 root:leadchina@*************:3306/IO
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'leadchina',
    'db': 'IO',
    'autocommit': False,  # 使用事务
}


class WorkflowModifier:
    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.pool = None

    async def init_pool(self):
        """初始化连接池"""
        if not self.pool:
            self.pool = await aiomysql.create_pool(**self.db_config)
        return self.pool

    async def add_node(
        self, workflow_def_id: int, node_data: Dict
    ) -> Optional[int]:
        """添加新节点到工作流定义和所有相关实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()  # 开始事务

                    # 1. 添加节点定义
                    node_def_sql = """
                        INSERT INTO workflow_node_definitions
                        (workflow_definition_id, name, type, color, subprocess_id,
                         task_url, need_approval, input_logic, status_update_mode, status_query)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    await cursor.execute(
                        node_def_sql,
                        (
                            workflow_def_id,
                            node_data['name'],
                            node_data['type'],
                            node_data.get('color'),
                            node_data.get('subprocess_id'),
                            node_data.get('task_url'),
                            node_data.get('need_approval', False),
                            node_data.get('input_logic', 'AND'),
                            node_data.get('status_update_mode', 'manual'),
                            node_data.get('status_query'),
                        ),
                    )
                    node_def_id = cursor.lastrowid

                    # 2. 获取所有工作流实例及其状态
                    await cursor.execute(
                        """
                        SELECT id, status FROM workflow_instances
                        WHERE workflow_definition_id = %s
                    """,
                        (workflow_def_id,),
                    )
                    instances = await cursor.fetchall()

                    # 3. 为每个实例创建节点实例，已完成的流程设置节点为完成状态
                    now = datetime.now()
                    for (instance_id, instance_status) in instances:
                        status = (
                            'completed'
                            if instance_status == 'completed'
                            else 'pending'
                        )
                        end_time = now if status == 'completed' else None

                        await cursor.execute(
                            """
                            INSERT INTO workflow_node_instances
                            (workflow_instance_id, node_definition_id, status, start_time, end_time)
                            VALUES (%s, %s, %s, %s, %s)
                        """,
                            (instance_id, node_def_id, status, now, end_time),
                        )

                    await conn.commit()
                    return node_def_id

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'添加节点失败: {str(e)}')
                    return None

    async def delete_node(
        self, workflow_def_id: int, node_def_id: int
    ) -> bool:
        """删除工作流中的节点及其关联的所有实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    # 1. 删除相关的边实例
                    await cursor.execute(
                        """
                        DELETE ei FROM workflow_edge_instances ei
                        JOIN workflow_edge_definitions ed ON ei.edge_definition_id = ed.id
                        WHERE ed.from_node_id = %s OR ed.to_node_id = %s
                    """,
                        (node_def_id, node_def_id),
                    )

                    # 2. 删除边定义
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_definitions
                        WHERE from_node_id = %s OR to_node_id = %s
                    """,
                        (node_def_id, node_def_id),
                    )

                    # 3. 删除节点实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_node_instances
                        WHERE node_definition_id = %s
                    """,
                        (node_def_id,),
                    )

                    # 4. 删除节点定义
                    await cursor.execute(
                        """
                        DELETE FROM workflow_node_definitions
                        WHERE id = %s AND workflow_definition_id = %s
                    """,
                        (node_def_id, workflow_def_id),
                    )

                    await conn.commit()
                    return True

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'删除节点失败: {str(e)}')
                    return False

    async def add_edge(
        self, workflow_def_id: int, edge_data: Dict
    ) -> Optional[int]:
        """添加新边到工作流定义和所有相关实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    # 1. 添加边定义
                    await cursor.execute(
                        """
                        INSERT INTO workflow_edge_definitions
                        (workflow_definition_id, from_node_id, to_node_id, condition)
                        VALUES (%s, %s, %s, %s)
                    """,
                        (
                            workflow_def_id,
                            edge_data['from_node_id'],
                            edge_data['to_node_id'],
                            edge_data.get('condition', 'default'),
                        ),
                    )
                    edge_def_id = cursor.lastrowid

                    # 2. 为所有活动的工作流实例创建边实例
                    await cursor.execute(
                        """
                        INSERT INTO workflow_edge_instances
                        (workflow_instance_id, edge_definition_id, from_node_instance_id, to_node_instance_id, transition_time)
                        SELECT
                            wi.id,
                            %s,
                            ni_from.id,
                            ni_to.id,
                            CASE
                                WHEN wi.status = 'completed' THEN NOW()
                                ELSE NULL
                            END as transition_time
                        FROM workflow_instances wi
                        JOIN workflow_node_instances ni_from
                            ON wi.id = ni_from.workflow_instance_id
                            AND ni_from.node_definition_id = %s
                        JOIN workflow_node_instances ni_to
                            ON wi.id = ni_to.workflow_instance_id
                            AND ni_to.node_definition_id = %s
                        WHERE wi.workflow_definition_id = %s
                    """,
                        (
                            edge_def_id,
                            edge_data['from_node_id'],
                            edge_data['to_node_id'],
                            workflow_def_id,
                        ),
                    )

                    await conn.commit()
                    return edge_def_id

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'添加边失败: {str(e)}')
                    return None

    async def delete_edge(
        self, workflow_def_id: int, edge_def_id: int
    ) -> bool:
        """删除工作流中的边及其关联的所有实例"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    # 1. 删除边实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_instances
                        WHERE edge_definition_id = %s
                    """,
                        (edge_def_id,),
                    )

                    # 2. 删除边定义
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_definitions
                        WHERE id = %s AND workflow_definition_id = %s
                    """,
                        (edge_def_id, workflow_def_id),
                    )

                    await conn.commit()
                    return True

                except Exception as e:
                    await conn.rollback()
                    logging.error(f'删除边失败: {str(e)}')
                    return False

    async def update_workflow(
        self, workflow_def_id: int, modifications: Dict
    ) -> bool:
        """批量更新工作流定义和实例"""
        try:
            # 确保连接池已初始化
            await self.init_pool()

            # 添加新节点
            for node_data in modifications.get('add_nodes', []):
                await self.add_node(workflow_def_id, node_data)

            # 删除节点
            for node_id in modifications.get('delete_nodes', []):
                await self.delete_node(workflow_def_id, node_id)

            # 添加新边
            for edge_data in modifications.get('add_edges', []):
                await self.add_edge(workflow_def_id, edge_data)

            # 删除边
            for edge_id in modifications.get('delete_edges', []):
                await self.delete_edge(workflow_def_id, edge_id)

            return True

        except Exception as e:
            logging.error(f'更新工作流失败: {str(e)}')
            return False

    async def list_workflow_changes(
        self, workflow_def_id: int, instance_id: Optional[int] = None
    ) -> Dict:
        """列出工作流的所有节点和边，可选择查看特定实例的状态"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 获取所有节点
                await cursor.execute(
                    """
                    SELECT id, name, type, color, task_url
                    FROM workflow_node_definitions
                    WHERE workflow_definition_id = %s
                """,
                    (workflow_def_id,),
                )
                nodes = await cursor.fetchall()

                # 获取所有边
                await cursor.execute(
                    """
                    SELECT id, from_node_id, to_node_id, `condition`
                    FROM workflow_edge_definitions
                    WHERE workflow_definition_id = %s
                """,
                    (workflow_def_id,),
                )
                edges = await cursor.fetchall()

                result = {
                    'nodes': [
                        {
                            'id': node[0],
                            'name': node[1],
                            'type': node[2],
                            'color': node[3],
                            'task_url': node[4],
                        }
                        for node in nodes
                    ],
                    'edges': [
                        {
                            'id': edge[0],
                            'from_node': edge[1],
                            'to_node': edge[2],
                            'condition': edge[3],
                        }
                        for edge in edges
                    ],
                }

                # 如果指定了实例ID，添加实例状态信息
                if instance_id:
                    await cursor.execute(
                        """
                        SELECT nd.id, ni.status, ni.start_time, ni.end_time
                        FROM workflow_node_definitions nd
                        LEFT JOIN workflow_node_instances ni
                            ON nd.id = ni.node_definition_id
                            AND ni.workflow_instance_id = %s
                        WHERE nd.workflow_definition_id = %s
                    """,
                        (instance_id, workflow_def_id),
                    )
                    node_states = await cursor.fetchall()

                    # 添加节点状态信息
                    for node in result['nodes']:
                        node_state = next(
                            (ns for ns in node_states if ns[0] == node['id']),
                            None,
                        )
                        if node_state:
                            node['instance_status'] = {
                                'status': node_state[1],
                                'start_time': node_state[2].isoformat()
                                if node_state[2]
                                else None,
                                'end_time': node_state[3].isoformat()
                                if node_state[3]
                                else None,
                            }

                return result

    async def close(self):
        """关闭连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()


# 使用示例
async def main():
    # 初始化修改器
    modifier = WorkflowModifier(DB_CONFIG)
    await modifier.init_pool()

    try:
        # 示例: 更新工作流
        modifications = {
            # 'add_nodes': [
            #     {
            #         'name': '新任务节点',
            #         'type': 'process',
            #         'color': '#FF0000',
            #         'task_url': 'http://example.com/task',
            #     }
            # ],
            'delete_nodes': [123],
            # 'add_edges': [
            #     {'from_node_id': 1, 'to_node_id': 2, 'condition': 'default'}
            # ],
            # 'delete_edges': [456],
        }

        success = await modifier.update_workflow(1, modifications)
        if success:
            print('工作流更新成功')

            # 列出当前工作流结构
            current_structure = await modifier.list_workflow_changes(1)
            print('当前工作流结构:', json.dumps(current_structure, indent=2))
        else:
            print('工作流更新失败')

    finally:
        await modifier.close()


if __name__ == '__main__':
    import asyncio

    asyncio.run(main())
