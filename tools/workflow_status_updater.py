#!/usr/bin/env python3
import asyncio
import aiomysql
import logging
from typing import Dict, List
from datetime import datetime

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'leadchina',
    'db': 'IO',
    'autocommit': False,
}

# 状态列表
VALID_STATUSES = ['completed', 'active', 'pending']


async def get_project_workflows(pool, project_id: int) -> List[Dict]:
    """获取项目下的所有工作流实例"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                """
                SELECT
                    wi.id as workflow_instance_id,
                    wd.id as workflow_def_id,
                    wd.name as workflow_name,
                    wi.status as workflow_status,
                    wi.start_time,
                    wi.end_time
                FROM workflow_instances wi
                JOIN workflow_definitions wd ON wi.workflow_definition_id = wd.id
                WHERE wi.project_id = %s
                """,
                (project_id,),
            )
            workflows = await cursor.fetchall()

            return [
                {
                    'workflow_instance_id': row[0],
                    'workflow_def_id': row[1],
                    'workflow_name': row[2],
                    'status': row[3],
                    'start_time': row[4],
                    'end_time': row[5],
                }
                for row in workflows
            ]


async def get_workflow_nodes(pool, workflow_instance_id: int) -> List[Dict]:
    """获取工作流下的所有节点实例"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                """
                SELECT
                    ni.id as node_instance_id,
                    nd.id as node_def_id,
                    nd.name as node_name,
                    ni.status,
                    ni.start_time,
                    ni.end_time
                FROM workflow_node_instances ni
                JOIN workflow_node_definitions nd ON ni.node_definition_id = nd.id
                WHERE ni.workflow_instance_id = %s
                ORDER BY nd.id
                """,
                (workflow_instance_id,),
            )
            nodes = await cursor.fetchall()

            return [
                {
                    'node_instance_id': row[0],
                    'node_def_id': row[1],
                    'node_name': row[2],
                    'current_status': row[3],
                    'start_time': row[4],
                    'end_time': row[5],
                }
                for row in nodes
            ]


async def update_workflow_status(
    pool, workflow: Dict, new_status: str
) -> bool:
    """更新工作流状态"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                await conn.begin()

                update_params = {
                    'status': new_status,
                    'updated_at': datetime.now(),
                }

                if new_status == 'active':
                    # active状态设置开始时间
                    if not workflow['start_time']:
                        update_params['start_time'] = datetime.now()

                elif new_status == 'completed':
                    # completed状态设置结束时间，如果没有开始时间也设置开始时间
                    if not workflow['start_time']:
                        update_params['start_time'] = datetime.now()
                    update_params['end_time'] = datetime.now()

                # 构建SQL语句
                set_clauses = [f'{k} = %s' for k in update_params.keys()]
                sql = f"""
                    UPDATE workflow_instances
                    SET {', '.join(set_clauses)}
                    WHERE id = %s
                """

                # 执行更新
                await cursor.execute(
                    sql,
                    (
                        *update_params.values(),
                        workflow['workflow_instance_id'],
                    ),
                )

                await conn.commit()
                logging.info(
                    f"工作流更新成功: {workflow['workflow_name']} -> {new_status}"
                )
                return True

            except Exception as e:
                await conn.rollback()
                logging.error(
                    f"工作流更新失败: {workflow['workflow_name']} - {str(e)}"
                )
                return False


async def update_node_status(pool, node: Dict, new_status: str) -> bool:
    """更新节点状态"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                await conn.begin()

                update_params = {
                    'status': new_status,
                    'updated_at': datetime.now(),
                }

                if new_status == 'active':
                    # active状态设置开始时间
                    if not node['start_time']:
                        update_params['start_time'] = datetime.now()

                elif new_status == 'completed':
                    # completed状态设置结束时间，如果没有开始时间也设置开始时间
                    if not node['start_time']:
                        update_params['start_time'] = datetime.now()
                    update_params['end_time'] = datetime.now()

                # 构建SQL语句
                set_clauses = [f'{k} = %s' for k in update_params.keys()]
                sql = f"""
                    UPDATE workflow_node_instances
                    SET {', '.join(set_clauses)}
                    WHERE id = %s
                """

                # 执行更新
                await cursor.execute(
                    sql, (*update_params.values(), node['node_instance_id'])
                )

                await conn.commit()
                logging.info(f"节点更新成功: {node['node_name']} -> {new_status}")
                return True

            except Exception as e:
                await conn.rollback()
                logging.error(f"节点更新失败: {node['node_name']} - {str(e)}")
                return False


async def update_project_workflow(pool, project_id: int, status: str) -> Dict:
    """更新项目下所有工作流和节点状态"""
    if status not in VALID_STATUSES:
        raise ValueError(f'无效的状态值: {status}')

    results = {
        'success_count': 0,
        'fail_count': 0,
        'workflows_processed': 0,
        'nodes_processed': 0,
    }

    # 获取项目下所有工作流
    workflows = await get_project_workflows(pool, project_id)
    if not workflows:
        logging.warning(f'未找到项目工作流: {project_id}')
        return results

    for workflow in workflows:
        results['workflows_processed'] += 1

        # 更新工作流状态
        if await update_workflow_status(pool, workflow, status):
            results['success_count'] += 1
        else:
            results['fail_count'] += 1

        # 获取工作流下所有节点
        nodes = await get_workflow_nodes(
            pool, workflow['workflow_instance_id']
        )
        for node in nodes:
            results['nodes_processed'] += 1

            # 更新节点状态
            if await update_node_status(pool, node, status):
                results['success_count'] += 1
            else:
                results['fail_count'] += 1

    return results


async def main():
    # 创建数据库连接池
    pool = await aiomysql.create_pool(**DB_CONFIG)

    try:
        # 设置要更新的项目ID和状态
        project_id = int(input('请输入项目ID: '))
        print('\n可选状态: completed, active, pending')
        status = input('请输入要更新的状态: ').lower()

        # 执行更新
        results = await update_project_workflow(pool, project_id, status)

        # 打印更新结果
        print('\n更新完成:')
        print(f"处理的工作流数量: {results['workflows_processed']}")
        print(f"处理的节点数量: {results['nodes_processed']}")
        print(f"成功更新: {results['success_count']}")
        print(f"更新失败: {results['fail_count']}")

    except ValueError as e:
        print(f'错误: {str(e)}')
    except Exception as e:
        logging.error(f'运行出错: {str(e)}')
    finally:
        pool.close()
        await pool.wait_closed()


if __name__ == '__main__':
    asyncio.run(main())
