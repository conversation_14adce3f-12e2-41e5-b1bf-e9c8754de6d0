#!/usr/bin/env python3
import asyncio
import logging
from typing import List, Tuple
import aiomysql
from datetime import datetime
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)


class OutputMachineWorkflowDeleter:
    """出料机工作流删除器"""

    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.pool = None

    async def init_pool(self):
        """初始化连接池"""
        if not self.pool:
            self.pool = await aiomysql.create_pool(**self.db_config)
        return self.pool

    async def close(self):
        """关闭连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()

    async def get_output_machine_workflows(self) -> List[Tuple[int, str]]:
        """获取所有出料机工作流实例ID和项目ID"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    SELECT id, project_id
                    FROM workflow_instances
                    WHERE device_type = 'output_machine'
                    ORDER BY project_id, id
                    """
                )
                results = await cursor.fetchall()
                return [(row[0], row[1]) for row in results]

    async def get_workflow_statistics(self) -> dict:
        """获取出料机工作流统计信息"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 获取总数和按项目分组的统计
                await cursor.execute(
                    """
                    SELECT
                        COUNT(*) as total_count,
                        COUNT(DISTINCT project_id) as project_count
                    FROM workflow_instances
                    WHERE device_type = 'output_machine'
                    """
                )
                total_stats = await cursor.fetchone()

                # 获取按项目分组的详细统计
                await cursor.execute(
                    """
                    SELECT
                        project_id,
                        COUNT(*) as workflow_count
                    FROM workflow_instances
                    WHERE device_type = 'output_machine'
                    GROUP BY project_id
                    ORDER BY project_id
                    """
                )
                project_stats = await cursor.fetchall()

                return {
                    'total_workflows': total_stats[0],
                    'total_projects': total_stats[1],
                    'project_details': [
                        (row[0], row[1]) for row in project_stats
                    ],
                }

    async def delete_workflow_instance(
        self, instance_id: int, project_id: str
    ) -> bool:
        """删除单个工作流实例及其相关数据"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()

                    # 1. 删除实例变量
                    await cursor.execute(
                        """
                        DELETE FROM workflow_instance_variables
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 2. 删除审批记录
                    await cursor.execute(
                        """
                        DELETE ar FROM workflow_approval_records ar
                        INNER JOIN workflow_node_instances ni ON ar.node_instance_id = ni.id
                        WHERE ni.workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 3. 删除变更日志
                    await cursor.execute(
                        """
                        DELETE FROM workflow_change_logs
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 4. 删除边实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_edge_instances
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 5. 删除节点实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_node_instances
                        WHERE workflow_instance_id = %s
                        """,
                        (instance_id,),
                    )

                    # 6. 最后删除工作流实例
                    await cursor.execute(
                        """
                        DELETE FROM workflow_instances
                        WHERE id = %s
                        """,
                        (instance_id,),
                    )

                    await conn.commit()
                    logging.info(
                        f'Successfully deleted output machine workflow {instance_id} for project {project_id}'
                    )
                    return True

                except Exception as e:
                    await conn.rollback()
                    logging.error(
                        f'Failed to delete output machine workflow {instance_id} for project {project_id}: {str(e)}'
                    )
                    return False

    async def generate_deletion_report(
        self, deleted_instances: List[Tuple[int, str, bool]], stats: dict
    ) -> str:
        """生成删除报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_filename = f'output_machine_deletion_report_{timestamp}.json'

        successful_deletions = [item for item in deleted_instances if item[2]]
        failed_deletions = [item for item in deleted_instances if not item[2]]

        report_data = {
            'deletion_timestamp': datetime.now().isoformat(),
            'statistics': {
                'total_workflows_before': stats['total_workflows'],
                'total_projects_affected': stats['total_projects'],
                'deletion_attempted': len(deleted_instances),
                'deletion_successful': len(successful_deletions),
                'deletion_failed': len(failed_deletions),
            },
            'project_details_before': [
                {'project_id': proj_id, 'workflow_count': count}
                for proj_id, count in stats['project_details']
            ],
            'successful_deletions': [
                {'workflow_id': wf_id, 'project_id': proj_id}
                for wf_id, proj_id, _ in successful_deletions
            ],
            'failed_deletions': [
                {'workflow_id': wf_id, 'project_id': proj_id}
                for wf_id, proj_id, _ in failed_deletions
            ],
        }

        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        logging.info(f'删除报告已保存到: {report_filename}')
        return report_filename


async def delete_all_output_machine_workflows(
    db_config: dict, confirm: bool = False
):
    """删除所有出料机工作流实例"""
    deleter = OutputMachineWorkflowDeleter(db_config)
    await deleter.init_pool()

    try:
        # 首先获取统计信息
        stats = await deleter.get_workflow_statistics()

        logging.info('=== 出料机工作流统计信息 ===')
        logging.info(f"总工作流数量: {stats['total_workflows']}")
        logging.info(f"涉及项目数量: {stats['total_projects']}")
        logging.info('各项目详情:')
        for project_id, count in stats['project_details']:
            logging.info(f'  项目 {project_id}: {count} 个出料机工作流')

        if not confirm:
            logging.warning('这是预览模式，不会实际删除数据')
            logging.warning('如需实际删除，请在调用时设置 confirm=True')
            return

        # 获取所有出料机工作流实例
        workflow_instances = await deleter.get_output_machine_workflows()

        if not workflow_instances:
            logging.info('No output machine workflow instances found')
            return

        logging.info(
            f'Found {len(workflow_instances)} output machine workflow instances'
        )

        # 删除每个工作流实例
        deletion_results = []
        success_count = 0

        for instance_id, project_id in workflow_instances:
            success = await deleter.delete_workflow_instance(
                instance_id, project_id
            )
            deletion_results.append((instance_id, project_id, success))
            if success:
                success_count += 1

        logging.info('=== 删除结果统计 ===')
        logging.info(
            f'成功删除: {success_count}/{len(workflow_instances)} 个出料机工作流'
        )

        failed_instances = [
            (wf_id, proj_id)
            for wf_id, proj_id, success in deletion_results
            if not success
        ]
        if failed_instances:
            logging.error('删除失败的工作流:')
            for instance_id, project_id in failed_instances:
                logging.error(f'  工作流 {instance_id} (项目 {project_id})')

        # 生成删除报告
        if confirm:  # 只有在实际删除时才生成报告
            report_file = await deleter.generate_deletion_report(
                deletion_results, stats
            )
            logging.info(f'详细删除报告已保存到: {report_file}')

    finally:
        await deleter.close()


async def delete_output_machine_workflows_by_project(
    db_config: dict, project_id: str
):
    """删除指定项目的出料机工作流实例"""
    deleter = OutputMachineWorkflowDeleter(db_config)
    await deleter.init_pool()

    try:
        # 获取指定项目的出料机工作流
        async with deleter.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    SELECT id, project_id
                    FROM workflow_instances
                    WHERE device_type = 'output_machine' AND project_id = %s
                    ORDER BY id
                    """,
                    (project_id,),
                )
                results = await cursor.fetchall()
                workflow_instances = [(row[0], row[1]) for row in results]

        if not workflow_instances:
            logging.info(
                f'No output machine workflow instances found for project {project_id}'
            )
            return

        logging.info(
            f'Found {len(workflow_instances)} output machine workflow instances for project {project_id}'
        )

        # 删除每个工作流实例
        success_count = 0
        for instance_id, proj_id in workflow_instances:
            if await deleter.delete_workflow_instance(instance_id, proj_id):
                success_count += 1

        logging.info(
            f'Successfully deleted {success_count}/{len(workflow_instances)} output machine workflows for project {project_id}'
        )

    finally:
        await deleter.close()


async def main():
    # 数据库配置
    DB_CONFIG = {
        'host': '*************',
        'user': 'root',
        'password': 'leadchina',
        'db': 'IO',
        'autocommit': False,
    }

    print('=== 出料机工作流删除工具 ===')
    print('此工具可以删除出料机工作流实例')
    print('1. 删除所有出料机工作流')
    print('2. 删除指定项目的出料机工作流')
    print()

    choice = input('请选择操作 (1/2): ').strip()

    if choice == '1':
        # 删除所有出料机工作流
        print('正在获取出料机工作流统计信息...')
        await delete_all_output_machine_workflows(DB_CONFIG, confirm=False)

        print()
        confirm = input("确认要删除所有出料机工作流吗？这个操作不可逆！(输入 'YES' 确认): ")

        if confirm == 'YES':
            print('开始删除所有出料机工作流...')
            await delete_all_output_machine_workflows(DB_CONFIG, confirm=True)
            print('删除操作完成！')
        else:
            print('操作已取消')

    elif choice == '2':
        # 删除指定项目的出料机工作流
        project_id = input('请输入项目ID: ').strip()
        if project_id:
            confirm = input(f"确认要删除项目 {project_id} 的所有出料机工作流吗？(输入 'YES' 确认): ")
            if confirm == 'YES':
                print(f'开始删除项目 {project_id} 的出料机工作流...')
                await delete_output_machine_workflows_by_project(
                    DB_CONFIG, project_id
                )
                print('删除操作完成！')
            else:
                print('操作已取消')
        else:
            print('项目ID不能为空')
    else:
        print('无效的选择')


if __name__ == '__main__':
    asyncio.run(main())
