#!/usr/bin/env python3
import aiomysql
import asyncio
import logging
from datetime import datetime
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'leadchina',
    'db': 'IO',
    'autocommit': True,
}

# 按照依赖关系排序的表（从最基础的表开始）
TABLES = [
    'workflow_definitions',  # 1. 基础定义表
    'workflow_node_definitions',  # 2. 依赖workflow_definitions
    'workflow_edge_definitions',  # 3. 依赖node_definitions
    'workflow_machine_type_durations',  # 4. 依赖node_definitions
    'workflow_instances',  # 5. 依赖workflow_definitions
    'workflow_node_instances',  # 6. 依赖instances和node_definitions
    'workflow_edge_instances',  # 7. 依赖edge_definitions和node_instances
    'workflow_instance_variables',  # 8. 依赖instances和node_instances
    'workflow_approval_records',  # 9. 依赖node_instances
    'workflow_change_logs',  # 10. 依赖instances
]


async def get_connection():
    """建立数据库连接"""
    try:
        pool = await aiomysql.create_pool(**DB_CONFIG)
        return pool
    except Exception as e:
        logging.error(f'数据库连接失败: {e}')
        return None


async def backup_table(pool, table, backup_table_name):
    """备份单个表"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                await cursor.execute(
                    f'DROP TABLE IF EXISTS {backup_table_name}'
                )
                await cursor.execute(
                    f'CREATE TABLE {backup_table_name} LIKE {table}'
                )
                await cursor.execute(
                    f'INSERT INTO {backup_table_name} SELECT * FROM {table}'
                )
                logging.info(f'表 {table} 备份完成')
                return True
            except Exception as e:
                logging.error(f'备份表 {table} 时发生错误: {e}')
                return False


async def restore_table(pool, table, backup_table_name):
    """恢复单个表"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                # 检查备份表是否存在
                await cursor.execute(f"SHOW TABLES LIKE '{backup_table_name}'")
                if not await cursor.fetchone():
                    logging.error(f'备份表 {backup_table_name} 不存在')
                    return False

                # 删除原表数据
                await cursor.execute(f'DELETE FROM {table}')

                # 恢复数据
                await cursor.execute(
                    f'INSERT INTO {table} SELECT * FROM {backup_table_name}'
                )

                logging.info(f'表 {table} 恢复完成')
                return True
            except Exception as e:
                logging.error(f'恢复表 {table} 时发生错误: {e}')
                return False


async def backup_tables(pool, backup_date=None):
    """备份所有表"""
    if backup_date is None:
        backup_date = datetime.now().strftime('%Y%m%d')

    tasks = []
    for table in TABLES:
        backup_table_name = f'{table}_backup_{backup_date}'
        task = backup_table(pool, table, backup_table_name)
        tasks.append(task)

    results = await asyncio.gather(*tasks, return_exceptions=True)
    success = all(isinstance(r, bool) and r for r in results)

    if success:
        logging.info('所有表备份完成')
    else:
        logging.error('部分表备份失败')

    return success


async def restore_tables(pool, backup_date):
    """恢复所有表"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                # 禁用外键检查
                await cursor.execute('SET FOREIGN_KEY_CHECKS = 0')

                # 首先检查所有备份表是否存在
                for table in reversed(TABLES):
                    backup_table_name = f'{table}_backup_{backup_date}'
                    await cursor.execute(
                        f"SHOW TABLES LIKE '{backup_table_name}'"
                    )
                    if not await cursor.fetchone():
                        raise Exception(f'备份表 {backup_table_name} 不存在')

                # 按照依赖关系的反序清空所有表
                for table in reversed(TABLES):
                    logging.info(f'清空表 {table}')
                    await cursor.execute(f'DELETE FROM {table}')

                # 按照依赖关系的正序恢复所有表
                for table in TABLES:
                    backup_table_name = f'{table}_backup_{backup_date}'
                    logging.info(f'正在恢复表 {table}')
                    await cursor.execute(
                        f'INSERT INTO {table} SELECT * FROM {backup_table_name}'
                    )

                logging.info('所有表恢复完成')
                return True

            except Exception as e:
                logging.error(f'恢复过程中发生错误: {e}')
                return False

            finally:
                # 恢复外键检查
                await cursor.execute('SET FOREIGN_KEY_CHECKS = 1')


async def list_backups(pool):
    """列出所有备份"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                await cursor.execute(
                    f"SHOW TABLES LIKE '{TABLES[0]}_backup_%'"
                )
                backups = await cursor.fetchall()

                backup_dates = set()
                for backup in backups:
                    date = backup[0].split('_backup_')[1]
                    backup_dates.add(date)

                if backup_dates:
                    logging.info('找到以下备份:')
                    for date in sorted(backup_dates):
                        logging.info(f'- {date}')
                else:
                    logging.info('没有找到任何备份')

                return list(backup_dates)

            except Exception as e:
                logging.error(f'列出备份时发生错误: {e}')
                return []


async def delete_backup(pool, backup_date):
    """删除指定日期的备份"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                tables_to_delete = []
                for table in TABLES:
                    backup_table_name = f'{table}_backup_{backup_date}'
                    await cursor.execute(
                        f"SHOW TABLES LIKE '{backup_table_name}'"
                    )
                    if await cursor.fetchone():
                        tables_to_delete.append(backup_table_name)

                if not tables_to_delete:
                    logging.error(f'没有找到日期为 {backup_date} 的备份')
                    return False

                for table in tables_to_delete:
                    logging.info(f'正在删除备份表 {table}')
                    await cursor.execute(f'DROP TABLE {table}')

                logging.info(f'成功删除所有 {backup_date} 的备份表')
                return True

            except Exception as e:
                logging.error(f'删除备份时发生错误: {e}')
                return False


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print('使用方法:')
        print('备份: python script.py backup')
        print('恢复: python script.py restore YYYYMMDD')
        print('删除: python script.py delete YYYYMMDD')
        print('列表: python script.py list')
        return

    pool = await get_connection()
    if not pool:
        return

    try:
        command = sys.argv[1]

        if command == 'backup':
            await backup_tables(pool)

        elif command == 'restore':
            if len(sys.argv) != 3:
                print('请指定要恢复的备份日期 (YYYYMMDD)')
                return
            await restore_tables(pool, sys.argv[2])

        elif command == 'list':
            await list_backups(pool)

        elif command == 'delete':
            if len(sys.argv) != 3:
                print('请指定要删除的备份日期 (YYYYMMDD)')
                return
            await delete_backup(pool, sys.argv[2])

        else:
            print('未知命令。可用命令: backup, restore, delete, list')

    finally:
        pool.close()
        await pool.wait_closed()


if __name__ == '__main__':
    asyncio.run(main())
