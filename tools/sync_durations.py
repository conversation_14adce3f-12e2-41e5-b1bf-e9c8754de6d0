import asyncio
import aiomysql
from typing import List, <PERSON><PERSON>, Set


async def get_db_connection():
    return await aiomysql.connect(
        host='*************',  # 请替换为实际的数据库配置
        user='root',
        password='leadchina',
        db='IO',
        charset='utf8mb4',
    )


async def get_all_machine_type_combinations(conn) -> List[Tuple[str, str]]:
    """获取所有唯一的 machine_type + device_type 组合"""
    async with conn.cursor() as cursor:
        await cursor.execute(
            """
            SELECT DISTINCT machine_type, device_type
            FROM workflow_machine_type_durations
        """
        )
        return await cursor.fetchall()


async def get_all_node_definition_ids(conn) -> Set[int]:
    """获取所有的 node_definition_id"""
    async with conn.cursor() as cursor:
        await cursor.execute(
            """
            SELECT id FROM workflow_node_definitions
        """
        )
        return {row[0] for row in await cursor.fetchall()}


async def get_existing_combinations(
    conn, machine_type: str, device_type: str
) -> Set[int]:
    """获取指定 machine_type + device_type 组合已有的 node_definition_id"""
    async with conn.cursor() as cursor:
        await cursor.execute(
            """
            SELECT node_definition_id
            FROM workflow_machine_type_durations
            WHERE machine_type = %s AND device_type = %s
        """,
            (machine_type, device_type),
        )
        return {row[0] for row in await cursor.fetchall()}


async def insert_missing_durations(
    conn, machine_type: str, device_type: str, node_ids: Set[int]
):
    """插入缺失的记录"""
    async with conn.cursor() as cursor:
        for node_id in node_ids:
            try:
                await cursor.execute(
                    """
                    INSERT INTO workflow_machine_type_durations
                    (node_definition_id, machine_type, expected_duration, device_type, created_by)
                    VALUES (%s, %s, NULL, %s, 40)
                """,
                    (node_id, machine_type, device_type),
                )
            except aiomysql.Error as e:
                print(
                    f'Error inserting record for node_id {node_id}, machine_type {machine_type}, '
                    f'device_type {device_type}: {e}'
                )


async def main():
    conn = await get_db_connection()
    try:
        # 获取所有组合
        combinations = await get_all_machine_type_combinations(conn)
        # 获取所有 node_definition_ids
        all_node_ids = await get_all_node_definition_ids(conn)

        print(f'Found {len(combinations)} machine type combinations')
        print(f'Found {len(all_node_ids)} node definitions')
        # 处理每个组合
        for machine_type, device_type in combinations:
            # 获取该组合已有的 node_definition_ids
            existing_node_ids = await get_existing_combinations(
                conn, machine_type, device_type
            )
            # 计算缺失的 node_definition_ids
            missing_node_ids = all_node_ids - existing_node_ids

            if missing_node_ids:
                print(
                    f'Inserting {len(missing_node_ids)} missing records for '
                    f'{machine_type} - {device_type}'
                )
                await insert_missing_durations(
                    conn, machine_type, device_type, missing_node_ids
                )
                await conn.commit()
            else:
                print(f'No missing records for {machine_type} - {device_type}')

    except Exception as e:
        print(f'An error occurred: {e}')
        await conn.rollback()
    finally:
        conn.close()


if __name__ == '__main__':
    asyncio.run(main())
