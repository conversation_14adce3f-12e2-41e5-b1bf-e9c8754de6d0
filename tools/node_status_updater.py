#!/usr/bin/env python3
import asyncio
import aiomysql
import logging
from typing import Dict

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'leadchina',
    'db': 'IO',
    'autocommit': False,
}

# 要更新的节点列表
NODES_TO_UPDATE = [
    {
        'workflow_def_id': 1,
        'project_id': 37242,
        'node_name': '技术开发负责人审核',
        'status': 'pending',
    },
    # {
    #     'workflow_def_id': 1,
    #     'project_id': 37555,
    #     'node_name': 'PLC/HMI程序准备',
    #     'status': 'pending',
    # },
    # {
    #     'workflow_def_id': 1,
    #     'project_id': 37221,
    #     'node_name': '部件完整信息录入',
    #     'status': 'active',
    # },
]


async def update_node_status(pool, node_config: Dict) -> bool:
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                # 获取所有匹配的节点实例ID
                await cursor.execute(
                    """
                    SELECT ni.id, ni.start_time
                    FROM workflow_node_instances ni
                    JOIN workflow_node_definitions nd ON ni.node_definition_id = nd.id
                    JOIN workflow_instances wi ON ni.workflow_instance_id = wi.id
                    WHERE nd.workflow_definition_id = %s
                    AND wi.project_id = %s
                    AND nd.name = %s
                    """,
                    (
                        node_config['workflow_def_id'],
                        node_config['project_id'],
                        node_config['node_name'],
                    ),
                )
                results = await cursor.fetchall()
                if not results:
                    logging.error(
                        f'找不到节点: {node_config["node_name"]} '
                        f'(项目ID: {node_config["project_id"]}, '
                        f'流程ID: {node_config["workflow_def_id"]})'
                    )
                    return False

                # 开始事务
                await conn.begin()

                # 根据状态构建更新SQL
                if node_config['status'] == 'active':
                    update_sql = """
                        UPDATE workflow_node_instances
                        SET status = %s,
                            start_time = NOW(),
                            updated_at = NOW()
                        WHERE id IN %s
                    """
                elif node_config['status'] == 'completed':
                    # 对于completed状态，需要分两种情况处理
                    for row in results:
                        instance_id = row[0]
                        start_time = row[1]

                        if start_time is None:
                            # 如果start_time为空，同时设置start_time和end_time
                            await cursor.execute(
                                """
                                UPDATE workflow_node_instances
                                SET status = %s,
                                    start_time = NOW(),
                                    end_time = NOW(),
                                    updated_at = NOW()
                                WHERE id = %s
                                """,
                                (node_config['status'], instance_id),
                            )
                        else:
                            # 如果start_time已存在，只设置end_time
                            await cursor.execute(
                                """
                                UPDATE workflow_node_instances
                                SET status = %s,
                                    end_time = NOW(),
                                    updated_at = NOW()
                                WHERE id = %s
                                """,
                                (node_config['status'], instance_id),
                            )

                    await conn.commit()
                    logging.info(
                        f'更新成功: {node_config["node_name"]} -> {node_config["status"]} '
                        f'(更新了 {len(results)} 个实例)'
                    )
                    return True
                else:
                    # 其他状态只更新status和updated_at
                    update_sql = """
                        UPDATE workflow_node_instances
                        SET status = %s,
                            updated_at = NOW()
                        WHERE id IN %s
                    """

                # 如果不是completed状态，执行批量更新
                if node_config['status'] != 'completed':
                    instance_ids = [row[0] for row in results]
                    await cursor.execute(
                        update_sql,
                        (node_config['status'], tuple(instance_ids)),
                    )

                await conn.commit()
                logging.info(
                    f'更新成功: {node_config["node_name"]} -> {node_config["status"]} '
                    f'(更新了 {len(results)} 个实例)'
                )
                return True

            except Exception as e:
                await conn.rollback()
                logging.error(f'更新失败: {str(e)}')
                return False


async def main():
    # 创建连接池
    pool = await aiomysql.create_pool(**DB_CONFIG)

    try:
        success_count = 0
        fail_count = 0

        # 执行批量更新
        for node in NODES_TO_UPDATE:
            if await update_node_status(pool, node):
                success_count += 1
            else:
                fail_count += 1

        # 打印结果统计
        print('\n更新完成:')
        print(f'成功: {success_count}')
        print(f'失败: {fail_count}')

    finally:
        pool.close()
        await pool.wait_closed()


if __name__ == '__main__':
    asyncio.run(main())
