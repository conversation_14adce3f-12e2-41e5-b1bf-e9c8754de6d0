apiVersion: apps/v1
kind: Deployment
metadata:
  name: wind-workflow
  labels:
    app: wind-workflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wind-workflow
  template:
    metadata:
      labels:
        app: wind-workflow
    spec:
      containers:
      - name: wind-workflow
        image: *************:5000/wind-workflow:v1 # localhost:5000/wind-workflow:v1
        imagePullPolicy: Always  # Always pull the latest image
        ports:
        - containerPort: 8000
---
apiVersion: v1
kind: Service
metadata:
  name: wind-workflow-service
spec:
  type: NodePort
  selector:
    app: wind-workflow
  ports:
  - protocol: TCP
    port: 8000
    targetPort: 8000
    nodePort: 30088  # 可选，指定 NodePort 端口号，范围通常为 30000-32767
