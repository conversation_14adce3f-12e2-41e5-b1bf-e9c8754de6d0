const { defineConfig } = require('@vue/cli-service')
module.exports = {
  transpileDependencies: ['mermaid'],
  configureWebpack: {
    module: {
      rules: [
        {
          test: /\.m?js$/,
          exclude: /(node_modules|bower_components)/,
          use: {
            loader: 'babel-loader',
            options: {
              plugins: ['@babel/plugin-transform-class-static-block']
            }
          }
        }
      ]
    }
  }
};
