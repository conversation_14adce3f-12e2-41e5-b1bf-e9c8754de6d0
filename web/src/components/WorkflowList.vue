<template>
  <div class="workflow-list">
    <h2>工作流程列表</h2>
    <el-menu @select="handleSelect">
      <el-menu-item v-for="workflow in workflows" :key="workflow.id" :index="workflow.id.toString()">
        {{ workflow.name }}
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import workflowApi from '../services/api.js'

export default {
  name: 'WorkflowList',
  emits: ['select-workflow'],
  setup(props, { emit }) {
    const workflows = ref([])

    const loadWorkflows = async () => {
      try {
        workflows.value = await workflowApi.getWorkflows()
      } catch (error) {
        console.error('Error loading workflows:', error)
      }
    }

    const handleSelect = (index) => {
      emit('select-workflow', parseInt(index))
    }

    onMounted(loadWorkflows)

    return {
      workflows,
      handleSelect
    }
  }
}
</script>

<style scoped>
.workflow-list {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
}
</style>
