<template>
  <div class="workflow-diagram">
    <h2>工作流程图</h2>
    <div v-if="workflowData" ref="mermaidDiv"></div>
    <el-empty v-else-if="!loading" description="请选择一个工作流程" />
    <el-loading v-else element-loading-text="加载中..." />

    <el-dialog
      v-model="showApprovalDialog"
      title="审批"
      width="30%"
      :before-close="handleCloseDialog"
    >
      <span>是否批准 "{{ selectedNode?.name }}" 节点？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button type="danger" @click="handleReject">拒绝</el-button>
          <el-button type="primary" @click="handleApprove">批准</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 调试信息 -->
    <div class="debug-info">
      <p>Selected Node: {{ selectedNode?.name }}</p>
      <p>Show Approval Dialog: {{ showApprovalDialog }}</p>
      <p>Workflow Data Structure:</p>
      <pre>{{ JSON.stringify(workflowData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { ref, watch, onMounted, nextTick } from 'vue'
import mermaid from 'mermaid'
import workflowApi from '../services/api.js'

export default {
  name: 'WorkflowDiagram',
  props: {
    workflowId: {
      type: Number,
      default: null
    }
  },
  setup(props, { emit }) {
    const mermaidDiv = ref(null)
    const workflowData = ref(null)
    const loading = ref(false)
    const showApprovalDialog = ref(false)
    const selectedNode = ref(null)
    let mermaidInitialized = false

    const fetchWorkflowData = async (id) => {
      if (!id) return
      loading.value = true
      try {
        workflowData.value = await workflowApi.getWorkflow(id)
        console.log('Fetched workflow data:', workflowData.value)
      } catch (error) {
        console.error('Error fetching workflow data:', error)
        workflowData.value = null
      } finally {
        loading.value = false
      }
    }

    const getNodeShape = (type) => {
      switch (type) {
        case 'start': return '((开始))'
        case 'end': return '((结束))'
        case 'decision': return '{}'
        case 'process': return '[]'
        default: return '([])'
      }
    }

    const renderDiagram = async () => {
      if (workflowData.value && mermaidDiv.value) {
        let definition = 'graph TD\n'

        definition += '    Start((开始))\n'

        // 假设 workflowData.value.nodes 是一个数组
        if (Array.isArray(workflowData.value.nodes)) {
          workflowData.value.nodes.forEach((node, index) => {
            const shape = getNodeShape(node.type)
            // 使用索引作为唯一标识符
            definition += `    node${index}${shape.charAt(0)}"${node.name}"${shape.slice(-1)}\n`
          })

          if (workflowData.value.nodes.length > 0) {
            definition += `    Start --> node0\n`
          }

          // 假设 workflowData.value.edges 是一个数组
          if (Array.isArray(workflowData.value.edges)) {
            workflowData.value.edges.forEach(edge => {
              let label = edge.condition ? `|"${edge.condition}"|` : ''
              const fromIndex = workflowData.value.nodes.findIndex(n => n.id === edge.from)
              const toIndex = workflowData.value.nodes.findIndex(n => n.id === edge.to)
              if (fromIndex !== -1 && toIndex !== -1) {
                definition += `    node${fromIndex} -->${label} node${toIndex}\n`
              }
            })
          }

          if (workflowData.value.nodes.length > 0) {
            const lastIndex = workflowData.value.nodes.length - 1
            definition += `    node${lastIndex} --> End((结束))\n`
          }
        }

        console.log('Mermaid definition:', definition)

        await nextTick()
        mermaidDiv.value.innerHTML = ''

        try {
          const { svg } = await mermaid.render('mermaid-diagram', definition)
          mermaidDiv.value.innerHTML = svg
          await nextTick()
          addClickEventsToDiagram()
        } catch (error) {
          console.error('Mermaid render error:', error)
        }
      }
    }

    const addClickEventsToDiagram = () => {
      const svgElement = mermaidDiv.value.querySelector('svg')
      const nodeElements = svgElement.querySelectorAll('.node')
      nodeElements.forEach(el => {
        el.style.cursor = 'pointer'
        el.addEventListener('click', (event) => {
          event.stopPropagation()
          handleNodeClick(el.id)
        })
      })
    }

    const handleNodeClick = (nodeId) => {
      console.log('Node clicked:', nodeId)
      // 从 nodeId 中提取索引
      console.log('workflowData.value.nodes:', workflowData.value.nodes)
      const match = nodeId.match(/node(\d+)/)
      if (match) {
        const index = parseInt(match[1])
        const node = workflowData.value.nodes[index]
        if (node) {
          console.log('Node found:', node)
          if (node.type === 'decision') {
            console.log('Decision node clicked:', node)
            selectedNode.value = node
            showApprovalDialog.value = true
            console.log('Dialog should open now')
          }
        } else {
          console.log('Node not found for index:', index)
        }
      } else {
        console.log('Unable to extract node index from:', nodeId)
      }
    }

    const handleCloseDialog = () => {
      showApprovalDialog.value = false
      selectedNode.value = null
    }

    const handleApprove = () => {
      emit('node-approved', selectedNode.value)
      handleCloseDialog()
    }

    const handleReject = () => {
      emit('node-rejected', selectedNode.value)
      handleCloseDialog()
    }

    watch(() => props.workflowId, fetchWorkflowData, { immediate: true })
    watch(workflowData, renderDiagram)

    onMounted(() => {
      if (!mermaidInitialized) {
        mermaid.initialize({ startOnLoad: false })
        mermaidInitialized = true
      }
    })

    return {
      mermaidDiv,
      workflowData,
      loading,
      showApprovalDialog,
      selectedNode,
      handleCloseDialog,
      handleApprove,
      handleReject
    }
  }
}
</script>

<style scoped>
.workflow-diagram {
  width: 100%;
  height: 600px;
  border: 1px solid #ddd;
  padding: 20px;
  background-color: white;
}

:deep(.node) {
  cursor: pointer;
}

:deep(.node:hover) {
  opacity: 0.8;
}

.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
