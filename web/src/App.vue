<template>
  <el-container>
    <el-header>
      <h1>工作流程可视化</h1>
    </el-header>
    <el-main>
      <el-row :gutter="20">
        <el-col :span="6">
          <workflow-list @select-workflow="selectWorkflow" />
        </el-col>
        <el-col :span="18">
          <workflow-diagram :workflow-id="selectedWorkflowId" />
        </el-col>
      </el-row>
    </el-main>
  </el-container>
</template>

<script>
import { ref } from 'vue'
import WorkflowList from './components/WorkflowList.vue'
import WorkflowDiagram from './components/WorkflowDiagram.vue'

export default {
  name: 'App',
  components: {
    WorkflowList,
    WorkflowDiagram
  },
  setup() {
    const selectedWorkflowId = ref(null)

    const selectWorkflow = (workflowId) => {
      selectedWorkflowId.value = workflowId
    }

    return {
      selectedWorkflowId,
      selectWorkflow
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-header {
  background-color: #B3C0D1;
  color: #333;
  line-height: 60px;
}

.el-main {
  background-color: #E9EEF3;
  color: #333;
}
</style>
