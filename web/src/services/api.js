// workflowApi.js
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';  // 根据你的实际API地址进行调整

const workflowApi = {
  /**
   * 获取指定ID的工作流程数据
   * @param {number} workflowId - 工作流程ID
   * @returns {Promise<Object>} 工作流程数据
   */
  async getWorkflow(workflowId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/workflow/${workflowId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching workflow:', error);
      throw error;
    }
  },

  /**
   * 获取所有工作流程的列表
   * @returns {Promise<Array>} 工作流程列表
   */
  async getWorkflows() {
    try {
      const response = await axios.get(`${API_BASE_URL}/workflows`);
      return response.data;
    } catch (error) {
      console.error('Error fetching workflows list:', error);
      throw error;
    }
  }
};

export default workflowApi;
