{"name": "web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@vue-flow/background": "^1.3.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.41.2", "@vue-flow/minimap": "^1.5.0", "@vue-flow/node-resizer": "^1.4.0", "axios": "^1.7.7", "core-js": "^3.8.3", "element-plus": "^2.8.5", "mermaid": "^11.3.0", "vue": "^3.2.13"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-transform-class-static-block": "^7.25.8", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}